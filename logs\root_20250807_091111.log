2025-08-07 09:11:13 - training_session_manager - INFO - 初始化训练会话管理器
2025-08-07 09:11:13 - training_session_manager - INFO - 成功加载会话: 训练_nodule2_20250807_090034
2025-08-07 09:11:13 - training_session_manager - INFO - 自动激活最新会话: 训练_nodule2_20250807_090034
2025-08-07 09:11:13 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-08-07 09:11:14 - data_exploration - INFO - 数据探索器初始化完成，输出目录: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\data_exploration
2025-08-07 09:11:14 - gui_data_exploration - INFO - 数据探索GUI组件初始化完成
2025-08-07 09:11:14 - GUI - INFO - GUI界面初始化完成
2025-08-07 09:11:34 - data_preprocessing - INFO - 成功加载 数据，特征维度: (197, 8)
2025-08-07 09:11:34 - data_preprocessing - INFO - 应用了 standard 特征缩放
2025-08-07 09:11:34 - hyperparameter_tuning - INFO - 开始对 RandomForest 进行超参数调优，试验次数: 50
2025-08-07 09:11:34 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 4
2025-08-07 09:11:34 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-07 09:11:34 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-07 09:11:34 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 50, 'n_jobs': 4, 'timeout': 1800, 'callbacks': [<function tune_model.<locals>.early_stopping_callback at 0x00000236DC6F8430>]}
2025-08-07 09:11:35 - hyperparameter_tuning - INFO - Trial 1: 发现更好的得分 0.9785
2025-08-07 09:11:37 - hyperparameter_tuning - INFO - Trial 3: 发现更好的得分 0.9818
2025-08-07 09:11:37 - hyperparameter_tuning - INFO - Trial 5: 发现更好的得分 0.9834
2025-08-07 09:11:44 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-07 09:11:44 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9834
2025-08-07 09:11:45 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-07 09:11:45 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9834
2025-08-07 09:11:45 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-07 09:11:45 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9834
2025-08-07 09:11:45 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-07 09:11:45 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9834
2025-08-07 09:11:45 - hyperparameter_tuning - INFO - 模型 RandomForest 的最佳参数: {'n_estimators': 53, 'max_depth': 15, 'min_samples_split': 17, 'min_samples_leaf': 14, 'max_features': 'log2'}
2025-08-07 09:11:45 - hyperparameter_tuning - INFO - 模型 RandomForest 的最佳得分: 0.9834
2025-08-07 09:11:45 - hyperparameter_tuning - INFO - 实际执行试验次数: 17/50
2025-08-07 09:11:45 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-07 09:11:45 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-07 09:11:45 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-07 09:11:45 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\RandomForest\optimization_history_20250807_091145.html
2025-08-07 09:11:45 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-07 09:11:45 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-07 09:11:45 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\RandomForest\param_importances_20250807_091145.html
2025-08-07 09:11:45 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 11.23 秒
2025-08-07 09:11:45 - hyperparameter_tuning - INFO - 开始对 CatBoost 进行超参数调优，试验次数: 50
2025-08-07 09:11:45 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 4
2025-08-07 09:11:45 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-07 09:11:45 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-07 09:11:45 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 50, 'n_jobs': 4, 'timeout': 1800, 'callbacks': [<function tune_model.<locals>.early_stopping_callback at 0x00000236DF845EE0>]}
2025-08-07 09:11:45 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-07 09:11:45 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-07 09:11:45 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-07 09:11:45 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-07 09:11:54 - hyperparameter_tuning - INFO - Trial 0: 发现更好的得分 0.9753
2025-08-07 09:11:54 - hyperparameter_tuning - ERROR - 超参数调优过程中出错: 
All the 5 fits failed.
It is very likely that your model is misconfigured.
You can try to debug the error by setting error_score='raise'.

Below are more details about the failures:
--------------------------------------------------------------------------------
1 <USER> <GROUP> with the following error:
Traceback (most recent call last):
  File "D:\anaconda\envs\multi_model\lib\site-packages\sklearn\model_selection\_validation.py", line 866, in _fit_and_score
    estimator.fit(X_train, y_train, **fit_params)
  File "D:\anaconda\envs\multi_model\lib\site-packages\catboost\core.py", line 5245, in fit
    self._fit(X, y, cat_features, text_features, embedding_features, None, graph, sample_weight, None, None, None, None, baseline, use_best_model,
  File "D:\anaconda\envs\multi_model\lib\site-packages\catboost\core.py", line 2410, in _fit
    self._train(
  File "D:\anaconda\envs\multi_model\lib\site-packages\catboost\core.py", line 1790, in _train
    self._object._train(train_pool, test_pool, params, allow_clear_pool, init_model._object if init_model else None)
  File "_catboost.pyx", line 5017, in _catboost._CatBoost._train
  File "_catboost.pyx", line 5066, in _catboost._CatBoost._train
_catboost.CatBoostError: catboost/cuda/cuda_lib/devices_provider.h:190: Error: device already requested 0

--------------------------------------------------------------------------------
4 <USER> <GROUP> with the following error:
Traceback (most recent call last):
  File "D:\anaconda\envs\multi_model\lib\site-packages\sklearn\model_selection\_validation.py", line 866, in _fit_and_score
    estimator.fit(X_train, y_train, **fit_params)
  File "D:\anaconda\envs\multi_model\lib\site-packages\catboost\core.py", line 5245, in fit
    self._fit(X, y, cat_features, text_features, embedding_features, None, graph, sample_weight, None, None, None, None, baseline, use_best_model,
  File "D:\anaconda\envs\multi_model\lib\site-packages\catboost\core.py", line 2410, in _fit
    self._train(
  File "D:\anaconda\envs\multi_model\lib\site-packages\catboost\core.py", line 1790, in _train
    self._object._train(train_pool, test_pool, params, allow_clear_pool, init_model._object if init_model else None)
  File "_catboost.pyx", line 5017, in _catboost._CatBoost._train
  File "_catboost.pyx", line 5066, in _catboost._CatBoost._train
_catboost.CatBoostError: catboost/cuda/cuda_lib/cuda_manager.cpp:201: Condition violated: `State == nullptr'

2025-08-07 09:11:54 - hyperparameter_tuning - INFO - 开始对 KNN 进行超参数调优，试验次数: 50
2025-08-07 09:11:54 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 4
2025-08-07 09:11:54 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-07 09:11:54 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-07 09:11:54 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 50, 'n_jobs': 4, 'timeout': 1800, 'callbacks': [<function tune_model.<locals>.early_stopping_callback at 0x00000236DFAB7DC0>]}
2025-08-07 09:11:54 - hyperparameter_tuning - INFO - Trial 1: 发现更好的得分 0.9605
2025-08-07 09:11:54 - hyperparameter_tuning - INFO - Trial 0: 发现更好的得分 0.9753
2025-08-07 09:11:54 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-07 09:11:54 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9753
2025-08-07 09:11:54 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-07 09:11:54 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9753
2025-08-07 09:11:54 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-07 09:11:54 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-07 09:11:54 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9753
2025-08-07 09:11:54 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9753
2025-08-07 09:11:54 - hyperparameter_tuning - INFO - 模型 KNN 的最佳参数: {'n_neighbors': 6}
2025-08-07 09:11:54 - hyperparameter_tuning - INFO - 模型 KNN 的最佳得分: 0.9753
2025-08-07 09:11:54 - hyperparameter_tuning - INFO - 实际执行试验次数: 16/50
2025-08-07 09:11:54 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-07 09:11:54 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-07 09:11:55 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-07 09:11:55 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\KNN\optimization_history_20250807_091154.html
2025-08-07 09:11:55 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-07 09:11:55 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-07 09:11:55 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\KNN\param_importances_20250807_091155.html
2025-08-07 09:11:55 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 0.76 秒
2025-08-07 09:12:06 - data_preprocessing - INFO - 成功加载 数据，特征维度: (197, 8)
2025-08-07 09:12:06 - data_preprocessing - INFO - 应用了 standard 特征缩放
2025-08-07 09:12:06 - model_training - INFO - 模型名称: Random Forest
2025-08-07 09:12:06 - model_training - INFO - 准确率: 0.9000
2025-08-07 09:12:06 - model_training - INFO - AUC: 0.9386
2025-08-07 09:12:06 - model_training - INFO - 混淆矩阵:
2025-08-07 09:12:06 - model_training - INFO - 
[[22  1]
 [ 3 14]]
2025-08-07 09:12:06 - model_training - INFO - 
分类报告:
2025-08-07 09:12:06 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.88      0.96      0.92        23
           1       0.93      0.82      0.88        17

    accuracy                           0.90        40
   macro avg       0.91      0.89      0.90        40
weighted avg       0.90      0.90      0.90        40

2025-08-07 09:12:06 - model_training - INFO - 训练时间: 0.08 秒
2025-08-07 09:12:06 - model_training - INFO - 模型 RandomForest 性能: 准确率=0.9000
2025-08-07 09:12:06 - training_session_manager - INFO - 保存模型 RandomForest 到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250807_090034\models\RandomForest_single_091206.joblib
2025-08-07 09:12:06 - model_training - INFO - 模型 RandomForest 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250807_090034\models\RandomForest_single_091206.joblib
2025-08-07 09:12:06 - model_training - INFO - 模型 RandomForest 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\RandomForest_results.joblib
2025-08-07 09:12:07 - model_training - INFO - 模型名称: CatBoost
2025-08-07 09:12:07 - model_training - INFO - 准确率: 0.9000
2025-08-07 09:12:07 - model_training - INFO - AUC: 0.9591
2025-08-07 09:12:07 - model_training - INFO - 混淆矩阵:
2025-08-07 09:12:07 - model_training - INFO - 
[[22  1]
 [ 3 14]]
2025-08-07 09:12:07 - model_training - INFO - 
分类报告:
2025-08-07 09:12:07 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.88      0.96      0.92        23
           1       0.93      0.82      0.88        17

    accuracy                           0.90        40
   macro avg       0.91      0.89      0.90        40
weighted avg       0.90      0.90      0.90        40

2025-08-07 09:12:07 - model_training - INFO - 训练时间: 1.00 秒
2025-08-07 09:12:07 - model_training - INFO - 模型 CatBoost 性能: 准确率=0.9000
2025-08-07 09:12:07 - training_session_manager - INFO - 保存模型 CatBoost 到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250807_090034\models\CatBoost_single_091207.joblib
2025-08-07 09:12:07 - model_training - INFO - 模型 CatBoost 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250807_090034\models\CatBoost_single_091207.joblib
2025-08-07 09:12:07 - model_training - INFO - 模型 CatBoost 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\CatBoost_results.joblib
2025-08-07 09:12:07 - model_training - INFO - 模型名称: KNN
2025-08-07 09:12:07 - model_training - INFO - 准确率: 0.8750
2025-08-07 09:12:07 - model_training - INFO - AUC: 0.9322
2025-08-07 09:12:07 - model_training - INFO - 混淆矩阵:
2025-08-07 09:12:07 - model_training - INFO - 
[[21  2]
 [ 3 14]]
2025-08-07 09:12:07 - model_training - INFO - 
分类报告:
2025-08-07 09:12:07 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.88      0.91      0.89        23
           1       0.88      0.82      0.85        17

    accuracy                           0.88        40
   macro avg       0.88      0.87      0.87        40
weighted avg       0.88      0.88      0.87        40

2025-08-07 09:12:07 - model_training - INFO - 训练时间: 0.01 秒
2025-08-07 09:12:07 - model_training - INFO - 模型 KNN 性能: 准确率=0.8750
2025-08-07 09:12:07 - training_session_manager - INFO - 保存模型 KNN 到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250807_090034\models\KNN_single_091207.joblib
2025-08-07 09:12:07 - model_training - INFO - 模型 KNN 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250807_090034\models\KNN_single_091207.joblib
2025-08-07 09:12:07 - model_training - INFO - 模型 KNN 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\KNN_results.joblib
