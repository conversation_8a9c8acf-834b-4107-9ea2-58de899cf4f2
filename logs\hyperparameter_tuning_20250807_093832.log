2025-08-07 09:38:32 - hyperparameter_tuning - INFO - 开始对 DecisionTree 进行超参数调优，试验次数: 50
2025-08-07 09:38:32 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 4
2025-08-07 09:38:32 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-07 09:38:32 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-07 09:38:32 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 50, 'n_jobs': 4, 'timeout': 1800, 'callbacks': [<function tune_model.<locals>.early_stopping_callback at 0x000001DEA36265E0>]}
2025-08-07 09:38:32 - hyperparameter_tuning - INFO - Trial 1: 发现更好的得分 0.9262
2025-08-07 09:38:32 - hyperparameter_tuning - INFO - Trial 3: 发现更好的得分 0.9418
2025-08-07 09:38:32 - hyperparameter_tuning - INFO - Trial 5: 发现更好的得分 0.9505
2025-08-07 09:38:33 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-07 09:38:33 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9508
2025-08-07 09:38:33 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-07 09:38:33 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9508
2025-08-07 09:38:33 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-07 09:38:33 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9508
2025-08-07 09:38:33 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-07 09:38:33 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9508
2025-08-07 09:38:33 - hyperparameter_tuning - INFO - 模型 DecisionTree 的最佳参数: {'max_depth': 8, 'min_samples_split': 49, 'min_samples_leaf': 23, 'criterion': 'entropy', 'class_weight': 'balanced', 'max_features': None}
2025-08-07 09:38:33 - hyperparameter_tuning - INFO - 模型 DecisionTree 的最佳得分: 0.9508
2025-08-07 09:38:33 - hyperparameter_tuning - INFO - 实际执行试验次数: 18/50
2025-08-07 09:38:33 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-07 09:38:33 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-07 09:38:33 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\DecisionTree\optimization_history_20250807_093833.html
2025-08-07 09:38:33 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-07 09:38:33 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\DecisionTree\param_importances_20250807_093833.html
2025-08-07 09:38:33 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 1.04 秒
2025-08-07 09:38:33 - hyperparameter_tuning - INFO - 开始对 RandomForest 进行超参数调优，试验次数: 50
2025-08-07 09:38:33 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 4
2025-08-07 09:38:33 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-07 09:38:33 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-07 09:38:33 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 50, 'n_jobs': 4, 'timeout': 1800, 'callbacks': [<function tune_model.<locals>.early_stopping_callback at 0x000001DEA67F7E50>]}
2025-08-07 09:38:36 - hyperparameter_tuning - INFO - Trial 1: 发现更好的得分 0.9810
2025-08-07 09:38:37 - hyperparameter_tuning - INFO - Trial 0: 发现更好的得分 0.9825
2025-08-07 09:38:43 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-07 09:38:43 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9827
2025-08-07 09:38:44 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-07 09:38:44 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9827
2025-08-07 09:38:44 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-07 09:38:44 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9827
2025-08-07 09:38:44 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-07 09:38:44 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9827
2025-08-07 09:38:44 - hyperparameter_tuning - INFO - 模型 RandomForest 的最佳参数: {'n_estimators': 202, 'max_depth': 20, 'min_samples_split': 15, 'min_samples_leaf': 14, 'max_features': 'sqrt'}
2025-08-07 09:38:44 - hyperparameter_tuning - INFO - 模型 RandomForest 的最佳得分: 0.9827
2025-08-07 09:38:44 - hyperparameter_tuning - INFO - 实际执行试验次数: 15/50
2025-08-07 09:38:44 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-07 09:38:44 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-07 09:38:44 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\RandomForest\optimization_history_20250807_093844.html
2025-08-07 09:38:45 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-07 09:38:45 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\RandomForest\param_importances_20250807_093844.html
2025-08-07 09:38:45 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 11.34 秒
2025-08-07 09:38:45 - hyperparameter_tuning - INFO - 开始对 XGBoost 进行超参数调优，试验次数: 50
2025-08-07 09:38:45 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 4
2025-08-07 09:38:45 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-07 09:38:45 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-07 09:38:45 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 50, 'n_jobs': 4, 'timeout': 1800, 'callbacks': [<function tune_model.<locals>.early_stopping_callback at 0x000001DEA682D040>]}
2025-08-07 09:38:45 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-07 09:38:45 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-07 09:38:45 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-07 09:38:45 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-07 09:38:46 - hyperparameter_tuning - INFO - Trial 0: 发现更好的得分 0.9836
2025-08-07 09:38:46 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-07 09:38:46 - hyperparameter_tuning - INFO - Trial 2: 发现更好的得分 0.9853
2025-08-07 09:38:46 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-07 09:38:46 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-07 09:38:46 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-07 09:38:46 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-07 09:38:46 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-07 09:38:46 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-07 09:38:46 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-07 09:38:46 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-07 09:38:47 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-07 09:38:47 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-07 09:38:47 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-07 09:38:47 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9860
2025-08-07 09:38:47 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-07 09:38:47 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9860
2025-08-07 09:38:47 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-07 09:38:47 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9860
2025-08-07 09:38:47 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-07 09:38:47 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9860
2025-08-07 09:38:47 - hyperparameter_tuning - INFO - 模型 XGBoost 的最佳参数: {'n_estimators': 220, 'max_depth': 4, 'learning_rate': 0.2033575573412162, 'subsample': 0.8316145929182917, 'colsample_bytree': 0.5838589852135501}
2025-08-07 09:38:47 - hyperparameter_tuning - INFO - 模型 XGBoost 的最佳得分: 0.9860
2025-08-07 09:38:47 - hyperparameter_tuning - INFO - 实际执行试验次数: 15/50
2025-08-07 09:38:47 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-07 09:38:47 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-07 09:38:48 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\XGBoost\optimization_history_20250807_093847.html
2025-08-07 09:38:48 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-07 09:38:48 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\XGBoost\param_importances_20250807_093848.html
2025-08-07 09:38:48 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 3.09 秒
2025-08-07 09:38:48 - hyperparameter_tuning - INFO - 开始对 LightGBM 进行超参数调优，试验次数: 50
2025-08-07 09:38:48 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 4
2025-08-07 09:38:48 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-07 09:38:48 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-07 09:38:48 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 50, 'n_jobs': 4, 'timeout': 1800, 'callbacks': [<function tune_model.<locals>.early_stopping_callback at 0x000001DEA682DC10>]}
2025-08-07 09:38:49 - hyperparameter_tuning - INFO - Trial 0: 发现更好的得分 0.9744
2025-08-07 09:38:50 - hyperparameter_tuning - INFO - Trial 4: 发现更好的得分 0.9845
2025-08-07 09:38:50 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-07 09:38:50 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9845
2025-08-07 09:38:51 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-07 09:38:51 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9845
2025-08-07 09:38:51 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-07 09:38:51 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9845
2025-08-07 09:38:51 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-07 09:38:51 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9845
2025-08-07 09:38:51 - hyperparameter_tuning - INFO - 模型 LightGBM 的最佳参数: {'n_estimators': 88, 'max_depth': 3, 'learning_rate': 0.12595315176924943, 'feature_fraction': 0.5192324241244926, 'bagging_fraction': 0.9442482307928439}
2025-08-07 09:38:51 - hyperparameter_tuning - INFO - 模型 LightGBM 的最佳得分: 0.9845
2025-08-07 09:38:51 - hyperparameter_tuning - INFO - 实际执行试验次数: 15/50
2025-08-07 09:38:51 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-07 09:38:51 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-07 09:38:51 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\LightGBM\optimization_history_20250807_093851.html
2025-08-07 09:38:51 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-07 09:38:51 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\LightGBM\param_importances_20250807_093851.html
2025-08-07 09:38:51 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 3.30 秒
2025-08-07 09:38:51 - hyperparameter_tuning - INFO - 开始对 CatBoost 进行超参数调优，试验次数: 50
2025-08-07 09:38:51 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 4
2025-08-07 09:38:51 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-07 09:38:51 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-07 09:38:51 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 50, 'n_jobs': 4, 'timeout': 1800, 'callbacks': [<function tune_model.<locals>.early_stopping_callback at 0x000001DEA682D040>]}
2025-08-07 09:38:51 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-07 09:38:51 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-07 09:38:51 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-07 09:38:51 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-07 09:38:54 - hyperparameter_tuning - INFO - Trial 0: 发现更好的得分 0.9771
2025-08-07 09:38:54 - hyperparameter_tuning - ERROR - 超参数调优过程中出错: 
All the 5 fits failed.
It is very likely that your model is misconfigured.
You can try to debug the error by setting error_score='raise'.

Below are more details about the failures:
--------------------------------------------------------------------------------
1 <USER> <GROUP> with the following error:
Traceback (most recent call last):
  File "D:\anaconda\envs\multi_model\lib\site-packages\sklearn\model_selection\_validation.py", line 866, in _fit_and_score
    estimator.fit(X_train, y_train, **fit_params)
  File "D:\anaconda\envs\multi_model\lib\site-packages\catboost\core.py", line 5245, in fit
    self._fit(X, y, cat_features, text_features, embedding_features, None, graph, sample_weight, None, None, None, None, baseline, use_best_model,
  File "D:\anaconda\envs\multi_model\lib\site-packages\catboost\core.py", line 2410, in _fit
    self._train(
  File "D:\anaconda\envs\multi_model\lib\site-packages\catboost\core.py", line 1790, in _train
    self._object._train(train_pool, test_pool, params, allow_clear_pool, init_model._object if init_model else None)
  File "_catboost.pyx", line 5017, in _catboost._CatBoost._train
  File "_catboost.pyx", line 5066, in _catboost._CatBoost._train
_catboost.CatBoostError: catboost/cuda/cuda_lib/devices_provider.h:190: Error: device already requested 0

--------------------------------------------------------------------------------
4 <USER> <GROUP> with the following error:
Traceback (most recent call last):
  File "D:\anaconda\envs\multi_model\lib\site-packages\sklearn\model_selection\_validation.py", line 866, in _fit_and_score
    estimator.fit(X_train, y_train, **fit_params)
  File "D:\anaconda\envs\multi_model\lib\site-packages\catboost\core.py", line 5245, in fit
    self._fit(X, y, cat_features, text_features, embedding_features, None, graph, sample_weight, None, None, None, None, baseline, use_best_model,
  File "D:\anaconda\envs\multi_model\lib\site-packages\catboost\core.py", line 2410, in _fit
    self._train(
  File "D:\anaconda\envs\multi_model\lib\site-packages\catboost\core.py", line 1790, in _train
    self._object._train(train_pool, test_pool, params, allow_clear_pool, init_model._object if init_model else None)
  File "_catboost.pyx", line 5017, in _catboost._CatBoost._train
  File "_catboost.pyx", line 5066, in _catboost._CatBoost._train
_catboost.CatBoostError: catboost/cuda/cuda_lib/cuda_manager.cpp:201: Condition violated: `State == nullptr'

2025-08-07 09:38:54 - hyperparameter_tuning - INFO - 开始对 Logistic 进行超参数调优，试验次数: 50
2025-08-07 09:38:54 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 4
2025-08-07 09:38:54 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-07 09:38:54 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-07 09:38:54 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 50, 'n_jobs': 4, 'timeout': 1800, 'callbacks': [<function tune_model.<locals>.early_stopping_callback at 0x000001DEA7CFE820>]}
2025-08-07 09:38:54 - hyperparameter_tuning - INFO - Trial 0: 发现更好的得分 0.9664
2025-08-07 09:38:54 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-07 09:38:54 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-07 09:38:54 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9672
2025-08-07 09:38:54 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9672
2025-08-07 09:38:55 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-07 09:38:55 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9672
2025-08-07 09:38:55 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-07 09:38:55 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9672
2025-08-07 09:38:55 - hyperparameter_tuning - INFO - 模型 Logistic 的最佳参数: {'C': 5.2514855664547895, 'solver': 'liblinear'}
2025-08-07 09:38:55 - hyperparameter_tuning - INFO - 模型 Logistic 的最佳得分: 0.9672
2025-08-07 09:38:55 - hyperparameter_tuning - INFO - 实际执行试验次数: 14/50
2025-08-07 09:38:55 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-07 09:38:55 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-07 09:38:55 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\Logistic\optimization_history_20250807_093855.html
2025-08-07 09:38:55 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-07 09:38:55 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\Logistic\param_importances_20250807_093855.html
2025-08-07 09:38:55 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 0.76 秒
2025-08-07 09:38:55 - hyperparameter_tuning - INFO - 开始对 SVM 进行超参数调优，试验次数: 50
2025-08-07 09:38:55 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 4
2025-08-07 09:38:55 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-07 09:38:55 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-07 09:38:55 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 50, 'n_jobs': 4, 'timeout': 1800, 'callbacks': [<function tune_model.<locals>.early_stopping_callback at 0x000001DEA693AD30>]}
2025-08-07 09:38:55 - hyperparameter_tuning - INFO - Trial 0: 发现更好的得分 0.9887
2025-08-07 09:38:55 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-07 09:38:55 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9895
2025-08-07 09:38:55 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-07 09:38:55 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9895
2025-08-07 09:38:55 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-07 09:38:55 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9895
2025-08-07 09:38:55 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-07 09:38:55 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9895
2025-08-07 09:38:55 - hyperparameter_tuning - INFO - 模型 SVM 的最佳参数: {'C': 5.932786617483413, 'kernel': 'rbf'}
2025-08-07 09:38:55 - hyperparameter_tuning - INFO - 模型 SVM 的最佳得分: 0.9895
2025-08-07 09:38:55 - hyperparameter_tuning - INFO - 实际执行试验次数: 14/50
2025-08-07 09:38:55 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-07 09:38:55 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-07 09:38:55 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\SVM\optimization_history_20250807_093855.html
2025-08-07 09:38:56 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-07 09:38:56 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\SVM\param_importances_20250807_093856.html
2025-08-07 09:38:56 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 0.73 秒
2025-08-07 09:38:56 - hyperparameter_tuning - INFO - 开始对 KNN 进行超参数调优，试验次数: 50
2025-08-07 09:38:56 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 4
2025-08-07 09:38:56 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-07 09:38:56 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-07 09:38:56 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 50, 'n_jobs': 4, 'timeout': 1800, 'callbacks': [<function tune_model.<locals>.early_stopping_callback at 0x000001DEA693AAF0>]}
2025-08-07 09:38:56 - hyperparameter_tuning - INFO - Trial 3: 发现更好的得分 0.9605
2025-08-07 09:38:56 - hyperparameter_tuning - INFO - Trial 1: 发现更好的得分 0.9744
2025-08-07 09:38:56 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-07 09:38:56 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-07 09:38:56 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9744
2025-08-07 09:38:56 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9744
2025-08-07 09:38:56 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-07 09:38:56 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9744
2025-08-07 09:38:56 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-07 09:38:56 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9744
2025-08-07 09:38:56 - hyperparameter_tuning - INFO - 模型 KNN 的最佳参数: {'n_neighbors': 8}
2025-08-07 09:38:56 - hyperparameter_tuning - INFO - 模型 KNN 的最佳得分: 0.9744
2025-08-07 09:38:56 - hyperparameter_tuning - INFO - 实际执行试验次数: 15/50
2025-08-07 09:38:56 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-07 09:38:56 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-07 09:38:56 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\KNN\optimization_history_20250807_093856.html
2025-08-07 09:38:57 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-07 09:38:57 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\KNN\param_importances_20250807_093856.html
2025-08-07 09:38:57 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 0.79 秒
2025-08-07 09:38:57 - hyperparameter_tuning - INFO - 开始对 NaiveBayes 进行超参数调优，试验次数: 50
2025-08-07 09:38:57 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 4
2025-08-07 09:38:57 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-07 09:38:57 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-07 09:38:57 - hyperparameter_tuning - INFO - NaiveBayes模型无需调参
2025-08-07 09:38:57 - hyperparameter_tuning - INFO - 开始对 NeuralNet 进行超参数调优，试验次数: 50
2025-08-07 09:38:57 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 4
2025-08-07 09:38:57 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-07 09:38:57 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-07 09:38:57 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 50, 'n_jobs': 4, 'timeout': 1800, 'callbacks': [<function tune_model.<locals>.early_stopping_callback at 0x000001DEA6710670>]}
2025-08-07 09:38:59 - hyperparameter_tuning - INFO - Trial 2: 发现更好的得分 0.9806
2025-08-07 09:39:08 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-07 09:39:08 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9815
2025-08-07 09:39:08 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-07 09:39:08 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9815
2025-08-07 09:39:09 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-07 09:39:09 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9815
2025-08-07 09:39:09 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-07 09:39:09 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9815
2025-08-07 09:39:09 - hyperparameter_tuning - INFO - 模型 NeuralNet 的最佳参数: {'hidden_layer_sizes': (50, 50), 'alpha': 0.009037215216917902}
2025-08-07 09:39:09 - hyperparameter_tuning - INFO - 模型 NeuralNet 的最佳得分: 0.9815
2025-08-07 09:39:09 - hyperparameter_tuning - INFO - 实际执行试验次数: 14/50
2025-08-07 09:39:09 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-07 09:39:09 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-07 09:39:09 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\NeuralNet\optimization_history_20250807_093909.html
2025-08-07 09:39:09 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-07 09:39:09 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\NeuralNet\param_importances_20250807_093909.html
2025-08-07 09:39:09 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 12.73 秒
