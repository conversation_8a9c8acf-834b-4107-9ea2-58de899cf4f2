2025-08-07 08:24:59 - training_session_manager - INFO - 初始化训练会话管理器
2025-08-07 08:24:59 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-08-07 08:24:59 - data_exploration - INFO - 数据探索器初始化完成，输出目录: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\data_exploration
2025-08-07 08:24:59 - gui_data_exploration - INFO - 数据探索GUI组件初始化完成
2025-08-07 08:25:00 - GUI - INFO - GUI界面初始化完成
2025-08-07 08:26:40 - enhanced_ensemble_selector - INFO - 开始评估 10 个基模型...
2025-08-07 08:26:40 - enhanced_ensemble_selector - INFO -   评估模型: DecisionTree
2025-08-07 08:26:40 - model_training - INFO - 模型名称: Decision Tree
2025-08-07 08:26:40 - model_training - INFO - 准确率: 0.8000
2025-08-07 08:26:40 - model_training - INFO - AUC: 0.8951
2025-08-07 08:26:40 - model_training - INFO - 混淆矩阵:
2025-08-07 08:26:40 - model_training - INFO - 
[[20  3]
 [ 5 12]]
2025-08-07 08:26:40 - model_training - INFO - 
分类报告:
2025-08-07 08:26:40 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.80      0.87      0.83        23
           1       0.80      0.71      0.75        17

    accuracy                           0.80        40
   macro avg       0.80      0.79      0.79        40
weighted avg       0.80      0.80      0.80        40

2025-08-07 08:26:40 - model_training - INFO - 训练时间: 0.01 秒
2025-08-07 08:26:40 - model_training - INFO - 模型 DecisionTree 性能: 准确率=0.8000
2025-08-07 08:26:40 - model_training - INFO - 模型 DecisionTree 的结果已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\DecisionTree_results.joblib
2025-08-07 08:26:40 - model_training - INFO - 特征名称已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\DecisionTree_feature_names.joblib
2025-08-07 08:26:40 - enhanced_ensemble_selector - INFO -     DecisionTree - 性能得分: 0.8233
2025-08-07 08:26:40 - enhanced_ensemble_selector - INFO -   评估模型: RandomForest
2025-08-07 08:26:40 - model_training - INFO - 模型名称: Random Forest
2025-08-07 08:26:40 - model_training - INFO - 准确率: 0.9000
2025-08-07 08:26:40 - model_training - INFO - AUC: 0.9386
2025-08-07 08:26:40 - model_training - INFO - 混淆矩阵:
2025-08-07 08:26:40 - model_training - INFO - 
[[22  1]
 [ 3 14]]
2025-08-07 08:26:40 - model_training - INFO - 
分类报告:
2025-08-07 08:26:40 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.88      0.96      0.92        23
           1       0.93      0.82      0.88        17

    accuracy                           0.90        40
   macro avg       0.91      0.89      0.90        40
weighted avg       0.90      0.90      0.90        40

2025-08-07 08:26:40 - model_training - INFO - 训练时间: 0.08 秒
2025-08-07 08:26:40 - model_training - INFO - 模型 RandomForest 性能: 准确率=0.9000
2025-08-07 08:26:40 - model_training - INFO - 模型 RandomForest 的结果已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\RandomForest_results.joblib
2025-08-07 08:26:40 - model_training - INFO - 特征名称已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\RandomForest_feature_names.joblib
2025-08-07 08:26:40 - enhanced_ensemble_selector - INFO -     RandomForest - 性能得分: 0.9097
2025-08-07 08:26:40 - enhanced_ensemble_selector - INFO -   评估模型: XGBoost
2025-08-07 08:26:40 - model_training - INFO - 模型名称: XGBoost
2025-08-07 08:26:40 - model_training - INFO - 准确率: 0.8750
2025-08-07 08:26:40 - model_training - INFO - AUC: 0.9668
2025-08-07 08:26:40 - model_training - INFO - 混淆矩阵:
2025-08-07 08:26:40 - model_training - INFO - 
[[21  2]
 [ 3 14]]
2025-08-07 08:26:40 - model_training - INFO - 
分类报告:
2025-08-07 08:26:40 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.88      0.91      0.89        23
           1       0.88      0.82      0.85        17

    accuracy                           0.88        40
   macro avg       0.88      0.87      0.87        40
weighted avg       0.88      0.88      0.87        40

2025-08-07 08:26:40 - model_training - INFO - 训练时间: 0.04 秒
2025-08-07 08:26:40 - model_training - INFO - 模型 XGBoost 性能: 准确率=0.8750
2025-08-07 08:26:40 - model_training - INFO - 模型 XGBoost 的结果已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\XGBoost_results.joblib
2025-08-07 08:26:40 - model_training - INFO - 特征名称已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\XGBoost_feature_names.joblib
2025-08-07 08:26:40 - enhanced_ensemble_selector - INFO -     XGBoost - 性能得分: 0.8978
2025-08-07 08:26:40 - enhanced_ensemble_selector - INFO -   评估模型: LightGBM
2025-08-07 08:26:41 - model_training - INFO - 模型名称: LightGBM
2025-08-07 08:26:41 - model_training - INFO - 准确率: 0.8750
2025-08-07 08:26:41 - model_training - INFO - AUC: 0.9463
2025-08-07 08:26:41 - model_training - INFO - 混淆矩阵:
2025-08-07 08:26:41 - model_training - INFO - 
[[21  2]
 [ 3 14]]
2025-08-07 08:26:41 - model_training - INFO - 
分类报告:
2025-08-07 08:26:41 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.88      0.91      0.89        23
           1       0.88      0.82      0.85        17

    accuracy                           0.88        40
   macro avg       0.88      0.87      0.87        40
weighted avg       0.88      0.88      0.87        40

2025-08-07 08:26:41 - model_training - INFO - 训练时间: 1.36 秒
2025-08-07 08:26:41 - model_training - INFO - 模型 LightGBM 性能: 准确率=0.8750
2025-08-07 08:26:41 - model_training - INFO - 模型 LightGBM 的结果已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\LightGBM_results.joblib
2025-08-07 08:26:41 - model_training - INFO - 特征名称已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\LightGBM_feature_names.joblib
2025-08-07 08:26:41 - enhanced_ensemble_selector - INFO -     LightGBM - 性能得分: 0.8927
2025-08-07 08:26:41 - enhanced_ensemble_selector - INFO -   评估模型: CatBoost
2025-08-07 08:26:43 - model_training - INFO - 模型名称: CatBoost
2025-08-07 08:26:43 - model_training - INFO - 准确率: 0.9000
2025-08-07 08:26:43 - model_training - INFO - AUC: 0.9591
2025-08-07 08:26:43 - model_training - INFO - 混淆矩阵:
2025-08-07 08:26:43 - model_training - INFO - 
[[22  1]
 [ 3 14]]
2025-08-07 08:26:43 - model_training - INFO - 
分类报告:
2025-08-07 08:26:43 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.88      0.96      0.92        23
           1       0.93      0.82      0.88        17

    accuracy                           0.90        40
   macro avg       0.91      0.89      0.90        40
weighted avg       0.90      0.90      0.90        40

2025-08-07 08:26:43 - model_training - INFO - 训练时间: 1.15 秒
2025-08-07 08:26:43 - model_training - INFO - 模型 CatBoost 性能: 准确率=0.9000
2025-08-07 08:26:43 - model_training - INFO - 模型 CatBoost 的结果已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\CatBoost_results.joblib
2025-08-07 08:26:43 - model_training - INFO - 特征名称已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\CatBoost_feature_names.joblib
2025-08-07 08:26:43 - enhanced_ensemble_selector - INFO -     CatBoost - 性能得分: 0.9148
2025-08-07 08:26:43 - enhanced_ensemble_selector - INFO -   评估模型: Logistic
2025-08-07 08:26:43 - model_training - INFO - 模型名称: Logistic Regression
2025-08-07 08:26:43 - model_training - INFO - 准确率: 0.8500
2025-08-07 08:26:43 - model_training - INFO - AUC: 0.9284
2025-08-07 08:26:43 - model_training - INFO - 混淆矩阵:
2025-08-07 08:26:43 - model_training - INFO - 
[[21  2]
 [ 4 13]]
2025-08-07 08:26:43 - model_training - INFO - 
分类报告:
2025-08-07 08:26:43 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.84      0.91      0.88        23
           1       0.87      0.76      0.81        17

    accuracy                           0.85        40
   macro avg       0.85      0.84      0.84        40
weighted avg       0.85      0.85      0.85        40

2025-08-07 08:26:43 - model_training - INFO - 训练时间: 0.01 秒
2025-08-07 08:26:43 - model_training - INFO - 模型 Logistic 性能: 准确率=0.8500
2025-08-07 08:26:43 - model_training - INFO - 模型 Logistic 的结果已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\Logistic_results.joblib
2025-08-07 08:26:43 - model_training - INFO - 特征名称已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\Logistic_feature_names.joblib
2025-08-07 08:26:43 - enhanced_ensemble_selector - INFO -     Logistic - 性能得分: 0.8694
2025-08-07 08:26:43 - enhanced_ensemble_selector - INFO -   评估模型: SVM
2025-08-07 08:26:43 - model_training - INFO - 模型名称: SVM
2025-08-07 08:26:43 - model_training - INFO - 准确率: 0.8000
2025-08-07 08:26:43 - model_training - INFO - AUC: 0.9207
2025-08-07 08:26:43 - model_training - INFO - 混淆矩阵:
2025-08-07 08:26:43 - model_training - INFO - 
[[21  2]
 [ 6 11]]
2025-08-07 08:26:43 - model_training - INFO - 
分类报告:
2025-08-07 08:26:43 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.78      0.91      0.84        23
           1       0.85      0.65      0.73        17

    accuracy                           0.80        40
   macro avg       0.81      0.78      0.79        40
weighted avg       0.81      0.80      0.79        40

2025-08-07 08:26:43 - model_training - INFO - 训练时间: 0.01 秒
2025-08-07 08:26:43 - model_training - INFO - 模型 SVM 性能: 准确率=0.8000
2025-08-07 08:26:43 - model_training - INFO - 模型 SVM 的结果已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\SVM_results.joblib
2025-08-07 08:26:43 - model_training - INFO - 特征名称已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\SVM_feature_names.joblib
2025-08-07 08:26:43 - enhanced_ensemble_selector - INFO -     SVM - 性能得分: 0.8297
2025-08-07 08:26:43 - enhanced_ensemble_selector - INFO -   评估模型: KNN
2025-08-07 08:26:43 - model_training - INFO - 模型名称: KNN
2025-08-07 08:26:43 - model_training - INFO - 准确率: 0.8750
2025-08-07 08:26:43 - model_training - INFO - AUC: 0.9322
2025-08-07 08:26:43 - model_training - INFO - 混淆矩阵:
2025-08-07 08:26:43 - model_training - INFO - 
[[21  2]
 [ 3 14]]
2025-08-07 08:26:43 - model_training - INFO - 
分类报告:
2025-08-07 08:26:43 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.88      0.91      0.89        23
           1       0.88      0.82      0.85        17

    accuracy                           0.88        40
   macro avg       0.88      0.87      0.87        40
weighted avg       0.88      0.88      0.87        40

2025-08-07 08:26:43 - model_training - INFO - 训练时间: 0.01 秒
2025-08-07 08:26:43 - model_training - INFO - 模型 KNN 性能: 准确率=0.8750
2025-08-07 08:26:43 - model_training - INFO - 模型 KNN 的结果已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\KNN_results.joblib
2025-08-07 08:26:43 - model_training - INFO - 特征名称已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\KNN_feature_names.joblib
2025-08-07 08:26:43 - enhanced_ensemble_selector - INFO -     KNN - 性能得分: 0.8892
2025-08-07 08:26:43 - enhanced_ensemble_selector - INFO -   评估模型: NaiveBayes
2025-08-07 08:26:43 - model_training - INFO - 模型名称: Naive Bayes
2025-08-07 08:26:43 - model_training - INFO - 准确率: 0.8750
2025-08-07 08:26:43 - model_training - INFO - AUC: 0.8977
2025-08-07 08:26:43 - model_training - INFO - 混淆矩阵:
2025-08-07 08:26:43 - model_training - INFO - 
[[22  1]
 [ 4 13]]
2025-08-07 08:26:43 - model_training - INFO - 
分类报告:
2025-08-07 08:26:43 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.85      0.96      0.90        23
           1       0.93      0.76      0.84        17

    accuracy                           0.88        40
   macro avg       0.89      0.86      0.87        40
weighted avg       0.88      0.88      0.87        40

2025-08-07 08:26:43 - model_training - INFO - 训练时间: 0.01 秒
2025-08-07 08:26:43 - model_training - INFO - 模型 NaiveBayes 性能: 准确率=0.8750
2025-08-07 08:26:43 - model_training - INFO - 模型 NaiveBayes 的结果已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\NaiveBayes_results.joblib
2025-08-07 08:26:43 - model_training - INFO - 特征名称已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\NaiveBayes_feature_names.joblib
2025-08-07 08:26:43 - enhanced_ensemble_selector - INFO -     NaiveBayes - 性能得分: 0.8809
2025-08-07 08:26:43 - enhanced_ensemble_selector - INFO -   评估模型: NeuralNet
2025-08-07 08:26:43 - model_training - INFO - 模型名称: Neural Network
2025-08-07 08:26:43 - model_training - INFO - 准确率: 0.8750
2025-08-07 08:26:43 - model_training - INFO - AUC: 0.9591
2025-08-07 08:26:43 - model_training - INFO - 混淆矩阵:
2025-08-07 08:26:43 - model_training - INFO - 
[[21  2]
 [ 3 14]]
2025-08-07 08:26:43 - model_training - INFO - 
分类报告:
2025-08-07 08:26:43 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.88      0.91      0.89        23
           1       0.88      0.82      0.85        17

    accuracy                           0.88        40
   macro avg       0.88      0.87      0.87        40
weighted avg       0.88      0.88      0.87        40

2025-08-07 08:26:43 - model_training - INFO - 训练时间: 0.47 秒
2025-08-07 08:26:43 - model_training - INFO - 模型 NeuralNet 性能: 准确率=0.8750
2025-08-07 08:26:43 - model_training - INFO - 模型 NeuralNet 的结果已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\NeuralNet_results.joblib
2025-08-07 08:26:43 - model_training - INFO - 特征名称已缓存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\NeuralNet_feature_names.joblib
2025-08-07 08:26:43 - enhanced_ensemble_selector - INFO -     NeuralNet - 性能得分: 0.8959
2025-08-07 08:26:43 - improved_diversity_analyzer - INFO - 开始综合多样性分析...
2025-08-07 08:26:43 - improved_diversity_analyzer - INFO - 数据复杂度得分: 0.246
2025-08-07 08:26:43 - improved_diversity_analyzer - INFO - 调整后相关性阈值: 0.600
2025-08-07 08:26:43 - improved_diversity_analyzer - INFO - 综合多样性得分: 0.374
2025-08-07 08:26:43 - enhanced_ensemble_selector - INFO - 使用改进的多样性分析完成
2025-08-07 08:26:43 - enhanced_ensemble_selector - INFO - 数据复杂度: 0.246
2025-08-07 08:26:43 - enhanced_ensemble_selector - INFO - 调整后相关性阈值: 0.600
2025-08-07 08:26:43 - enhanced_ensemble_selector - INFO - 综合多样性得分: 0.374
2025-08-07 08:26:43 - enhanced_ensemble_selector - INFO -   DecisionTree vs RandomForest: 相关性=0.806, 多样性=0.194
2025-08-07 08:26:43 - enhanced_ensemble_selector - INFO -   DecisionTree vs XGBoost: 相关性=0.843, 多样性=0.157
2025-08-07 08:26:43 - enhanced_ensemble_selector - INFO -   DecisionTree vs LightGBM: 相关性=0.784, 多样性=0.216
2025-08-07 08:26:43 - enhanced_ensemble_selector - INFO -   DecisionTree vs CatBoost: 相关性=0.803, 多样性=0.197
2025-08-07 08:26:43 - enhanced_ensemble_selector - INFO -   DecisionTree vs Logistic: 相关性=0.673, 多样性=0.327
2025-08-07 08:26:43 - enhanced_ensemble_selector - INFO -   DecisionTree vs SVM: 相关性=0.699, 多样性=0.301
2025-08-07 08:26:43 - enhanced_ensemble_selector - INFO -   DecisionTree vs KNN: 相关性=0.696, 多样性=0.304
2025-08-07 08:26:43 - enhanced_ensemble_selector - INFO -   DecisionTree vs NaiveBayes: 相关性=0.659, 多样性=0.341
2025-08-07 08:26:43 - enhanced_ensemble_selector - INFO -   DecisionTree vs NeuralNet: 相关性=0.643, 多样性=0.357
2025-08-07 08:26:43 - enhanced_ensemble_selector - INFO -   RandomForest vs XGBoost: 相关性=0.955, 多样性=0.045
2025-08-07 08:26:43 - enhanced_ensemble_selector - INFO -   RandomForest vs LightGBM: 相关性=0.950, 多样性=0.050
2025-08-07 08:26:43 - enhanced_ensemble_selector - INFO -   RandomForest vs CatBoost: 相关性=0.986, 多样性=0.014
2025-08-07 08:26:43 - enhanced_ensemble_selector - INFO -   RandomForest vs Logistic: 相关性=0.887, 多样性=0.113
2025-08-07 08:26:43 - enhanced_ensemble_selector - INFO -   RandomForest vs SVM: 相关性=0.936, 多样性=0.064
2025-08-07 08:26:43 - enhanced_ensemble_selector - INFO -   RandomForest vs KNN: 相关性=0.912, 多样性=0.088
2025-08-07 08:26:43 - enhanced_ensemble_selector - INFO -   RandomForest vs NaiveBayes: 相关性=0.861, 多样性=0.139
2025-08-07 08:26:43 - enhanced_ensemble_selector - INFO -   RandomForest vs NeuralNet: 相关性=0.886, 多样性=0.114
2025-08-07 08:26:43 - enhanced_ensemble_selector - INFO -   XGBoost vs LightGBM: 相关性=0.971, 多样性=0.029
2025-08-07 08:26:43 - enhanced_ensemble_selector - INFO -   XGBoost vs CatBoost: 相关性=0.981, 多样性=0.019
2025-08-07 08:26:43 - enhanced_ensemble_selector - INFO -   XGBoost vs Logistic: 相关性=0.814, 多样性=0.186
2025-08-07 08:26:43 - enhanced_ensemble_selector - INFO -   XGBoost vs SVM: 相关性=0.888, 多样性=0.112
2025-08-07 08:26:43 - enhanced_ensemble_selector - INFO -   XGBoost vs KNN: 相关性=0.857, 多样性=0.143
2025-08-07 08:26:43 - enhanced_ensemble_selector - INFO -   XGBoost vs NaiveBayes: 相关性=0.822, 多样性=0.178
2025-08-07 08:26:43 - enhanced_ensemble_selector - INFO -   XGBoost vs NeuralNet: 相关性=0.838, 多样性=0.162
2025-08-07 08:26:43 - enhanced_ensemble_selector - INFO -   LightGBM vs CatBoost: 相关性=0.981, 多样性=0.019
2025-08-07 08:26:43 - enhanced_ensemble_selector - INFO -   LightGBM vs Logistic: 相关性=0.819, 多样性=0.181
2025-08-07 08:26:43 - enhanced_ensemble_selector - INFO -   LightGBM vs SVM: 相关性=0.895, 多样性=0.105
2025-08-07 08:26:43 - enhanced_ensemble_selector - INFO -   LightGBM vs KNN: 相关性=0.876, 多样性=0.124
2025-08-07 08:26:43 - enhanced_ensemble_selector - INFO -   LightGBM vs NaiveBayes: 相关性=0.797, 多样性=0.203
2025-08-07 08:26:43 - enhanced_ensemble_selector - INFO -   LightGBM vs NeuralNet: 相关性=0.882, 多样性=0.118
2025-08-07 08:26:43 - enhanced_ensemble_selector - INFO -   CatBoost vs Logistic: 相关性=0.865, 多样性=0.135
2025-08-07 08:26:43 - enhanced_ensemble_selector - INFO -   CatBoost vs SVM: 相关性=0.924, 多样性=0.076
2025-08-07 08:26:43 - enhanced_ensemble_selector - INFO -   CatBoost vs KNN: 相关性=0.907, 多样性=0.093
2025-08-07 08:26:43 - enhanced_ensemble_selector - INFO -   CatBoost vs NaiveBayes: 相关性=0.843, 多样性=0.157
2025-08-07 08:26:43 - enhanced_ensemble_selector - INFO -   CatBoost vs NeuralNet: 相关性=0.892, 多样性=0.108
2025-08-07 08:26:43 - enhanced_ensemble_selector - INFO -   Logistic vs SVM: 相关性=0.899, 多样性=0.101
2025-08-07 08:26:43 - enhanced_ensemble_selector - INFO -   Logistic vs KNN: 相关性=0.963, 多样性=0.037
2025-08-07 08:26:43 - enhanced_ensemble_selector - INFO -   Logistic vs NaiveBayes: 相关性=0.954, 多样性=0.046
2025-08-07 08:26:43 - enhanced_ensemble_selector - INFO -   Logistic vs NeuralNet: 相关性=0.936, 多样性=0.064
2025-08-07 08:26:43 - enhanced_ensemble_selector - INFO -   SVM vs KNN: 相关性=0.924, 多样性=0.076
2025-08-07 08:26:43 - enhanced_ensemble_selector - INFO -   SVM vs NaiveBayes: 相关性=0.874, 多样性=0.126
2025-08-07 08:26:43 - enhanced_ensemble_selector - INFO -   SVM vs NeuralNet: 相关性=0.894, 多样性=0.106
2025-08-07 08:26:43 - enhanced_ensemble_selector - INFO -   KNN vs NaiveBayes: 相关性=0.910, 多样性=0.090
2025-08-07 08:26:43 - enhanced_ensemble_selector - INFO -   KNN vs NeuralNet: 相关性=0.939, 多样性=0.061
2025-08-07 08:26:43 - enhanced_ensemble_selector - INFO -   NaiveBayes vs NeuralNet: 相关性=0.897, 多样性=0.103
2025-08-07 08:26:43 - enhanced_ensemble_selector - INFO - 特征重要性多样性: 0.359
2025-08-07 08:26:43 - enhanced_ensemble_selector - INFO - 算法类型多样性: 1.000
2025-08-07 08:26:43 - enhanced_ensemble_selector - INFO - 算法类型分布: {'tree': 5, 'linear': 2, 'instance': 1, 'probabilistic': 1, 'neural': 1}
2025-08-07 08:26:43 - enhanced_ensemble_selector - INFO - 开始量化多样性评估...
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO - 开始计算 10 个模型间的量化多样性指标...
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -   DecisionTree_vs_RandomForest:
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -     Q统计量: 0.898 (多样性: 0.102)
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -     不一致性: 0.150
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -     双错度量: 0.075 (多样性: 0.925)
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -     相关系数: 0.680 (多样性: 0.320)
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -     综合多样性: 0.325
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -   DecisionTree_vs_XGBoost:
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -     Q统计量: 0.938 (多样性: 0.062)
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -     不一致性: 0.125
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -     相关系数: 0.738 (多样性: 0.262)
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -     综合多样性: 0.289
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -   DecisionTree_vs_LightGBM:
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -     Q统计量: 0.800 (多样性: 0.200)
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -     不一致性: 0.175
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -     双错度量: 0.075 (多样性: 0.925)
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -     相关系数: 0.632 (多样性: 0.368)
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -     综合多样性: 0.371
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -   DecisionTree_vs_CatBoost:
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -     Q统计量: 0.898 (多样性: 0.102)
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -     不一致性: 0.150
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -     双错度量: 0.075 (多样性: 0.925)
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -     相关系数: 0.680 (多样性: 0.320)
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -     综合多样性: 0.325
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -   DecisionTree_vs_Logistic:
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -     Q统计量: 0.706 (多样性: 0.294)
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -     不一致性: 0.200
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -     双错度量: 0.075 (多样性: 0.925)
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -     相关系数: 0.573 (多样性: 0.427)
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -     综合多样性: 0.419
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -   DecisionTree_vs_SVM:
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -     Q统计量: 0.750 (多样性: 0.250)
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -     不一致性: 0.200
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -     相关系数: 0.565 (多样性: 0.435)
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -     综合多样性: 0.402
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -   DecisionTree_vs_KNN:
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -     Q统计量: 0.000 (多样性: 1.000)
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -     不一致性: 0.275
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -     双错度量: 0.025 (多样性: 0.975)
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -     相关系数: 0.422 (多样性: 0.578)
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -     综合多样性: 0.693
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -   DecisionTree_vs_NaiveBayes:
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -     Q统计量: 0.800 (多样性: 0.200)
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -     不一致性: 0.175
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -     双错度量: 0.075 (多样性: 0.925)
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -     相关系数: 0.623 (多样性: 0.377)
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -     综合多样性: 0.373
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -   DecisionTree_vs_NeuralNet:
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -     Q统计量: 0.526 (多样性: 0.474)
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -     不一致性: 0.225
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -     双错度量: 0.050 (多样性: 0.950)
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -     相关系数: 0.527 (多样性: 0.473)
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -     综合多样性: 0.494
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -   RandomForest_vs_XGBoost:
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -     Q统计量: 1.000 (多样性: 0.000)
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -     不一致性: 0.025
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -     相关系数: 0.949 (多样性: 0.051)
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -     综合多样性: 0.198
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -   RandomForest_vs_LightGBM:
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -     Q统计量: 0.962 (多样性: 0.038)
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -     不一致性: 0.075
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -     双错度量: 0.075 (多样性: 0.925)
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -     相关系数: 0.843 (多样性: 0.157)
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -     综合多样性: 0.250
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -   RandomForest_vs_CatBoost:
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -     Q统计量: 0.981 (多样性: 0.019)
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -     不一致性: 0.050
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -     双错度量: 0.075 (多样性: 0.925)
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -     相关系数: 0.893 (多样性: 0.107)
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -     综合多样性: 0.227
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -   RandomForest_vs_Logistic:
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -     Q统计量: 0.941 (多样性: 0.059)
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -     不一致性: 0.100
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -     双错度量: 0.075 (多样性: 0.925)
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -     相关系数: 0.787 (多样性: 0.213)
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -     综合多样性: 0.275
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -   RandomForest_vs_SVM:
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -     Q统计量: 1.000 (多样性: 0.000)
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -     不一致性: 0.100
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -     相关系数: 0.786 (多样性: 0.214)
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -     综合多样性: 0.253
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -   RandomForest_vs_KNN:
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -     Q统计量: 0.833 (多样性: 0.167)
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -     不一致性: 0.125
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -     双错度量: 0.050 (多样性: 0.950)
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -     相关系数: 0.738 (多样性: 0.262)
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -     综合多样性: 0.330
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -   RandomForest_vs_NaiveBayes:
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -     Q统计量: 0.962 (多样性: 0.038)
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -     不一致性: 0.075
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -     双错度量: 0.075 (多样性: 0.925)
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -     相关系数: 0.839 (多样性: 0.161)
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -     综合多样性: 0.251
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -   RandomForest_vs_NeuralNet:
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -     Q统计量: 0.962 (多样性: 0.038)
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -     不一致性: 0.075
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -     双错度量: 0.075 (多样性: 0.925)
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -     相关系数: 0.843 (多样性: 0.157)
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -     综合多样性: 0.250
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -   XGBoost_vs_LightGBM:
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -     Q统计量: 0.985 (多样性: 0.015)
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -     不一致性: 0.050
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -     相关系数: 0.896 (多样性: 0.104)
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -     综合多样性: 0.220
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -   XGBoost_vs_CatBoost:
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -     Q统计量: 1.000 (多样性: 0.000)
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -     不一致性: 0.025
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -     相关系数: 0.949 (多样性: 0.051)
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -     综合多样性: 0.198
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -   XGBoost_vs_Logistic:
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -     Q统计量: 0.882 (多样性: 0.118)
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -     不一致性: 0.125
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -     双错度量: 0.075 (多样性: 0.925)
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -     相关系数: 0.738 (多样性: 0.262)
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -     综合多样性: 0.310
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -   XGBoost_vs_SVM:
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -     Q统计量: 0.938 (多样性: 0.062)
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -     不一致性: 0.125
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -     相关系数: 0.741 (多样性: 0.259)
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -     综合多样性: 0.288
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -   XGBoost_vs_KNN:
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -     Q统计量: 0.753 (多样性: 0.247)
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -     不一致性: 0.150
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -     双错度量: 0.050 (多样性: 0.950)
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -     相关系数: 0.688 (多样性: 0.312)
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -     综合多样性: 0.371
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -   XGBoost_vs_NaiveBayes:
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -     Q统计量: 0.922 (多样性: 0.078)
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -     不一致性: 0.100
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -     双错度量: 0.075 (多样性: 0.925)
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -     相关系数: 0.792 (多样性: 0.208)
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -     综合多样性: 0.280
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -   XGBoost_vs_NeuralNet:
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -     Q统计量: 0.922 (多样性: 0.078)
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -     不一致性: 0.100
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -     双错度量: 0.075 (多样性: 0.925)
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -     相关系数: 0.792 (多样性: 0.208)
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -     综合多样性: 0.280
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -   LightGBM_vs_CatBoost:
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -     Q统计量: 1.000 (多样性: 0.000)
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -     不一致性: 0.025
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -     相关系数: 0.949 (多样性: 0.051)
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -     综合多样性: 0.198
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -   LightGBM_vs_Logistic:
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -     Q统计量: 0.882 (多样性: 0.118)
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -     不一致性: 0.125
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -     双错度量: 0.075 (多样性: 0.925)
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -     相关系数: 0.738 (多样性: 0.262)
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -     综合多样性: 0.310
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -   LightGBM_vs_SVM:
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -     Q统计量: 0.938 (多样性: 0.062)
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -     不一致性: 0.125
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -     相关系数: 0.741 (多样性: 0.259)
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -     综合多样性: 0.288
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -   LightGBM_vs_KNN:
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -     Q统计量: 0.922 (多样性: 0.078)
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -     不一致性: 0.100
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -     双错度量: 0.075 (多样性: 0.925)
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -     相关系数: 0.792 (多样性: 0.208)
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -     综合多样性: 0.280
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -   LightGBM_vs_NaiveBayes:
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -     Q统计量: 0.753 (多样性: 0.247)
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -     不一致性: 0.150
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -     双错度量: 0.050 (多样性: 0.950)
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -     相关系数: 0.685 (多样性: 0.315)
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -     综合多样性: 0.372
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -   LightGBM_vs_NeuralNet:
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -     Q统计量: 0.922 (多样性: 0.078)
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -     不一致性: 0.100
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -     双错度量: 0.075 (多样性: 0.925)
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -     相关系数: 0.792 (多样性: 0.208)
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -     综合多样性: 0.280
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -   CatBoost_vs_Logistic:
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -     Q统计量: 0.778 (多样性: 0.222)
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -     不一致性: 0.150
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -     双错度量: 0.050 (多样性: 0.950)
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -     相关系数: 0.680 (多样性: 0.320)
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -     综合多样性: 0.366
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -   CatBoost_vs_SVM:
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -     Q统计量: 0.898 (多样性: 0.102)
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -     不一致性: 0.150
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -     双错度量: 0.075 (多样性: 0.925)
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -     相关系数: 0.675 (多样性: 0.325)
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -     综合多样性: 0.326
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -   CatBoost_vs_KNN:
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -     Q统计量: 0.833 (多样性: 0.167)
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -     不一致性: 0.125
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -     双错度量: 0.050 (多样性: 0.950)
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -     相关系数: 0.738 (多样性: 0.262)
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -     综合多样性: 0.330
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -   CatBoost_vs_NaiveBayes:
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -     Q统计量: 0.833 (多样性: 0.167)
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -     不一致性: 0.125
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -     双错度量: 0.050 (多样性: 0.950)
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -     相关系数: 0.731 (多样性: 0.269)
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -     综合多样性: 0.331
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -   CatBoost_vs_NeuralNet:
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -     Q统计量: 0.833 (多样性: 0.167)
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -     不一致性: 0.125
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -     双错度量: 0.050 (多样性: 0.950)
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -     相关系数: 0.738 (多样性: 0.262)
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -     综合多样性: 0.330
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -   Logistic_vs_SVM:
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -     Q统计量: 1.000 (多样性: 0.000)
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -     不一致性: 0.050
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -     双错度量: 0.150 (多样性: 0.850)
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -     相关系数: 0.896 (多样性: 0.104)
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -     综合多样性: 0.206
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -   Logistic_vs_KNN:
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -     Q统计量: 0.970 (多样性: 0.030)
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -     不一致性: 0.075
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -     相关系数: 0.843 (多样性: 0.157)
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -     综合多样性: 0.243
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -   Logistic_vs_NaiveBayes:
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -     Q统计量: 1.000 (多样性: 0.000)
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -     不一致性: 0.025
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -     双错度量: 0.125 (多样性: 0.875)
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -     相关系数: 0.947 (多样性: 0.053)
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -     综合多样性: 0.193
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -   Logistic_vs_NeuralNet:
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -     Q统计量: 1.000 (多样性: 0.000)
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -     不一致性: 0.025
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -     双错度量: 0.125 (多样性: 0.875)
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -     相关系数: 0.949 (多样性: 0.051)
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -     综合多样性: 0.193
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -   SVM_vs_KNN:
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -     Q统计量: 0.938 (多样性: 0.062)
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -     不一致性: 0.125
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -     相关系数: 0.741 (多样性: 0.259)
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -     综合多样性: 0.288
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -   SVM_vs_NaiveBayes:
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -     Q统计量: 1.000 (多样性: 0.000)
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -     不一致性: 0.075
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -     双错度量: 0.125 (多样性: 0.875)
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -     相关系数: 0.834 (多样性: 0.166)
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -     综合多样性: 0.231
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -   SVM_vs_NeuralNet:
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -     Q统计量: 1.000 (多样性: 0.000)
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -     不一致性: 0.075
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -     双错度量: 0.125 (多样性: 0.875)
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -     相关系数: 0.850 (多样性: 0.150)
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -     综合多样性: 0.228
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -   KNN_vs_NaiveBayes:
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -     Q统计量: 0.922 (多样性: 0.078)
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -     不一致性: 0.100
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -     双错度量: 0.075 (多样性: 0.925)
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -     相关系数: 0.792 (多样性: 0.208)
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -     综合多样性: 0.280
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -   KNN_vs_NeuralNet:
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -     Q统计量: 0.985 (多样性: 0.015)
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -     不一致性: 0.050
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -     相关系数: 0.896 (多样性: 0.104)
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -     综合多样性: 0.220
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -   NaiveBayes_vs_NeuralNet:
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -     Q统计量: 0.985 (多样性: 0.015)
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -     不一致性: 0.050
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -     相关系数: 0.899 (多样性: 0.101)
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -     综合多样性: 0.220
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -   平均多样性: 0.297
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.090
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -   最小多样性: 0.193
2025-08-07 08:26:43 - quantified_diversity_evaluator - INFO -   最大多样性: 0.693
2025-08-07 08:26:43 - enhanced_ensemble_selector - INFO - 量化多样性评估完成:
2025-08-07 08:26:43 - enhanced_ensemble_selector - INFO -   Q统计量平均多样性: 0.297
2025-08-07 08:26:43 - enhanced_ensemble_selector - INFO -   多样性变异系数: 0.301
2025-08-07 08:26:43 - enhanced_ensemble_selector - INFO -   熵多样性: 0.215
2025-08-07 08:26:43 - enhanced_ensemble_selector - INFO - 从 10 个合格模型中选择 3 个进行集成
2025-08-07 08:26:43 - enhanced_ensemble_selector - INFO - 最优组合: ['DecisionTree', 'NaiveBayes', 'NeuralNet']
2025-08-07 08:26:43 - enhanced_ensemble_selector - INFO - 综合得分: 0.7135
2025-08-07 08:30:26 - training_session_manager - INFO - 成功加载会话: 训练_nodule2_20250807_044550
2025-08-07 08:30:26 - session_loader - INFO - 初始化会话加载器
2025-08-07 08:30:28 - training_session_manager - INFO - 成功删除会话: 20250807_044550
2025-08-07 08:30:31 - training_session_manager - INFO - 成功加载会话: 训练_nodule2_20250807_052059
2025-08-07 08:30:31 - session_loader - INFO - 初始化会话加载器
2025-08-07 08:30:33 - training_session_manager - INFO - 成功删除会话: 20250807_052059
2025-08-07 08:30:36 - training_session_manager - INFO - 成功加载会话: 训练_nodule2_20250807_060856
2025-08-07 08:30:36 - session_loader - INFO - 初始化会话加载器
2025-08-07 08:30:39 - training_session_manager - INFO - 成功删除会话: 20250807_060856
2025-08-07 08:30:43 - training_session_manager - INFO - 成功加载会话: 训练_nodule2_20250807_062257
2025-08-07 08:30:43 - session_loader - INFO - 初始化会话加载器
2025-08-07 08:30:44 - training_session_manager - INFO - 成功删除会话: 20250807_062257
2025-08-07 08:32:10 - data_preprocessing - INFO - 成功加载 数据，特征维度: (197, 8)
2025-08-07 08:32:10 - data_preprocessing - INFO - 应用了 standard 特征缩放
2025-08-07 08:32:10 - hyperparameter_tuning - INFO - 开始对 DecisionTree 进行超参数调优，试验次数: 50
2025-08-07 08:32:10 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 4
2025-08-07 08:32:10 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-07 08:32:10 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-07 08:32:10 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 50, 'n_jobs': 4, 'timeout': 1800, 'callbacks': [<function tune_model.<locals>.early_stopping_callback at 0x000001E8E2211310>]}
2025-08-07 08:32:10 - hyperparameter_tuning - INFO - Trial 2: 发现更好的得分 0.9103
2025-08-07 08:32:10 - hyperparameter_tuning - INFO - Trial 0: 发现更好的得分 0.9315
2025-08-07 08:32:10 - hyperparameter_tuning - INFO - Trial 3: 发现更好的得分 0.9361
2025-08-07 08:32:10 - hyperparameter_tuning - INFO - Trial 4: 发现更好的得分 0.9454
2025-08-07 08:32:10 - hyperparameter_tuning - INFO - Trial 13: 发现更好的得分 0.9477
2025-08-07 08:32:10 - hyperparameter_tuning - INFO - Trial 20: 发现更好的得分 0.9511
2025-08-07 08:32:10 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-07 08:32:10 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9511
2025-08-07 08:32:10 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-07 08:32:10 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9511
2025-08-07 08:32:10 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-07 08:32:10 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9511
2025-08-07 08:32:10 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-07 08:32:10 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9511
2025-08-07 08:32:10 - hyperparameter_tuning - INFO - 模型 DecisionTree 的最佳参数: {'max_depth': 7, 'min_samples_split': 44, 'min_samples_leaf': 6, 'criterion': 'entropy', 'class_weight': 'balanced', 'max_features': None}
2025-08-07 08:32:10 - hyperparameter_tuning - INFO - 模型 DecisionTree 的最佳得分: 0.9511
2025-08-07 08:32:10 - hyperparameter_tuning - INFO - 实际执行试验次数: 33/50
2025-08-07 08:32:10 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-07 08:32:11 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-07 08:32:11 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-07 08:32:11 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\DecisionTree\optimization_history_20250807_083211.html
2025-08-07 08:32:11 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-07 08:32:11 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-07 08:32:11 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\DecisionTree\param_importances_20250807_083211.html
2025-08-07 08:32:11 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 1.48 秒
2025-08-07 08:32:11 - hyperparameter_tuning - INFO - 开始对 RandomForest 进行超参数调优，试验次数: 50
2025-08-07 08:32:11 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 4
2025-08-07 08:32:11 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-07 08:32:11 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-07 08:32:11 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 50, 'n_jobs': 4, 'timeout': 1800, 'callbacks': [<function tune_model.<locals>.early_stopping_callback at 0x000001E8E7808040>]}
2025-08-07 08:32:13 - hyperparameter_tuning - INFO - Trial 2: 发现更好的得分 0.9836
2025-08-07 08:32:14 - hyperparameter_tuning - INFO - Trial 5: 发现更好的得分 0.9868
2025-08-07 08:32:20 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-07 08:32:20 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9868
2025-08-07 08:32:21 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-07 08:32:21 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9868
2025-08-07 08:32:22 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-07 08:32:22 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9868
2025-08-07 08:32:22 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-07 08:32:22 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9868
2025-08-07 08:32:22 - hyperparameter_tuning - INFO - 模型 RandomForest 的最佳参数: {'n_estimators': 109, 'max_depth': 24, 'min_samples_split': 11, 'min_samples_leaf': 2, 'max_features': 'log2'}
2025-08-07 08:32:22 - hyperparameter_tuning - INFO - 模型 RandomForest 的最佳得分: 0.9868
2025-08-07 08:32:22 - hyperparameter_tuning - INFO - 实际执行试验次数: 17/50
2025-08-07 08:32:22 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-07 08:32:22 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-07 08:32:22 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-07 08:32:22 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\RandomForest\optimization_history_20250807_083222.html
2025-08-07 08:32:22 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-07 08:32:22 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-07 08:32:23 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\RandomForest\param_importances_20250807_083222.html
2025-08-07 08:32:23 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 11.41 秒
2025-08-07 08:32:23 - hyperparameter_tuning - INFO - 开始对 XGBoost 进行超参数调优，试验次数: 50
2025-08-07 08:32:23 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 4
2025-08-07 08:32:23 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-07 08:32:23 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-07 08:32:23 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 50, 'n_jobs': 4, 'timeout': 1800, 'callbacks': [<function tune_model.<locals>.early_stopping_callback at 0x000001E8E2211310>]}
2025-08-07 08:32:23 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-07 08:32:23 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-07 08:32:23 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-07 08:32:23 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-07 08:32:23 - hyperparameter_tuning - INFO - Trial 1: 发现更好的得分 0.9852
2025-08-07 08:32:23 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-07 08:32:23 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-07 08:32:23 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-07 08:32:23 - hyperparameter_tuning - INFO - Trial 3: 发现更好的得分 0.9868
2025-08-07 08:32:23 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-07 08:32:23 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-07 08:32:24 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-07 08:32:24 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-07 08:32:24 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-07 08:32:24 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-07 08:32:24 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-07 08:32:25 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-07 08:32:25 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-07 08:32:25 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-07 08:32:25 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-07 08:32:25 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9876
2025-08-07 08:32:25 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-07 08:32:25 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9876
2025-08-07 08:32:25 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-07 08:32:25 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9876
2025-08-07 08:32:25 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-07 08:32:25 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9876
2025-08-07 08:32:25 - hyperparameter_tuning - INFO - 模型 XGBoost 的最佳参数: {'n_estimators': 243, 'max_depth': 6, 'learning_rate': 0.25465187941439205, 'subsample': 0.507238644359138, 'colsample_bytree': 0.8155206307416676}
2025-08-07 08:32:25 - hyperparameter_tuning - INFO - 模型 XGBoost 的最佳得分: 0.9876
2025-08-07 08:32:25 - hyperparameter_tuning - INFO - 实际执行试验次数: 17/50
2025-08-07 08:32:25 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-07 08:32:25 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-07 08:32:26 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-07 08:32:26 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\XGBoost\optimization_history_20250807_083225.html
2025-08-07 08:32:26 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-07 08:32:26 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-07 08:32:26 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\XGBoost\param_importances_20250807_083226.html
2025-08-07 08:32:26 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 3.47 秒
2025-08-07 08:32:26 - hyperparameter_tuning - INFO - 开始对 LightGBM 进行超参数调优，试验次数: 50
2025-08-07 08:32:26 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 4
2025-08-07 08:32:26 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-07 08:32:26 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-07 08:32:26 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 50, 'n_jobs': 4, 'timeout': 1800, 'callbacks': [<function tune_model.<locals>.early_stopping_callback at 0x000001E8E7808550>]}
2025-08-07 08:32:26 - hyperparameter_tuning - INFO - Trial 1: 发现更好的得分 0.9802
2025-08-07 08:32:26 - hyperparameter_tuning - INFO - Trial 2: 发现更好的得分 0.9826
2025-08-07 08:32:26 - hyperparameter_tuning - INFO - Trial 4: 发现更好的得分 0.9843
2025-08-07 08:32:27 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-07 08:32:27 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9843
2025-08-07 08:32:27 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-07 08:32:27 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9843
2025-08-07 08:32:27 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-07 08:32:27 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9843
2025-08-07 08:32:27 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-07 08:32:27 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9843
2025-08-07 08:32:27 - hyperparameter_tuning - INFO - 模型 LightGBM 的最佳参数: {'n_estimators': 83, 'max_depth': 6, 'learning_rate': 0.13279123091250636, 'feature_fraction': 0.6778327961008619, 'bagging_fraction': 0.8559692528759305}
2025-08-07 08:32:27 - hyperparameter_tuning - INFO - 模型 LightGBM 的最佳得分: 0.9843
2025-08-07 08:32:27 - hyperparameter_tuning - INFO - 实际执行试验次数: 17/50
2025-08-07 08:32:27 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-07 08:32:27 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-07 08:32:28 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-07 08:32:28 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\LightGBM\optimization_history_20250807_083227.html
2025-08-07 08:32:28 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-07 08:32:28 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-07 08:32:28 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\LightGBM\param_importances_20250807_083228.html
2025-08-07 08:32:28 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 2.17 秒
2025-08-07 08:32:28 - hyperparameter_tuning - INFO - 开始对 CatBoost 进行超参数调优，试验次数: 50
2025-08-07 08:32:28 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 4
2025-08-07 08:32:28 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-07 08:32:28 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-07 08:32:28 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 50, 'n_jobs': 4, 'timeout': 1800, 'callbacks': [<function tune_model.<locals>.early_stopping_callback at 0x000001E8E7808EE0>]}
2025-08-07 08:32:28 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-07 08:32:28 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-07 08:32:28 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-07 08:32:28 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-07 08:32:32 - hyperparameter_tuning - INFO - Trial 0: 发现更好的得分 0.9859
2025-08-07 08:32:32 - hyperparameter_tuning - ERROR - 超参数调优过程中出错: 
All the 5 fits failed.
It is very likely that your model is misconfigured.
You can try to debug the error by setting error_score='raise'.

Below are more details about the failures:
--------------------------------------------------------------------------------
1 <USER> <GROUP> with the following error:
Traceback (most recent call last):
  File "D:\anaconda\envs\multi_model\lib\site-packages\sklearn\model_selection\_validation.py", line 866, in _fit_and_score
    estimator.fit(X_train, y_train, **fit_params)
  File "D:\anaconda\envs\multi_model\lib\site-packages\catboost\core.py", line 5245, in fit
    self._fit(X, y, cat_features, text_features, embedding_features, None, graph, sample_weight, None, None, None, None, baseline, use_best_model,
  File "D:\anaconda\envs\multi_model\lib\site-packages\catboost\core.py", line 2410, in _fit
    self._train(
  File "D:\anaconda\envs\multi_model\lib\site-packages\catboost\core.py", line 1790, in _train
    self._object._train(train_pool, test_pool, params, allow_clear_pool, init_model._object if init_model else None)
  File "_catboost.pyx", line 5017, in _catboost._CatBoost._train
  File "_catboost.pyx", line 5066, in _catboost._CatBoost._train
_catboost.CatBoostError: catboost/cuda/cuda_lib/devices_provider.h:190: Error: device already requested 0

--------------------------------------------------------------------------------
4 <USER> <GROUP> with the following error:
Traceback (most recent call last):
  File "D:\anaconda\envs\multi_model\lib\site-packages\sklearn\model_selection\_validation.py", line 866, in _fit_and_score
    estimator.fit(X_train, y_train, **fit_params)
  File "D:\anaconda\envs\multi_model\lib\site-packages\catboost\core.py", line 5245, in fit
    self._fit(X, y, cat_features, text_features, embedding_features, None, graph, sample_weight, None, None, None, None, baseline, use_best_model,
  File "D:\anaconda\envs\multi_model\lib\site-packages\catboost\core.py", line 2410, in _fit
    self._train(
  File "D:\anaconda\envs\multi_model\lib\site-packages\catboost\core.py", line 1790, in _train
    self._object._train(train_pool, test_pool, params, allow_clear_pool, init_model._object if init_model else None)
  File "_catboost.pyx", line 5017, in _catboost._CatBoost._train
  File "_catboost.pyx", line 5066, in _catboost._CatBoost._train
_catboost.CatBoostError: catboost/cuda/cuda_lib/cuda_manager.cpp:201: Condition violated: `State == nullptr'

2025-08-07 08:32:32 - hyperparameter_tuning - INFO - 开始对 Logistic 进行超参数调优，试验次数: 50
2025-08-07 08:32:32 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 4
2025-08-07 08:32:32 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-07 08:32:32 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-07 08:32:32 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 50, 'n_jobs': 4, 'timeout': 1800, 'callbacks': [<function tune_model.<locals>.early_stopping_callback at 0x000001E8E7808DC0>]}
2025-08-07 08:32:32 - hyperparameter_tuning - INFO - Trial 0: 发现更好的得分 0.9685
2025-08-07 08:32:32 - hyperparameter_tuning - INFO - Trial 3: 发现更好的得分 0.9727
2025-08-07 08:32:32 - hyperparameter_tuning - INFO - Trial 1: 发现更好的得分 0.9761
2025-08-07 08:32:32 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-07 08:32:32 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9761
2025-08-07 08:32:32 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-07 08:32:32 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9761
2025-08-07 08:32:32 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-07 08:32:32 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-07 08:32:32 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9761
2025-08-07 08:32:32 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9761
2025-08-07 08:32:32 - hyperparameter_tuning - INFO - 模型 Logistic 的最佳参数: {'C': 7.3173274166688085, 'solver': 'liblinear'}
2025-08-07 08:32:32 - hyperparameter_tuning - INFO - 模型 Logistic 的最佳得分: 0.9761
2025-08-07 08:32:32 - hyperparameter_tuning - INFO - 实际执行试验次数: 16/50
2025-08-07 08:32:32 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-07 08:32:32 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-07 08:32:32 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-07 08:32:32 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\Logistic\optimization_history_20250807_083232.html
2025-08-07 08:32:32 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-07 08:32:33 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-07 08:32:33 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\Logistic\param_importances_20250807_083232.html
2025-08-07 08:32:33 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 0.81 秒
2025-08-07 08:32:33 - hyperparameter_tuning - INFO - 开始对 SVM 进行超参数调优，试验次数: 50
2025-08-07 08:32:33 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 4
2025-08-07 08:32:33 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-07 08:32:33 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-07 08:32:33 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 50, 'n_jobs': 4, 'timeout': 1800, 'callbacks': [<function tune_model.<locals>.early_stopping_callback at 0x000001E8E7C383A0>]}
2025-08-07 08:32:33 - hyperparameter_tuning - INFO - Trial 1: 发现更好的得分 0.9650
2025-08-07 08:32:33 - hyperparameter_tuning - INFO - Trial 0: 发现更好的得分 0.9693
2025-08-07 08:32:33 - hyperparameter_tuning - INFO - Trial 4: 发现更好的得分 0.9951
2025-08-07 08:32:33 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-07 08:32:33 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-07 08:32:33 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9951
2025-08-07 08:32:33 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9951
2025-08-07 08:32:33 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-07 08:32:33 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9951
2025-08-07 08:32:33 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-07 08:32:33 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9951
2025-08-07 08:32:33 - hyperparameter_tuning - INFO - 模型 SVM 的最佳参数: {'C': 8.629665047938591, 'kernel': 'rbf'}
2025-08-07 08:32:33 - hyperparameter_tuning - INFO - 模型 SVM 的最佳得分: 0.9951
2025-08-07 08:32:33 - hyperparameter_tuning - INFO - 实际执行试验次数: 18/50
2025-08-07 08:32:33 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-07 08:32:33 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-07 08:32:33 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-07 08:32:33 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\SVM\optimization_history_20250807_083233.html
2025-08-07 08:32:33 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-07 08:32:33 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-07 08:32:33 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\SVM\param_importances_20250807_083233.html
2025-08-07 08:32:33 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 0.86 秒
2025-08-07 08:32:33 - hyperparameter_tuning - INFO - 开始对 KNN 进行超参数调优，试验次数: 50
2025-08-07 08:32:33 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 4
2025-08-07 08:32:33 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-07 08:32:33 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-07 08:32:33 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 50, 'n_jobs': 4, 'timeout': 1800, 'callbacks': [<function tune_model.<locals>.early_stopping_callback at 0x000001E8E7C385E0>]}
2025-08-07 08:32:34 - hyperparameter_tuning - INFO - Trial 0: 发现更好的得分 0.9790
2025-08-07 08:32:34 - hyperparameter_tuning - INFO - Trial 3: 发现更好的得分 0.9816
2025-08-07 08:32:34 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-07 08:32:34 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9817
2025-08-07 08:32:34 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-07 08:32:34 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-07 08:32:34 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9817
2025-08-07 08:32:34 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9817
2025-08-07 08:32:34 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-07 08:32:34 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9817
2025-08-07 08:32:34 - hyperparameter_tuning - INFO - 模型 KNN 的最佳参数: {'n_neighbors': 5}
2025-08-07 08:32:34 - hyperparameter_tuning - INFO - 模型 KNN 的最佳得分: 0.9817
2025-08-07 08:32:34 - hyperparameter_tuning - INFO - 实际执行试验次数: 15/50
2025-08-07 08:32:34 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-07 08:32:34 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-07 08:32:34 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-07 08:32:34 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\KNN\optimization_history_20250807_083234.html
2025-08-07 08:32:34 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-07 08:32:34 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-07 08:32:34 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\KNN\param_importances_20250807_083234.html
2025-08-07 08:32:34 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 0.74 秒
2025-08-07 08:32:34 - hyperparameter_tuning - INFO - 开始对 NaiveBayes 进行超参数调优，试验次数: 50
2025-08-07 08:32:34 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 4
2025-08-07 08:32:34 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-07 08:32:34 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-07 08:32:34 - hyperparameter_tuning - INFO - NaiveBayes模型无需调参
2025-08-07 08:32:34 - hyperparameter_tuning - INFO - 开始对 NeuralNet 进行超参数调优，试验次数: 50
2025-08-07 08:32:34 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 4
2025-08-07 08:32:34 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-07 08:32:34 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-07 08:32:34 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 50, 'n_jobs': 4, 'timeout': 1800, 'callbacks': [<function tune_model.<locals>.early_stopping_callback at 0x000001E8E75C4EE0>]}
2025-08-07 08:32:37 - hyperparameter_tuning - INFO - Trial 3: 发现更好的得分 0.9814
2025-08-07 08:32:45 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-07 08:32:45 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9814
2025-08-07 08:32:46 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-07 08:32:46 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9814
2025-08-07 08:32:47 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-07 08:32:47 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9814
2025-08-07 08:32:47 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-07 08:32:47 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9814
2025-08-07 08:32:47 - hyperparameter_tuning - INFO - 模型 NeuralNet 的最佳参数: {'hidden_layer_sizes': (100,), 'alpha': 0.004367182624548074}
2025-08-07 08:32:47 - hyperparameter_tuning - INFO - 模型 NeuralNet 的最佳得分: 0.9814
2025-08-07 08:32:47 - hyperparameter_tuning - INFO - 实际执行试验次数: 14/50
2025-08-07 08:32:47 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-07 08:32:47 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-07 08:32:47 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-07 08:32:47 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\NeuralNet\optimization_history_20250807_083247.html
2025-08-07 08:32:47 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-07 08:32:47 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-07 08:32:47 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\NeuralNet\param_importances_20250807_083247.html
2025-08-07 08:32:47 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 12.94 秒
2025-08-07 08:33:33 - training_session_manager - INFO - 创建会话目录结构: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250807_083333
2025-08-07 08:33:33 - training_session_manager - INFO - 创建训练会话: 训练_nodule2_20250807_083333 (ID: 20250807_083333)
2025-08-07 08:33:33 - training_session_manager - INFO - 创建新会话: 训练_nodule2_20250807_083333
2025-08-07 08:33:33 - session_utils - INFO - 创建新会话: 训练_nodule2_20250807_083333 (ID: 20250807_083333)
2025-08-07 08:33:33 - data_preprocessing - INFO - 成功加载 数据，特征维度: (197, 8)
2025-08-07 08:33:33 - data_preprocessing - INFO - 应用了 standard 特征缩放
2025-08-07 08:33:33 - model_training - INFO - 模型名称: Decision Tree
2025-08-07 08:33:33 - model_training - INFO - 准确率: 0.8049
2025-08-07 08:33:33 - model_training - INFO - AUC: 0.8998
2025-08-07 08:33:33 - model_training - INFO - 混淆矩阵:
2025-08-07 08:33:33 - model_training - INFO - 
[[20  3]
 [ 5 13]]
2025-08-07 08:33:33 - model_training - INFO - 
分类报告:
2025-08-07 08:33:33 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.80      0.87      0.83        23
           1       0.81      0.72      0.76        18

    accuracy                           0.80        41
   macro avg       0.81      0.80      0.80        41
weighted avg       0.81      0.80      0.80        41

2025-08-07 08:33:33 - model_training - INFO - 训练时间: 0.01 秒
2025-08-07 08:33:33 - model_training - INFO - 模型 DecisionTree 性能: 准确率=0.8049
2025-08-07 08:33:33 - training_session_manager - INFO - 保存模型 DecisionTree 到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250807_083333\models\DecisionTree_single_083333.joblib
2025-08-07 08:33:33 - model_training - INFO - 模型 DecisionTree 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250807_083333\models\DecisionTree_single_083333.joblib
2025-08-07 08:33:33 - model_training - INFO - 模型 DecisionTree 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\DecisionTree_results.joblib
2025-08-07 08:33:33 - model_training - INFO - 模型名称: Random Forest
2025-08-07 08:33:33 - model_training - INFO - 准确率: 0.8780
2025-08-07 08:33:33 - model_training - INFO - AUC: 0.9505
2025-08-07 08:33:33 - model_training - INFO - 混淆矩阵:
2025-08-07 08:33:33 - model_training - INFO - 
[[21  2]
 [ 3 15]]
2025-08-07 08:33:33 - model_training - INFO - 
分类报告:
2025-08-07 08:33:33 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.88      0.91      0.89        23
           1       0.88      0.83      0.86        18

    accuracy                           0.88        41
   macro avg       0.88      0.87      0.88        41
weighted avg       0.88      0.88      0.88        41

2025-08-07 08:33:33 - model_training - INFO - 训练时间: 0.08 秒
2025-08-07 08:33:33 - model_training - INFO - 模型 RandomForest 性能: 准确率=0.8780
2025-08-07 08:33:33 - training_session_manager - INFO - 保存模型 RandomForest 到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250807_083333\models\RandomForest_single_083333.joblib
2025-08-07 08:33:33 - model_training - INFO - 模型 RandomForest 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250807_083333\models\RandomForest_single_083333.joblib
2025-08-07 08:33:33 - model_training - INFO - 模型 RandomForest 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\RandomForest_results.joblib
2025-08-07 08:33:33 - model_training - INFO - 模型名称: XGBoost
2025-08-07 08:33:33 - model_training - INFO - 准确率: 0.8780
2025-08-07 08:33:33 - model_training - INFO - AUC: 0.9686
2025-08-07 08:33:33 - model_training - INFO - 混淆矩阵:
2025-08-07 08:33:33 - model_training - INFO - 
[[21  2]
 [ 3 15]]
2025-08-07 08:33:33 - model_training - INFO - 
分类报告:
2025-08-07 08:33:33 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.88      0.91      0.89        23
           1       0.88      0.83      0.86        18

    accuracy                           0.88        41
   macro avg       0.88      0.87      0.88        41
weighted avg       0.88      0.88      0.88        41

2025-08-07 08:33:33 - model_training - INFO - 训练时间: 0.04 秒
2025-08-07 08:33:33 - model_training - INFO - 模型 XGBoost 性能: 准确率=0.8780
2025-08-07 08:33:33 - training_session_manager - INFO - 保存模型 XGBoost 到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250807_083333\models\XGBoost_single_083333.joblib
2025-08-07 08:33:33 - model_training - INFO - 模型 XGBoost 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250807_083333\models\XGBoost_single_083333.joblib
2025-08-07 08:33:33 - model_training - INFO - 模型 XGBoost 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\XGBoost_results.joblib
2025-08-07 08:33:33 - model_training - INFO - 模型名称: LightGBM
2025-08-07 08:33:33 - model_training - INFO - 准确率: 0.8780
2025-08-07 08:33:33 - model_training - INFO - AUC: 0.9493
2025-08-07 08:33:33 - model_training - INFO - 混淆矩阵:
2025-08-07 08:33:33 - model_training - INFO - 
[[21  2]
 [ 3 15]]
2025-08-07 08:33:33 - model_training - INFO - 
分类报告:
2025-08-07 08:33:33 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.88      0.91      0.89        23
           1       0.88      0.83      0.86        18

    accuracy                           0.88        41
   macro avg       0.88      0.87      0.88        41
weighted avg       0.88      0.88      0.88        41

2025-08-07 08:33:33 - model_training - INFO - 训练时间: 0.04 秒
2025-08-07 08:33:33 - model_training - INFO - 模型 LightGBM 性能: 准确率=0.8780
2025-08-07 08:33:33 - training_session_manager - INFO - 保存模型 LightGBM 到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250807_083333\models\LightGBM_single_083333.joblib
2025-08-07 08:33:33 - model_training - INFO - 模型 LightGBM 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250807_083333\models\LightGBM_single_083333.joblib
2025-08-07 08:33:33 - model_training - INFO - 模型 LightGBM 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\LightGBM_results.joblib
2025-08-07 08:33:34 - model_training - INFO - 模型名称: CatBoost
2025-08-07 08:33:34 - model_training - INFO - 准确率: 0.9024
2025-08-07 08:33:34 - model_training - INFO - AUC: 0.9614
2025-08-07 08:33:34 - model_training - INFO - 混淆矩阵:
2025-08-07 08:33:34 - model_training - INFO - 
[[22  1]
 [ 3 15]]
2025-08-07 08:33:34 - model_training - INFO - 
分类报告:
2025-08-07 08:33:34 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.88      0.96      0.92        23
           1       0.94      0.83      0.88        18

    accuracy                           0.90        41
   macro avg       0.91      0.89      0.90        41
weighted avg       0.91      0.90      0.90        41

2025-08-07 08:33:34 - model_training - INFO - 训练时间: 1.15 秒
2025-08-07 08:33:34 - model_training - INFO - 模型 CatBoost 性能: 准确率=0.9024
2025-08-07 08:33:34 - training_session_manager - INFO - 保存模型 CatBoost 到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250807_083333\models\CatBoost_single_083334.joblib
2025-08-07 08:33:34 - model_training - INFO - 模型 CatBoost 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250807_083333\models\CatBoost_single_083334.joblib
2025-08-07 08:33:34 - model_training - INFO - 模型 CatBoost 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\CatBoost_results.joblib
2025-08-07 08:33:34 - model_training - INFO - 模型名称: Logistic Regression
2025-08-07 08:33:34 - model_training - INFO - 准确率: 0.8537
2025-08-07 08:33:34 - model_training - INFO - AUC: 0.9324
2025-08-07 08:33:34 - model_training - INFO - 混淆矩阵:
2025-08-07 08:33:34 - model_training - INFO - 
[[21  2]
 [ 4 14]]
2025-08-07 08:33:34 - model_training - INFO - 
分类报告:
2025-08-07 08:33:34 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.84      0.91      0.88        23
           1       0.88      0.78      0.82        18

    accuracy                           0.85        41
   macro avg       0.86      0.85      0.85        41
weighted avg       0.86      0.85      0.85        41

2025-08-07 08:33:34 - model_training - INFO - 训练时间: 0.01 秒
2025-08-07 08:33:34 - model_training - INFO - 模型 Logistic 性能: 准确率=0.8537
2025-08-07 08:33:34 - training_session_manager - INFO - 保存模型 Logistic 到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250807_083333\models\Logistic_single_083334.joblib
2025-08-07 08:33:34 - model_training - INFO - 模型 Logistic 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250807_083333\models\Logistic_single_083334.joblib
2025-08-07 08:33:34 - model_training - INFO - 模型 Logistic 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\Logistic_results.joblib
2025-08-07 08:33:34 - model_training - INFO - 模型名称: SVM
2025-08-07 08:33:34 - model_training - INFO - 准确率: 0.8049
2025-08-07 08:33:34 - model_training - INFO - AUC: 0.9251
2025-08-07 08:33:34 - model_training - INFO - 混淆矩阵:
2025-08-07 08:33:34 - model_training - INFO - 
[[21  2]
 [ 6 12]]
2025-08-07 08:33:34 - model_training - INFO - 
分类报告:
2025-08-07 08:33:34 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.78      0.91      0.84        23
           1       0.86      0.67      0.75        18

    accuracy                           0.80        41
   macro avg       0.82      0.79      0.79        41
weighted avg       0.81      0.80      0.80        41

2025-08-07 08:33:34 - model_training - INFO - 训练时间: 0.01 秒
2025-08-07 08:33:34 - model_training - INFO - 模型 SVM 性能: 准确率=0.8049
2025-08-07 08:33:34 - training_session_manager - INFO - 保存模型 SVM 到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250807_083333\models\SVM_single_083334.joblib
2025-08-07 08:33:34 - model_training - INFO - 模型 SVM 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250807_083333\models\SVM_single_083334.joblib
2025-08-07 08:33:34 - model_training - INFO - 模型 SVM 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\SVM_results.joblib
2025-08-07 08:33:34 - model_training - INFO - 模型名称: KNN
2025-08-07 08:33:34 - model_training - INFO - 准确率: 0.8780
2025-08-07 08:33:34 - model_training - INFO - AUC: 0.9360
2025-08-07 08:33:34 - model_training - INFO - 混淆矩阵:
2025-08-07 08:33:34 - model_training - INFO - 
[[21  2]
 [ 3 15]]
2025-08-07 08:33:34 - model_training - INFO - 
分类报告:
2025-08-07 08:33:34 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.88      0.91      0.89        23
           1       0.88      0.83      0.86        18

    accuracy                           0.88        41
   macro avg       0.88      0.87      0.88        41
weighted avg       0.88      0.88      0.88        41

2025-08-07 08:33:34 - model_training - INFO - 训练时间: 0.01 秒
2025-08-07 08:33:34 - model_training - INFO - 模型 KNN 性能: 准确率=0.8780
2025-08-07 08:33:34 - training_session_manager - INFO - 保存模型 KNN 到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250807_083333\models\KNN_single_083334.joblib
2025-08-07 08:33:34 - model_training - INFO - 模型 KNN 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250807_083333\models\KNN_single_083334.joblib
2025-08-07 08:33:34 - model_training - INFO - 模型 KNN 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\KNN_results.joblib
2025-08-07 08:33:34 - model_training - INFO - 模型名称: Naive Bayes
2025-08-07 08:33:34 - model_training - INFO - 准确率: 0.8780
2025-08-07 08:33:34 - model_training - INFO - AUC: 0.9058
2025-08-07 08:33:34 - model_training - INFO - 混淆矩阵:
2025-08-07 08:33:34 - model_training - INFO - 
[[22  1]
 [ 4 14]]
2025-08-07 08:33:34 - model_training - INFO - 
分类报告:
2025-08-07 08:33:34 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.85      0.96      0.90        23
           1       0.93      0.78      0.85        18

    accuracy                           0.88        41
   macro avg       0.89      0.87      0.87        41
weighted avg       0.88      0.88      0.88        41

2025-08-07 08:33:34 - model_training - INFO - 训练时间: 0.01 秒
2025-08-07 08:33:34 - model_training - INFO - 模型 NaiveBayes 性能: 准确率=0.8780
2025-08-07 08:33:34 - training_session_manager - INFO - 保存模型 NaiveBayes 到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250807_083333\models\NaiveBayes_single_083334.joblib
2025-08-07 08:33:34 - model_training - INFO - 模型 NaiveBayes 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250807_083333\models\NaiveBayes_single_083334.joblib
2025-08-07 08:33:34 - model_training - INFO - 模型 NaiveBayes 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\NaiveBayes_results.joblib
2025-08-07 08:33:35 - model_training - INFO - 模型名称: Neural Network
2025-08-07 08:33:35 - model_training - INFO - 准确率: 0.8780
2025-08-07 08:33:35 - model_training - INFO - AUC: 0.9614
2025-08-07 08:33:35 - model_training - INFO - 混淆矩阵:
2025-08-07 08:33:35 - model_training - INFO - 
[[21  2]
 [ 3 15]]
2025-08-07 08:33:35 - model_training - INFO - 
分类报告:
2025-08-07 08:33:35 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.88      0.91      0.89        23
           1       0.88      0.83      0.86        18

    accuracy                           0.88        41
   macro avg       0.88      0.87      0.88        41
weighted avg       0.88      0.88      0.88        41

2025-08-07 08:33:35 - model_training - INFO - 训练时间: 0.25 秒
2025-08-07 08:33:35 - model_training - INFO - 模型 NeuralNet 性能: 准确率=0.8780
2025-08-07 08:33:35 - training_session_manager - INFO - 保存模型 NeuralNet 到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250807_083333\models\NeuralNet_single_083335.joblib
2025-08-07 08:33:35 - model_training - INFO - 模型 NeuralNet 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250807_083333\models\NeuralNet_single_083335.joblib
2025-08-07 08:33:35 - model_training - INFO - 模型 NeuralNet 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\NeuralNet_results.joblib
