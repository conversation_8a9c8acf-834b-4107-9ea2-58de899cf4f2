{"session_id": "20250807_083333", "session_name": "训练_nodule2_20250807_083333", "description": "自动创建的训练会话，基于数据文件: nodule2", "created_time": "2025-08-07T08:33:33.268751", "last_modified": "2025-08-07T08:33:35.156839", "trained_models": [{"model_name": "DecisionTree", "model_type": "single", "filename": "DecisionTree_single_083333.joblib", "filepath": "c:\\Users\\<USER>\\PycharmProjects\\multi_model_01_updated\\training_sessions\\20250807_083333\\models\\DecisionTree_single_083333.joblib", "save_time": "2025-08-07T08:33:33.310409"}, {"model_name": "RandomForest", "model_type": "single", "filename": "RandomForest_single_083333.joblib", "filepath": "c:\\Users\\<USER>\\PycharmProjects\\multi_model_01_updated\\training_sessions\\20250807_083333\\models\\RandomForest_single_083333.joblib", "save_time": "2025-08-07T08:33:33.408904"}, {"model_name": "XGBoost", "model_type": "single", "filename": "XGBoost_single_083333.joblib", "filepath": "c:\\Users\\<USER>\\PycharmProjects\\multi_model_01_updated\\training_sessions\\20250807_083333\\models\\XGBoost_single_083333.joblib", "save_time": "2025-08-07T08:33:33.502293"}, {"model_name": "LightGBM", "model_type": "single", "filename": "LightGBM_single_083333.joblib", "filepath": "c:\\Users\\<USER>\\PycharmProjects\\multi_model_01_updated\\training_sessions\\20250807_083333\\models\\LightGBM_single_083333.joblib", "save_time": "2025-08-07T08:33:33.578935"}, {"model_name": "CatBoost", "model_type": "single", "filename": "CatBoost_single_083334.joblib", "filepath": "c:\\Users\\<USER>\\PycharmProjects\\multi_model_01_updated\\training_sessions\\20250807_083333\\models\\CatBoost_single_083334.joblib", "save_time": "2025-08-07T08:33:34.756110"}, {"model_name": "Logistic", "model_type": "single", "filename": "Logistic_single_083334.joblib", "filepath": "c:\\Users\\<USER>\\PycharmProjects\\multi_model_01_updated\\training_sessions\\20250807_083333\\models\\Logistic_single_083334.joblib", "save_time": "2025-08-07T08:33:34.788229"}, {"model_name": "SVM", "model_type": "single", "filename": "SVM_single_083334.joblib", "filepath": "c:\\Users\\<USER>\\PycharmProjects\\multi_model_01_updated\\training_sessions\\20250807_083333\\models\\SVM_single_083334.joblib", "save_time": "2025-08-07T08:33:34.820473"}, {"model_name": "KNN", "model_type": "single", "filename": "KNN_single_083334.joblib", "filepath": "c:\\Users\\<USER>\\PycharmProjects\\multi_model_01_updated\\training_sessions\\20250807_083333\\models\\KNN_single_083334.joblib", "save_time": "2025-08-07T08:33:34.850197"}, {"model_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "model_type": "single", "filename": "NaiveBayes_single_083334.joblib", "filepath": "c:\\Users\\<USER>\\PycharmProjects\\multi_model_01_updated\\training_sessions\\20250807_083333\\models\\NaiveBayes_single_083334.joblib", "save_time": "2025-08-07T08:33:34.879487"}, {"model_name": "NeuralNet", "model_type": "single", "filename": "NeuralNet_single_083335.joblib", "filepath": "c:\\Users\\<USER>\\PycharmProjects\\multi_model_01_updated\\training_sessions\\20250807_083333\\models\\NeuralNet_single_083335.joblib", "save_time": "2025-08-07T08:33:35.148567"}], "ensemble_results": [], "data_files": [], "plots": [], "logs": [], "status": "created"}