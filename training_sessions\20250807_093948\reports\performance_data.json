{"generation_time": "2025-08-07T09:40:02.239293", "best_model": "CatBoost", "best_score": 0.8848230324205927, "model_count": 10, "detailed_metrics": {"NaiveBayes": {"accuracy": 0.875, "precision": 0.9285714285714286, "recall": 0.7647058823529411, "f1_score": 0.8387096774193549, "specificity": 0.9565217391304348, "sensitivity": 0.7647058823529411, "npv": 0.8461538461538461, "ppv": 0.9285714285714286, "auc_roc": 0.89769820971867, "auc_pr": 0.9095747389865037, "mcc": 0.7474980048088188, "kappa": 0.7382198952879582, "balanced_accuracy": 0.860613810741688, "composite_score": 0.8446478747875834}, "RandomForest": {"accuracy": 0.9, "precision": 0.9333333333333333, "recall": 0.8235294117647058, "f1_score": 0.875, "specificity": 0.9565217391304348, "sensitivity": 0.8235294117647058, "npv": 0.88, "ppv": 0.9333333333333333, "auc_roc": 0.9386189258312021, "auc_pr": 0.9390970495829831, "mcc": 0.7965184258559546, "kappa": 0.7922077922077921, "balanced_accuracy": 0.8900255754475703, "composite_score": 0.8807309608093395}, "LightGBM": {"accuracy": 0.875, "precision": 0.875, "recall": 0.8235294117647058, "f1_score": 0.8484848484848485, "specificity": 0.9130434782608695, "sensitivity": 0.8235294117647058, "npv": 0.875, "ppv": 0.875, "auc_roc": 0.9462915601023019, "auc_pr": 0.9452631578947368, "mcc": 0.7432561251138006, "kappa": 0.7422680412371134, "balanced_accuracy": 0.8682864450127876, "composite_score": 0.8564731122492061}, "KNN": {"accuracy": 0.875, "precision": 0.875, "recall": 0.8235294117647058, "f1_score": 0.8484848484848485, "specificity": 0.9130434782608695, "sensitivity": 0.8235294117647058, "npv": 0.875, "ppv": 0.875, "auc_roc": 0.9322250639386189, "auc_pr": 0.9188618925831202, "mcc": 0.7432561251138006, "kappa": 0.7422680412371134, "balanced_accuracy": 0.8682864450127876, "composite_score": 0.8536598130164695}, "XGBoost": {"accuracy": 0.875, "precision": 0.875, "recall": 0.8235294117647058, "f1_score": 0.8484848484848485, "specificity": 0.9130434782608695, "sensitivity": 0.8235294117647058, "npv": 0.875, "ppv": 0.875, "auc_roc": 0.9667519181585678, "auc_pr": 0.9556245561435873, "mcc": 0.7432561251138006, "kappa": 0.7422680412371134, "balanced_accuracy": 0.8682864450127876, "composite_score": 0.8605651838604592}, "SVM": {"accuracy": 0.8, "precision": 0.8461538461538461, "recall": 0.6470588235294118, "f1_score": 0.7333333333333333, "specificity": 0.9130434782608695, "sensitivity": 0.6470588235294118, "npv": 0.7777777777777778, "ppv": 0.8461538461538461, "auc_roc": 0.9207161125319694, "auc_pr": 0.9082446119142531, "mcc": 0.5911561035156879, "kappa": 0.5778364116094987, "balanced_accuracy": 0.7800511508951407, "composite_score": 0.7634652051529024}, "NeuralNet": {"accuracy": 0.875, "precision": 0.875, "recall": 0.8235294117647058, "f1_score": 0.8484848484848485, "specificity": 0.9130434782608695, "sensitivity": 0.8235294117647058, "npv": 0.875, "ppv": 0.875, "auc_roc": 0.959079283887468, "auc_pr": 0.945048204390765, "mcc": 0.7432561251138006, "kappa": 0.7422680412371134, "balanced_accuracy": 0.8682864450127876, "composite_score": 0.8590306570062393}, "DecisionTree": {"accuracy": 0.8, "precision": 0.8, "recall": 0.7058823529411765, "f1_score": 0.75, "specificity": 0.8695652173913043, "sensitivity": 0.7058823529411765, "npv": 0.8, "ppv": 0.8, "auc_roc": 0.8951406649616369, "auc_pr": 0.794830659536542, "mcc": 0.5875955600576713, "kappa": 0.5844155844155844, "balanced_accuracy": 0.7877237851662404, "composite_score": 0.7630498199421546}, "CatBoost": {"accuracy": 0.9, "precision": 0.9333333333333333, "recall": 0.8235294117647058, "f1_score": 0.875, "specificity": 0.9565217391304348, "sensitivity": 0.8235294117647058, "npv": 0.88, "ppv": 0.9333333333333333, "auc_roc": 0.959079283887468, "auc_pr": 0.9570278637770897, "mcc": 0.7965184258559546, "kappa": 0.7922077922077921, "balanced_accuracy": 0.8900255754475703, "composite_score": 0.8848230324205927}, "Logistic": {"accuracy": 0.85, "precision": 0.8666666666666667, "recall": 0.7647058823529411, "f1_score": 0.8125, "specificity": 0.9130434782608695, "sensitivity": 0.7647058823529411, "npv": 0.84, "ppv": 0.8666666666666667, "auc_roc": 0.928388746803069, "auc_pr": 0.9288021844893564, "mcc": 0.6920569929568129, "kappa": 0.6883116883116883, "balanced_accuracy": 0.8388746803069054, "composite_score": 0.8241921806570769}}, "ranking": [["CatBoost", 0.8848230324205927], ["RandomForest", 0.8807309608093395], ["XGBoost", 0.8605651838604592], ["NeuralNet", 0.8590306570062393], ["LightGBM", 0.8564731122492061], ["KNN", 0.8536598130164695], ["<PERSON><PERSON><PERSON><PERSON><PERSON>", 0.8446478747875834], ["Logistic", 0.8241921806570769], ["SVM", 0.7634652051529024], ["DecisionTree", 0.7630498199421546]]}