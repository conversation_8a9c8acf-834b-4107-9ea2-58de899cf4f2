2025-07-15 19:55:39 - model_ensemble - WARNING - SHAP库未安装，可解释性分析功能将被禁用
2025-07-15 19:57:01 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-07-15 19:57:12 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-07-15 19:58:00 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-07-15 19:58:00 - model_ensemble - INFO - 训练基础模型: RandomForest
2025-07-15 19:58:00 - model_ensemble - INFO -   RandomForest 训练完成
2025-07-15 19:58:00 - model_ensemble - INFO - 训练基础模型: XGBoost
2025-07-15 19:58:00 - model_ensemble - INFO -   XGBoost 训练完成
2025-07-15 19:58:00 - model_ensemble - INFO - 训练基础模型: LightGBM
2025-07-15 19:58:01 - model_ensemble - INFO -   LightGBM 训练完成
2025-07-15 19:58:01 - model_ensemble - INFO - 开始训练集成模型，方法: voting
2025-07-15 19:58:01 - model_ensemble - INFO - 开始训练集成模型，方法: bagging
2025-07-15 19:58:01 - model_ensemble - INFO - 开始训练集成模型，方法: boosting
2025-07-15 19:58:01 - model_ensemble - INFO - 开始训练集成模型，方法: stacking
2025-07-15 19:58:03 - model_ensemble - INFO - ============================================================
2025-07-15 19:58:03 - model_ensemble - INFO - 开始运行集成学习管道
2025-07-15 19:58:03 - model_ensemble - INFO - ============================================================
2025-07-15 19:58:03 - model_ensemble - INFO - 基础模型: ['RandomForest', 'XGBoost', 'LightGBM']
2025-07-15 19:58:03 - model_ensemble - INFO - 集成方法: ['voting', 'stacking']
2025-07-15 19:58:03 - model_ensemble - INFO - 步骤1: 训练基础模型
2025-07-15 19:58:03 - model_ensemble - INFO - 训练基础模型: RandomForest
2025-07-15 19:58:03 - model_ensemble - INFO -   RandomForest 训练完成
2025-07-15 19:58:03 - model_ensemble - INFO - 训练基础模型: XGBoost
2025-07-15 19:58:03 - model_ensemble - INFO -   XGBoost 训练完成
2025-07-15 19:58:03 - model_ensemble - INFO - 训练基础模型: LightGBM
2025-07-15 19:58:03 - model_ensemble - INFO -   LightGBM 训练完成
2025-07-15 19:58:03 - model_ensemble - INFO - 成功训练了 3 个基础模型
2025-07-15 19:58:03 - model_ensemble - INFO - 步骤2: 运行集成方法 - voting
2025-07-15 19:58:03 - model_ensemble - INFO -   训练 voting_soft 集成模型
2025-07-15 19:58:03 - model_ensemble - INFO - 开始训练集成模型，方法: voting
2025-07-15 19:58:04 - model_ensemble - INFO -     voting_soft - 准确率: 0.9733, F1: 0.9733
2025-07-15 19:58:04 - model_ensemble - INFO -   训练 voting_hard 集成模型
2025-07-15 19:58:04 - model_ensemble - INFO - 开始训练集成模型，方法: voting
2025-07-15 19:58:04 - model_ensemble - ERROR - 集成方法 voting 失败: This 'VotingClassifier' has no attribute 'predict_proba'
2025-07-15 19:58:04 - model_ensemble - INFO - 步骤2: 运行集成方法 - stacking
2025-07-15 19:58:04 - model_ensemble - INFO - 开始训练集成模型，方法: stacking
2025-07-15 19:58:07 - model_ensemble - INFO -   stacking - 准确率: 0.9733, F1: 0.9733
2025-07-15 19:58:07 - model_ensemble - INFO - ============================================================
2025-07-15 19:58:07 - model_ensemble - INFO - 集成学习结果总结
2025-07-15 19:58:07 - model_ensemble - INFO - ============================================================
2025-07-15 19:58:07 - model_ensemble - INFO - 最佳集成模型: voting_soft
2025-07-15 19:58:07 - model_ensemble - INFO - 最佳F1分数: 0.9733
2025-07-15 19:58:07 - model_ensemble - INFO - 最佳准确率: 0.9733
2025-07-15 19:58:07 - model_ensemble - INFO - 
所有集成模型性能对比:
2025-07-15 19:58:07 - model_ensemble - INFO -   voting_soft     - 准确率: 0.9733, 精确率: 0.9737, 召回率: 0.9733, F1: 0.9733, AUC: 0.9922
2025-07-15 19:58:07 - model_ensemble - INFO -   stacking        - 准确率: 0.9733, 精确率: 0.9737, 召回率: 0.9733, F1: 0.9733, AUC: 0.9922
2025-07-15 19:58:07 - model_ensemble - INFO - ============================================================
2025-07-15 19:58:07 - model_ensemble - INFO - 开始运行集成学习管道
2025-07-15 19:58:07 - model_ensemble - INFO - ============================================================
2025-07-15 19:58:07 - model_ensemble - INFO - 基础模型: ['RandomForest', 'XGBoost']
2025-07-15 19:58:07 - model_ensemble - INFO - 集成方法: ['voting']
2025-07-15 19:58:07 - model_ensemble - INFO - 步骤1: 训练基础模型
2025-07-15 19:58:07 - model_ensemble - INFO - 训练基础模型: RandomForest
2025-07-15 19:58:07 - model_ensemble - INFO -   RandomForest 训练完成
2025-07-15 19:58:07 - model_ensemble - INFO - 训练基础模型: XGBoost
2025-07-15 19:58:07 - model_ensemble - INFO -   XGBoost 训练完成
2025-07-15 19:58:07 - model_ensemble - INFO - 成功训练了 2 个基础模型
2025-07-15 19:58:07 - model_ensemble - INFO - 步骤2: 运行集成方法 - voting
2025-07-15 19:58:07 - model_ensemble - INFO -   训练 voting_soft 集成模型
2025-07-15 19:58:07 - model_ensemble - INFO - 开始训练集成模型，方法: voting
2025-07-15 19:58:08 - model_ensemble - INFO -     voting_soft - 准确率: 0.9567, F1: 0.9567
2025-07-15 19:58:08 - model_ensemble - INFO -   训练 voting_hard 集成模型
2025-07-15 19:58:08 - model_ensemble - INFO - 开始训练集成模型，方法: voting
2025-07-15 19:58:08 - model_ensemble - ERROR - 集成方法 voting 失败: This 'VotingClassifier' has no attribute 'predict_proba'
2025-07-15 19:58:08 - model_ensemble - INFO - ============================================================
2025-07-15 19:58:08 - model_ensemble - INFO - 集成学习结果总结
2025-07-15 19:58:08 - model_ensemble - INFO - ============================================================
2025-07-15 19:58:08 - model_ensemble - INFO - 最佳集成模型: voting_soft
2025-07-15 19:58:08 - model_ensemble - INFO - 最佳F1分数: 0.9567
2025-07-15 19:58:08 - model_ensemble - INFO - 最佳准确率: 0.9567
2025-07-15 19:58:08 - model_ensemble - INFO - 
所有集成模型性能对比:
2025-07-15 19:58:08 - model_ensemble - INFO -   voting_soft     - 准确率: 0.9567, 精确率: 0.9572, 召回率: 0.9567, F1: 0.9567, AUC: 0.9915
2025-07-15 20:04:05 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-07-15 20:05:03 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-07-15 20:06:11 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-07-15 20:06:12 - model_ensemble - INFO - ============================================================
2025-07-15 20:06:12 - model_ensemble - INFO - 开始运行集成学习管道
2025-07-15 20:06:12 - model_ensemble - INFO - ============================================================
2025-07-15 20:06:12 - model_ensemble - INFO - 基础模型: ['RandomForest', 'XGBoost', 'LightGBM']
2025-07-15 20:06:12 - model_ensemble - INFO - 集成方法: ['voting', 'stacking']
2025-07-15 20:06:12 - model_ensemble - INFO - 步骤1: 训练基础模型
2025-07-15 20:06:12 - model_ensemble - INFO - 训练基础模型: RandomForest
2025-07-15 20:06:12 - model_ensemble - INFO -   RandomForest 训练完成
2025-07-15 20:06:12 - model_ensemble - INFO - 训练基础模型: XGBoost
2025-07-15 20:06:12 - model_ensemble - INFO -   XGBoost 训练完成
2025-07-15 20:06:12 - model_ensemble - INFO - 训练基础模型: LightGBM
2025-07-15 20:06:12 - model_ensemble - INFO -   LightGBM 训练完成
2025-07-15 20:06:12 - model_ensemble - INFO - 成功训练了 3 个基础模型
2025-07-15 20:06:12 - model_ensemble - INFO - 步骤2: 运行集成方法 - voting
2025-07-15 20:06:12 - model_ensemble - INFO -   训练 voting_soft 集成模型
2025-07-15 20:06:12 - model_ensemble - INFO - 开始训练集成模型，方法: voting
2025-07-15 20:06:13 - model_ensemble - INFO -     voting_soft - 准确率: 0.9800, F1: 0.9800
2025-07-15 20:06:13 - model_ensemble - INFO -   训练 voting_hard 集成模型
2025-07-15 20:06:13 - model_ensemble - INFO - 开始训练集成模型，方法: voting
2025-07-15 20:06:13 - model_ensemble - ERROR - 集成方法 voting 失败: This 'VotingClassifier' has no attribute 'predict_proba'
2025-07-15 20:06:13 - model_ensemble - INFO - 步骤2: 运行集成方法 - stacking
2025-07-15 20:06:13 - model_ensemble - INFO - 开始训练集成模型，方法: stacking
2025-07-15 20:06:15 - model_ensemble - INFO -   stacking - 准确率: 0.9800, F1: 0.9800
2025-07-15 20:06:15 - model_ensemble - INFO - ============================================================
2025-07-15 20:06:15 - model_ensemble - INFO - 集成学习结果总结
2025-07-15 20:06:15 - model_ensemble - INFO - ============================================================
2025-07-15 20:06:15 - model_ensemble - INFO - 最佳集成模型: voting_soft
2025-07-15 20:06:15 - model_ensemble - INFO - 最佳F1分数: 0.9800
2025-07-15 20:06:15 - model_ensemble - INFO - 最佳准确率: 0.9800
2025-07-15 20:06:15 - model_ensemble - INFO - 
所有集成模型性能对比:
2025-07-15 20:06:15 - model_ensemble - INFO -   voting_soft     - 准确率: 0.9800, 精确率: 0.9800, 召回率: 0.9800, F1: 0.9800, AUC: 0.9922
2025-07-15 20:06:15 - model_ensemble - INFO -   stacking        - 准确率: 0.9800, 精确率: 0.9800, 召回率: 0.9800, F1: 0.9800, AUC: 0.9923
2025-07-15 20:06:15 - model_ensemble - INFO - 步骤3: 保存集成学习结果
2025-07-15 20:06:15 - model_ensemble - INFO - 集成学习结果已保存到: output\ensemble_example\ensemble_results_20250715_200615.joblib
2025-07-15 20:06:15 - model_ensemble - INFO - 训练基础模型: RandomForest
2025-07-15 20:06:15 - model_ensemble - INFO -   RandomForest 训练完成
2025-07-15 20:06:15 - model_ensemble - INFO - 训练基础模型: XGBoost
2025-07-15 20:06:15 - model_ensemble - INFO -   XGBoost 训练完成
2025-07-15 20:06:15 - model_ensemble - INFO - 开始训练集成模型，方法: voting
2025-07-15 20:06:16 - model_ensemble - INFO - 开始训练集成模型，方法: bagging
2025-07-15 20:06:16 - model_ensemble - INFO - 开始训练集成模型，方法: stacking
2025-07-15 20:06:18 - model_ensemble - INFO - ============================================================
2025-07-15 20:06:18 - model_ensemble - INFO - 开始运行集成学习管道
2025-07-15 20:06:18 - model_ensemble - INFO - ============================================================
2025-07-15 20:06:18 - model_ensemble - INFO - 基础模型: ['RandomForest', 'XGBoost', 'LightGBM']
2025-07-15 20:06:18 - model_ensemble - INFO - 集成方法: ['voting', 'stacking']
2025-07-15 20:06:18 - model_ensemble - INFO - 步骤1: 训练基础模型
2025-07-15 20:06:18 - model_ensemble - INFO - 训练基础模型: RandomForest
2025-07-15 20:06:18 - model_ensemble - INFO -   RandomForest 训练完成
2025-07-15 20:06:18 - model_ensemble - INFO - 训练基础模型: XGBoost
2025-07-15 20:06:18 - model_ensemble - INFO -   XGBoost 训练完成
2025-07-15 20:06:18 - model_ensemble - INFO - 训练基础模型: LightGBM
2025-07-15 20:06:18 - model_ensemble - INFO -   LightGBM 训练完成
2025-07-15 20:06:18 - model_ensemble - INFO - 成功训练了 3 个基础模型
2025-07-15 20:06:18 - model_ensemble - INFO - 步骤2: 运行集成方法 - voting
2025-07-15 20:06:18 - model_ensemble - INFO -   训练 voting_soft 集成模型
2025-07-15 20:06:18 - model_ensemble - INFO - 开始训练集成模型，方法: voting
2025-07-15 20:06:19 - model_ensemble - INFO -     voting_soft - 准确率: 0.9800, F1: 0.9800
2025-07-15 20:06:19 - model_ensemble - INFO -   训练 voting_hard 集成模型
2025-07-15 20:06:19 - model_ensemble - INFO - 开始训练集成模型，方法: voting
2025-07-15 20:06:19 - model_ensemble - ERROR - 集成方法 voting 失败: This 'VotingClassifier' has no attribute 'predict_proba'
2025-07-15 20:06:19 - model_ensemble - INFO - 步骤2: 运行集成方法 - stacking
2025-07-15 20:06:19 - model_ensemble - INFO - 开始训练集成模型，方法: stacking
2025-07-15 20:06:21 - model_ensemble - INFO -   stacking - 准确率: 0.9800, F1: 0.9800
2025-07-15 20:06:21 - model_ensemble - INFO - ============================================================
2025-07-15 20:06:21 - model_ensemble - INFO - 集成学习结果总结
2025-07-15 20:06:21 - model_ensemble - INFO - ============================================================
2025-07-15 20:06:21 - model_ensemble - INFO - 最佳集成模型: voting_soft
2025-07-15 20:06:21 - model_ensemble - INFO - 最佳F1分数: 0.9800
2025-07-15 20:06:21 - model_ensemble - INFO - 最佳准确率: 0.9800
2025-07-15 20:06:21 - model_ensemble - INFO - 
所有集成模型性能对比:
2025-07-15 20:06:21 - model_ensemble - INFO -   voting_soft     - 准确率: 0.9800, 精确率: 0.9800, 召回率: 0.9800, F1: 0.9800, AUC: 0.9922
2025-07-15 20:06:21 - model_ensemble - INFO -   stacking        - 准确率: 0.9800, 精确率: 0.9800, 召回率: 0.9800, F1: 0.9800, AUC: 0.9923
2025-07-15 20:06:21 - model_ensemble - INFO - 步骤3: 保存集成学习结果
2025-07-15 20:06:21 - model_ensemble - INFO - 集成学习结果已保存到: output\complete_pipeline\ensemble\ensemble_results_20250715_200621.joblib
2025-07-15 20:08:56 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-07-15 20:15:56 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-07-15 20:19:28 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-07-15 20:23:53 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-07-15 20:25:01 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-07-15 20:26:52 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-07-15 20:27:18 - model_ensemble - INFO - ============================================================
2025-07-15 20:27:18 - model_ensemble - INFO - 开始运行集成学习管道
2025-07-15 20:27:18 - model_ensemble - INFO - ============================================================
2025-07-15 20:27:18 - model_ensemble - INFO - 基础模型: ['RandomForest', 'XGBoost', 'LightGBM', 'Logistic']
2025-07-15 20:27:18 - model_ensemble - INFO - 集成方法: ['voting', 'stacking']
2025-07-15 20:27:18 - model_ensemble - INFO - 步骤1: 训练基础模型
2025-07-15 20:27:18 - model_ensemble - INFO - 训练基础模型: RandomForest
2025-07-15 20:27:19 - model_ensemble - INFO -   RandomForest 训练完成
2025-07-15 20:27:19 - model_ensemble - INFO - 训练基础模型: XGBoost
2025-07-15 20:27:19 - model_ensemble - INFO -   XGBoost 训练完成
2025-07-15 20:27:19 - model_ensemble - INFO - 训练基础模型: LightGBM
2025-07-15 20:27:20 - model_ensemble - INFO -   LightGBM 训练完成
2025-07-15 20:27:20 - model_ensemble - INFO - 训练基础模型: Logistic
2025-07-15 20:27:20 - model_ensemble - INFO -   Logistic 训练完成
2025-07-15 20:27:20 - model_ensemble - INFO - 成功训练了 4 个基础模型
2025-07-15 20:27:20 - model_ensemble - INFO - 步骤2: 运行集成方法 - voting
2025-07-15 20:27:20 - model_ensemble - INFO -   训练 voting_soft 集成模型
2025-07-15 20:27:20 - model_ensemble - INFO - 开始训练集成模型，方法: voting
2025-07-15 20:27:21 - model_ensemble - INFO -     voting_soft - 准确率: 0.8000, F1: 0.8000
2025-07-15 20:27:21 - model_ensemble - INFO -   训练 voting_hard 集成模型
2025-07-15 20:27:21 - model_ensemble - INFO - 开始训练集成模型，方法: voting
2025-07-15 20:27:21 - model_ensemble - ERROR - 集成方法 voting 失败: This 'VotingClassifier' has no attribute 'predict_proba'
2025-07-15 20:27:21 - model_ensemble - INFO - 步骤2: 运行集成方法 - stacking
2025-07-15 20:27:21 - model_ensemble - INFO - 开始训练集成模型，方法: stacking
2025-07-15 20:27:23 - model_ensemble - INFO -   stacking - 准确率: 0.8250, F1: 0.8242
2025-07-15 20:27:23 - model_ensemble - INFO - ============================================================
2025-07-15 20:27:23 - model_ensemble - INFO - 集成学习结果总结
2025-07-15 20:27:23 - model_ensemble - INFO - ============================================================
2025-07-15 20:27:23 - model_ensemble - INFO - 最佳集成模型: stacking
2025-07-15 20:27:23 - model_ensemble - INFO - 最佳F1分数: 0.8242
2025-07-15 20:27:23 - model_ensemble - INFO - 最佳准确率: 0.8250
2025-07-15 20:27:23 - model_ensemble - INFO - 
所有集成模型性能对比:
2025-07-15 20:27:23 - model_ensemble - INFO -   voting_soft     - 准确率: 0.8000, 精确率: 0.8000, 召回率: 0.8000, F1: 0.8000, AUC: 0.8900
2025-07-15 20:27:23 - model_ensemble - INFO -   stacking        - 准确率: 0.8250, 精确率: 0.8245, 召回率: 0.8250, F1: 0.8242, AUC: 0.8875
2025-07-15 20:27:23 - model_ensemble - INFO - 步骤3: 保存集成学习结果
2025-07-15 20:27:23 - model_ensemble - INFO - 集成学习结果已保存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\ensemble\ensemble_results_20250715_202723.joblib
2025-07-15 20:28:08 - model_ensemble - INFO - ============================================================
2025-07-15 20:28:08 - model_ensemble - INFO - 开始运行集成学习管道
2025-07-15 20:28:08 - model_ensemble - INFO - ============================================================
2025-07-15 20:28:08 - model_ensemble - INFO - 基础模型: ['RandomForest', 'XGBoost', 'LightGBM', 'Logistic']
2025-07-15 20:28:08 - model_ensemble - INFO - 集成方法: ['voting', 'stacking']
2025-07-15 20:28:08 - model_ensemble - INFO - 步骤1: 训练基础模型
2025-07-15 20:28:08 - model_ensemble - INFO - 训练基础模型: RandomForest
2025-07-15 20:28:08 - model_ensemble - INFO -   RandomForest 训练完成
2025-07-15 20:28:08 - model_ensemble - INFO - 训练基础模型: XGBoost
2025-07-15 20:28:08 - model_ensemble - INFO -   XGBoost 训练完成
2025-07-15 20:28:08 - model_ensemble - INFO - 训练基础模型: LightGBM
2025-07-15 20:28:09 - model_ensemble - INFO -   LightGBM 训练完成
2025-07-15 20:28:09 - model_ensemble - INFO - 训练基础模型: Logistic
2025-07-15 20:28:09 - model_ensemble - INFO -   Logistic 训练完成
2025-07-15 20:28:09 - model_ensemble - INFO - 成功训练了 4 个基础模型
2025-07-15 20:28:09 - model_ensemble - INFO - 步骤2: 运行集成方法 - voting
2025-07-15 20:28:09 - model_ensemble - INFO -   训练 voting_soft 集成模型
2025-07-15 20:28:09 - model_ensemble - INFO - 开始训练集成模型，方法: voting
2025-07-15 20:28:09 - model_ensemble - INFO -     voting_soft - 准确率: 0.8000, F1: 0.8000
2025-07-15 20:28:09 - model_ensemble - INFO -   训练 voting_hard 集成模型
2025-07-15 20:28:09 - model_ensemble - INFO - 开始训练集成模型，方法: voting
2025-07-15 20:28:09 - model_ensemble - ERROR - 集成方法 voting 失败: This 'VotingClassifier' has no attribute 'predict_proba'
2025-07-15 20:28:09 - model_ensemble - INFO - 步骤2: 运行集成方法 - stacking
2025-07-15 20:28:09 - model_ensemble - INFO - 开始训练集成模型，方法: stacking
2025-07-15 20:28:11 - model_ensemble - INFO -   stacking - 准确率: 0.8250, F1: 0.8242
2025-07-15 20:28:11 - model_ensemble - INFO - ============================================================
2025-07-15 20:28:11 - model_ensemble - INFO - 集成学习结果总结
2025-07-15 20:28:11 - model_ensemble - INFO - ============================================================
2025-07-15 20:28:11 - model_ensemble - INFO - 最佳集成模型: stacking
2025-07-15 20:28:11 - model_ensemble - INFO - 最佳F1分数: 0.8242
2025-07-15 20:28:11 - model_ensemble - INFO - 最佳准确率: 0.8250
2025-07-15 20:28:11 - model_ensemble - INFO - 
所有集成模型性能对比:
2025-07-15 20:28:11 - model_ensemble - INFO -   voting_soft     - 准确率: 0.8000, 精确率: 0.8000, 召回率: 0.8000, F1: 0.8000, AUC: 0.8900
2025-07-15 20:28:11 - model_ensemble - INFO -   stacking        - 准确率: 0.8250, 精确率: 0.8245, 召回率: 0.8250, F1: 0.8242, AUC: 0.8875
2025-07-15 20:28:11 - model_ensemble - INFO - 步骤3: 保存集成学习结果
2025-07-15 20:28:11 - model_ensemble - INFO - 集成学习结果已保存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\ensemble\ensemble_results_20250715_202811.joblib
2025-07-15 20:33:54 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-07-15 20:36:10 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-07-15 20:36:48 - model_ensemble - INFO - ============================================================
2025-07-15 20:36:48 - model_ensemble - INFO - 开始运行集成学习管道
2025-07-15 20:36:48 - model_ensemble - INFO - ============================================================
2025-07-15 20:36:48 - model_ensemble - INFO - 基础模型: ['RandomForest', 'XGBoost', 'LightGBM', 'Logistic']
2025-07-15 20:36:48 - model_ensemble - INFO - 集成方法: ['voting', 'stacking']
2025-07-15 20:36:48 - model_ensemble - INFO - 步骤1: 训练基础模型
2025-07-15 20:36:48 - model_ensemble - INFO - 训练基础模型: RandomForest
2025-07-15 20:36:48 - model_ensemble - INFO -   RandomForest 训练完成
2025-07-15 20:36:48 - model_ensemble - INFO - 训练基础模型: XGBoost
2025-07-15 20:36:48 - model_ensemble - INFO -   XGBoost 训练完成
2025-07-15 20:36:48 - model_ensemble - INFO - 训练基础模型: LightGBM
2025-07-15 20:36:50 - model_ensemble - INFO -   LightGBM 训练完成
2025-07-15 20:36:50 - model_ensemble - INFO - 训练基础模型: Logistic
2025-07-15 20:36:50 - model_ensemble - INFO -   Logistic 训练完成
2025-07-15 20:36:50 - model_ensemble - INFO - 成功训练了 4 个基础模型
2025-07-15 20:36:50 - model_ensemble - INFO - 步骤2: 运行集成方法 - voting
2025-07-15 20:36:50 - model_ensemble - INFO -   训练 voting_soft 集成模型
2025-07-15 20:36:50 - model_ensemble - INFO - 开始训练集成模型，方法: voting
2025-07-15 20:36:50 - model_ensemble - INFO -     voting_soft - 准确率: 0.8000, F1: 0.8000
2025-07-15 20:36:50 - model_ensemble - INFO -   训练 voting_hard 集成模型
2025-07-15 20:36:50 - model_ensemble - INFO - 开始训练集成模型，方法: voting
2025-07-15 20:36:51 - model_ensemble - ERROR - 集成方法 voting 失败: This 'VotingClassifier' has no attribute 'predict_proba'
2025-07-15 20:36:51 - model_ensemble - INFO - 步骤2: 运行集成方法 - stacking
2025-07-15 20:36:51 - model_ensemble - INFO - 开始训练集成模型，方法: stacking
2025-07-15 20:36:53 - model_ensemble - INFO -   stacking - 准确率: 0.8250, F1: 0.8242
2025-07-15 20:36:53 - model_ensemble - INFO - ============================================================
2025-07-15 20:36:53 - model_ensemble - INFO - 集成学习结果总结
2025-07-15 20:36:53 - model_ensemble - INFO - ============================================================
2025-07-15 20:36:53 - model_ensemble - INFO - 最佳集成模型: stacking
2025-07-15 20:36:53 - model_ensemble - INFO - 最佳F1分数: 0.8242
2025-07-15 20:36:53 - model_ensemble - INFO - 最佳准确率: 0.8250
2025-07-15 20:36:53 - model_ensemble - INFO - 
所有集成模型性能对比:
2025-07-15 20:36:53 - model_ensemble - INFO -   voting_soft     - 准确率: 0.8000, 精确率: 0.8000, 召回率: 0.8000, F1: 0.8000, AUC: 0.8900
2025-07-15 20:36:53 - model_ensemble - INFO -   stacking        - 准确率: 0.8250, 精确率: 0.8245, 召回率: 0.8250, F1: 0.8242, AUC: 0.8875
2025-07-15 20:36:53 - model_ensemble - INFO - 步骤3: 保存集成学习结果
2025-07-15 20:36:53 - model_ensemble - INFO - 集成学习结果已保存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\ensemble\ensemble_results_20250715_203653.joblib
2025-07-15 20:42:43 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-07-15 20:45:24 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-07-15 20:46:00 - model_ensemble - INFO - ============================================================
2025-07-15 20:46:00 - model_ensemble - INFO - 开始运行集成学习管道
2025-07-15 20:46:00 - model_ensemble - INFO - ============================================================
2025-07-15 20:46:00 - model_ensemble - INFO - 基础模型: ['RandomForest', 'XGBoost', 'LightGBM', 'Logistic']
2025-07-15 20:46:00 - model_ensemble - INFO - 集成方法: ['voting', 'stacking']
2025-07-15 20:46:00 - model_ensemble - INFO - 步骤1: 训练基础模型
2025-07-15 20:46:00 - model_ensemble - INFO - 训练基础模型: RandomForest
2025-07-15 20:46:00 - model_ensemble - INFO -   RandomForest 训练完成
2025-07-15 20:46:00 - model_ensemble - INFO - 训练基础模型: XGBoost
2025-07-15 20:46:00 - model_ensemble - INFO -   XGBoost 训练完成
2025-07-15 20:46:00 - model_ensemble - INFO - 训练基础模型: LightGBM
2025-07-15 20:46:02 - model_ensemble - INFO -   LightGBM 训练完成
2025-07-15 20:46:02 - model_ensemble - INFO - 训练基础模型: Logistic
2025-07-15 20:46:02 - model_ensemble - INFO -   Logistic 训练完成
2025-07-15 20:46:02 - model_ensemble - INFO - 成功训练了 4 个基础模型
2025-07-15 20:46:02 - model_ensemble - INFO - 步骤2: 运行集成方法 - voting
2025-07-15 20:46:02 - model_ensemble - INFO -   训练 voting_soft 集成模型
2025-07-15 20:46:02 - model_ensemble - INFO - 开始训练集成模型，方法: voting
2025-07-15 20:46:02 - model_ensemble - INFO -     voting_soft - 准确率: 0.8000, F1: 0.8000
2025-07-15 20:46:02 - model_ensemble - INFO -   训练 voting_hard 集成模型
2025-07-15 20:46:02 - model_ensemble - INFO - 开始训练集成模型，方法: voting
2025-07-15 20:46:03 - model_ensemble - ERROR - 集成方法 voting 失败: This 'VotingClassifier' has no attribute 'predict_proba'
2025-07-15 20:46:03 - model_ensemble - INFO - 步骤2: 运行集成方法 - stacking
2025-07-15 20:46:03 - model_ensemble - INFO - 开始训练集成模型，方法: stacking
2025-07-15 20:46:05 - model_ensemble - INFO -   stacking - 准确率: 0.8250, F1: 0.8242
2025-07-15 20:46:05 - model_ensemble - INFO - ============================================================
2025-07-15 20:46:05 - model_ensemble - INFO - 集成学习结果总结
2025-07-15 20:46:05 - model_ensemble - INFO - ============================================================
2025-07-15 20:46:05 - model_ensemble - INFO - 最佳集成模型: stacking
2025-07-15 20:46:05 - model_ensemble - INFO - 最佳F1分数: 0.8242
2025-07-15 20:46:05 - model_ensemble - INFO - 最佳准确率: 0.8250
2025-07-15 20:46:05 - model_ensemble - INFO - 
所有集成模型性能对比:
2025-07-15 20:46:05 - model_ensemble - INFO -   voting_soft     - 准确率: 0.8000, 精确率: 0.8000, 召回率: 0.8000, F1: 0.8000, AUC: 0.8900
2025-07-15 20:46:05 - model_ensemble - INFO -   stacking        - 准确率: 0.8250, 精确率: 0.8245, 召回率: 0.8250, F1: 0.8242, AUC: 0.8875
2025-07-15 20:46:05 - model_ensemble - INFO - 步骤3: 保存集成学习结果
2025-07-15 20:46:05 - model_ensemble - INFO - 集成学习结果已保存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\ensemble\ensemble_results_20250715_204605.joblib
2025-07-15 20:47:51 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-07-15 20:48:27 - model_ensemble - INFO - ============================================================
2025-07-15 20:48:27 - model_ensemble - INFO - 开始运行集成学习管道
2025-07-15 20:48:27 - model_ensemble - INFO - ============================================================
2025-07-15 20:48:27 - model_ensemble - INFO - 基础模型: ['RandomForest', 'XGBoost', 'SVM', 'KNN']
2025-07-15 20:48:27 - model_ensemble - INFO - 集成方法: ['voting', 'stacking']
2025-07-15 20:48:27 - model_ensemble - INFO - 步骤1: 训练基础模型
2025-07-15 20:48:27 - model_ensemble - INFO - 训练基础模型: RandomForest
2025-07-15 20:48:27 - model_ensemble - INFO -   RandomForest 训练完成
2025-07-15 20:48:27 - model_ensemble - INFO - 训练基础模型: XGBoost
2025-07-15 20:48:27 - model_ensemble - INFO -   XGBoost 训练完成
2025-07-15 20:48:27 - model_ensemble - INFO - 训练基础模型: SVM
2025-07-15 20:48:27 - model_ensemble - INFO -   SVM 训练完成
2025-07-15 20:48:27 - model_ensemble - INFO - 训练基础模型: KNN
2025-07-15 20:48:28 - model_ensemble - INFO -   KNN 训练完成
2025-07-15 20:48:28 - model_ensemble - INFO - 成功训练了 4 个基础模型
2025-07-15 20:48:28 - model_ensemble - INFO - 步骤2: 运行集成方法 - voting
2025-07-15 20:48:28 - model_ensemble - INFO -   训练 voting_soft 集成模型
2025-07-15 20:48:28 - model_ensemble - INFO - 开始训练集成模型，方法: voting
2025-07-15 20:48:28 - model_ensemble - INFO -     voting_soft - 准确率: 0.8750, F1: 0.8744
2025-07-15 20:48:28 - model_ensemble - INFO -   训练 voting_hard 集成模型
2025-07-15 20:48:28 - model_ensemble - INFO - 开始训练集成模型，方法: voting
2025-07-15 20:48:28 - model_ensemble - ERROR - 集成方法 voting 失败: This 'VotingClassifier' has no attribute 'predict_proba'
2025-07-15 20:48:28 - model_ensemble - INFO - 步骤2: 运行集成方法 - stacking
2025-07-15 20:48:28 - model_ensemble - INFO - 开始训练集成模型，方法: stacking
2025-07-15 20:48:29 - model_ensemble - INFO -   stacking - 准确率: 0.8750, F1: 0.8744
2025-07-15 20:48:29 - model_ensemble - INFO - ============================================================
2025-07-15 20:48:29 - model_ensemble - INFO - 集成学习结果总结
2025-07-15 20:48:29 - model_ensemble - INFO - ============================================================
2025-07-15 20:48:29 - model_ensemble - INFO - 最佳集成模型: voting_soft
2025-07-15 20:48:29 - model_ensemble - INFO - 最佳F1分数: 0.8744
2025-07-15 20:48:29 - model_ensemble - INFO - 最佳准确率: 0.8750
2025-07-15 20:48:29 - model_ensemble - INFO - 
所有集成模型性能对比:
2025-07-15 20:48:29 - model_ensemble - INFO -   voting_soft     - 准确率: 0.8750, 精确率: 0.8750, 召回率: 0.8750, F1: 0.8744, AUC: 0.9540
2025-07-15 20:48:29 - model_ensemble - INFO -   stacking        - 准确率: 0.8750, 精确率: 0.8750, 召回率: 0.8750, F1: 0.8744, AUC: 0.9565
2025-07-15 20:48:29 - model_ensemble - INFO - 步骤3: 保存集成学习结果
2025-07-15 20:48:29 - model_ensemble - INFO - 集成学习结果已保存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\ensemble\ensemble_results_20250715_204829.joblib
2025-07-15 20:50:56 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-07-15 20:52:10 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-07-15 20:52:10 - model_ensemble - INFO - ============================================================
2025-07-15 20:52:10 - model_ensemble - INFO - 开始运行集成学习管道
2025-07-15 20:52:10 - model_ensemble - INFO - ============================================================
2025-07-15 20:52:10 - model_ensemble - INFO - 基础模型: ['RandomForest', 'XGBoost']
2025-07-15 20:52:10 - model_ensemble - INFO - 集成方法: ['voting']
2025-07-15 20:52:10 - model_ensemble - INFO - 步骤1: 训练基础模型
2025-07-15 20:52:10 - model_ensemble - INFO - 训练基础模型: RandomForest
2025-07-15 20:52:10 - model_ensemble - INFO -   RandomForest 训练完成
2025-07-15 20:52:10 - model_ensemble - INFO - 训练基础模型: XGBoost
2025-07-15 20:52:10 - model_ensemble - INFO -   XGBoost 训练完成
2025-07-15 20:52:10 - model_ensemble - INFO - 成功训练了 2 个基础模型
2025-07-15 20:52:10 - model_ensemble - INFO - 步骤2: 运行集成方法 - voting
2025-07-15 20:52:10 - model_ensemble - INFO -   训练 voting_soft 集成模型
2025-07-15 20:52:10 - model_ensemble - INFO - 开始训练集成模型，方法: voting
2025-07-15 20:52:10 - model_ensemble - INFO -     voting_soft - 准确率: 0.8667, F1: 0.8665
2025-07-15 20:52:10 - model_ensemble - INFO -   训练 voting_hard 集成模型
2025-07-15 20:52:10 - model_ensemble - INFO - 开始训练集成模型，方法: voting
2025-07-15 20:52:11 - model_ensemble - INFO -     voting_hard - 准确率: 0.8833, F1: 0.8830
2025-07-15 20:52:11 - model_ensemble - INFO - ============================================================
2025-07-15 20:52:11 - model_ensemble - INFO - 集成学习结果总结
2025-07-15 20:52:11 - model_ensemble - INFO - ============================================================
2025-07-15 20:52:11 - model_ensemble - INFO - 最佳集成模型: voting_hard
2025-07-15 20:52:11 - model_ensemble - INFO - 最佳F1分数: 0.8830
2025-07-15 20:52:11 - model_ensemble - INFO - 最佳准确率: 0.8833
2025-07-15 20:52:11 - model_ensemble - INFO - 
所有集成模型性能对比:
2025-07-15 20:52:11 - model_ensemble - INFO -   voting_soft     - 准确率: 0.8667, 精确率: 0.8683, 召回率: 0.8667, F1: 0.8665, AUC: 0.9278
2025-07-15 20:52:11 - model_ensemble - INFO -   voting_hard     - 准确率: 0.8833, 精确率: 0.8872, 召回率: 0.8833, F1: 0.8830, AUC: 0.0000
2025-07-15 20:53:54 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-07-15 20:56:29 - model_ensemble - INFO - ============================================================
2025-07-15 20:56:29 - model_ensemble - INFO - 开始运行集成学习管道
2025-07-15 20:56:29 - model_ensemble - INFO - ============================================================
2025-07-15 20:56:29 - model_ensemble - INFO - 基础模型: ['RandomForest', 'XGBoost', 'LightGBM', 'Logistic']
2025-07-15 20:56:29 - model_ensemble - INFO - 集成方法: ['voting', 'stacking']
2025-07-15 20:56:29 - model_ensemble - INFO - 步骤1: 训练基础模型
2025-07-15 20:56:29 - model_ensemble - INFO - 训练基础模型: RandomForest
2025-07-15 20:56:29 - model_ensemble - INFO -   RandomForest 训练完成
2025-07-15 20:56:29 - model_ensemble - INFO - 训练基础模型: XGBoost
2025-07-15 20:56:29 - model_ensemble - INFO -   XGBoost 训练完成
2025-07-15 20:56:29 - model_ensemble - INFO - 训练基础模型: LightGBM
2025-07-15 20:56:29 - model_ensemble - INFO -   LightGBM 训练完成
2025-07-15 20:56:29 - model_ensemble - INFO - 训练基础模型: Logistic
2025-07-15 20:56:29 - model_ensemble - INFO -   Logistic 训练完成
2025-07-15 20:56:29 - model_ensemble - INFO - 成功训练了 4 个基础模型
2025-07-15 20:56:29 - model_ensemble - INFO - 步骤2: 运行集成方法 - voting
2025-07-15 20:56:29 - model_ensemble - INFO -   训练 voting_soft 集成模型
2025-07-15 20:56:29 - model_ensemble - INFO - 开始训练集成模型，方法: voting
2025-07-15 20:56:30 - model_ensemble - INFO -     voting_soft - 准确率: 0.8750, F1: 0.8744
2025-07-15 20:56:30 - model_ensemble - INFO -   训练 voting_hard 集成模型
2025-07-15 20:56:30 - model_ensemble - INFO - 开始训练集成模型，方法: voting
2025-07-15 20:56:30 - model_ensemble - INFO -     voting_hard - 准确率: 0.9000, F1: 0.8990
2025-07-15 20:56:30 - model_ensemble - INFO - 步骤2: 运行集成方法 - stacking
2025-07-15 20:56:30 - model_ensemble - INFO - 开始训练集成模型，方法: stacking
2025-07-15 20:56:32 - model_ensemble - INFO -   stacking - 准确率: 0.8750, F1: 0.8744
2025-07-15 20:56:32 - model_ensemble - INFO - ============================================================
2025-07-15 20:56:32 - model_ensemble - INFO - 集成学习结果总结
2025-07-15 20:56:32 - model_ensemble - INFO - ============================================================
2025-07-15 20:56:32 - model_ensemble - INFO - 最佳集成模型: voting_hard
2025-07-15 20:56:32 - model_ensemble - INFO - 最佳F1分数: 0.8990
2025-07-15 20:56:32 - model_ensemble - INFO - 最佳准确率: 0.9000
2025-07-15 20:56:32 - model_ensemble - INFO - 
所有集成模型性能对比:
2025-07-15 20:56:32 - model_ensemble - INFO -   voting_soft     - 准确率: 0.8750, 精确率: 0.8750, 召回率: 0.8750, F1: 0.8744, AUC: 0.9668
2025-07-15 20:56:32 - model_ensemble - INFO -   voting_hard     - 准确率: 0.9000, 精确率: 0.9027, 召回率: 0.9000, F1: 0.8990, AUC: 0.0000
2025-07-15 20:56:32 - model_ensemble - INFO -   stacking        - 准确率: 0.8750, 精确率: 0.8750, 召回率: 0.8750, F1: 0.8744, AUC: 0.9668
2025-07-15 20:56:32 - model_ensemble - INFO - 步骤3: 保存集成学习结果
2025-07-15 20:56:32 - model_ensemble - INFO - 集成学习结果已保存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\ensemble\ensemble_results_20250715_205632.joblib
2025-07-15 20:58:09 - model_ensemble - INFO - ============================================================
2025-07-15 20:58:09 - model_ensemble - INFO - 开始运行集成学习管道
2025-07-15 20:58:09 - model_ensemble - INFO - ============================================================
2025-07-15 20:58:09 - model_ensemble - INFO - 基础模型: ['RandomForest', 'XGBoost', 'LightGBM', 'Logistic']
2025-07-15 20:58:09 - model_ensemble - INFO - 集成方法: ['voting', 'stacking']
2025-07-15 20:58:09 - model_ensemble - INFO - 步骤1: 训练基础模型
2025-07-15 20:58:09 - model_ensemble - INFO - 训练基础模型: RandomForest
2025-07-15 20:58:10 - model_ensemble - INFO -   RandomForest 训练完成
2025-07-15 20:58:10 - model_ensemble - INFO - 训练基础模型: XGBoost
2025-07-15 20:58:10 - model_ensemble - INFO -   XGBoost 训练完成
2025-07-15 20:58:10 - model_ensemble - INFO - 训练基础模型: LightGBM
2025-07-15 20:58:10 - model_ensemble - INFO -   LightGBM 训练完成
2025-07-15 20:58:10 - model_ensemble - INFO - 训练基础模型: Logistic
2025-07-15 20:58:10 - model_ensemble - INFO -   Logistic 训练完成
2025-07-15 20:58:10 - model_ensemble - INFO - 成功训练了 4 个基础模型
2025-07-15 20:58:10 - model_ensemble - INFO - 步骤2: 运行集成方法 - voting
2025-07-15 20:58:10 - model_ensemble - INFO -   训练 voting_soft 集成模型
2025-07-15 20:58:10 - model_ensemble - INFO - 开始训练集成模型，方法: voting
2025-07-15 20:58:10 - model_ensemble - INFO -     voting_soft - 准确率: 0.8750, F1: 0.8744
2025-07-15 20:58:10 - model_ensemble - INFO -   训练 voting_hard 集成模型
2025-07-15 20:58:10 - model_ensemble - INFO - 开始训练集成模型，方法: voting
2025-07-15 20:58:11 - model_ensemble - INFO -     voting_hard - 准确率: 0.9000, F1: 0.8990
2025-07-15 20:58:11 - model_ensemble - INFO - 步骤2: 运行集成方法 - stacking
2025-07-15 20:58:11 - model_ensemble - INFO - 开始训练集成模型，方法: stacking
2025-07-15 20:58:12 - model_ensemble - INFO -   stacking - 准确率: 0.8750, F1: 0.8744
2025-07-15 20:58:12 - model_ensemble - INFO - ============================================================
2025-07-15 20:58:12 - model_ensemble - INFO - 集成学习结果总结
2025-07-15 20:58:12 - model_ensemble - INFO - ============================================================
2025-07-15 20:58:12 - model_ensemble - INFO - 最佳集成模型: voting_hard
2025-07-15 20:58:12 - model_ensemble - INFO - 最佳F1分数: 0.8990
2025-07-15 20:58:12 - model_ensemble - INFO - 最佳准确率: 0.9000
2025-07-15 20:58:12 - model_ensemble - INFO - 
所有集成模型性能对比:
2025-07-15 20:58:12 - model_ensemble - INFO -   voting_soft     - 准确率: 0.8750, 精确率: 0.8750, 召回率: 0.8750, F1: 0.8744, AUC: 0.9668
2025-07-15 20:58:12 - model_ensemble - INFO -   voting_hard     - 准确率: 0.9000, 精确率: 0.9027, 召回率: 0.9000, F1: 0.8990, AUC: 0.0000
2025-07-15 20:58:12 - model_ensemble - INFO -   stacking        - 准确率: 0.8750, 精确率: 0.8750, 召回率: 0.8750, F1: 0.8744, AUC: 0.9668
2025-07-15 20:58:12 - model_ensemble - INFO - 步骤3: 保存集成学习结果
2025-07-15 20:58:12 - model_ensemble - INFO - 集成学习结果已保存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\ensemble\ensemble_results_20250715_205812.joblib
2025-07-15 21:05:20 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-07-15 21:05:31 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-07-15 21:07:45 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-07-15 21:08:13 - model_ensemble - INFO - ============================================================
2025-07-15 21:08:13 - model_ensemble - INFO - 开始运行集成学习管道
2025-07-15 21:08:13 - model_ensemble - INFO - ============================================================
2025-07-15 21:08:13 - model_ensemble - INFO - 基础模型: ['RandomForest', 'XGBoost', 'LightGBM', 'Logistic']
2025-07-15 21:08:13 - model_ensemble - INFO - 集成方法: ['voting', 'stacking']
2025-07-15 21:08:13 - model_ensemble - INFO - 步骤1: 训练基础模型
2025-07-15 21:08:13 - model_ensemble - INFO - 训练基础模型: RandomForest
2025-07-15 21:08:13 - model_ensemble - INFO -   RandomForest 训练完成
2025-07-15 21:08:13 - model_ensemble - INFO - 训练基础模型: XGBoost
2025-07-15 21:08:13 - model_ensemble - INFO -   XGBoost 训练完成
2025-07-15 21:08:13 - model_ensemble - INFO - 训练基础模型: LightGBM
2025-07-15 21:08:15 - model_ensemble - INFO -   LightGBM 训练完成
2025-07-15 21:08:15 - model_ensemble - INFO - 训练基础模型: Logistic
2025-07-15 21:08:15 - model_ensemble - INFO -   Logistic 训练完成
2025-07-15 21:08:15 - model_ensemble - INFO - 成功训练了 4 个基础模型
2025-07-15 21:08:15 - model_ensemble - INFO - 步骤2: 运行集成方法 - voting
2025-07-15 21:08:15 - model_ensemble - INFO -   训练 voting_soft 集成模型
2025-07-15 21:08:15 - model_ensemble - INFO - 开始训练集成模型，方法: voting
2025-07-15 21:08:15 - model_ensemble - INFO -     voting_soft - 准确率: 0.8000, F1: 0.8000
2025-07-15 21:08:15 - model_ensemble - INFO -   训练 voting_hard 集成模型
2025-07-15 21:08:15 - model_ensemble - INFO - 开始训练集成模型，方法: voting
2025-07-15 21:08:16 - model_ensemble - INFO -     voting_hard - 准确率: 0.8000, F1: 0.7979
2025-07-15 21:08:16 - model_ensemble - INFO - 步骤2: 运行集成方法 - stacking
2025-07-15 21:08:16 - model_ensemble - INFO - 开始训练集成模型，方法: stacking
2025-07-15 21:08:18 - model_ensemble - INFO -   stacking - 准确率: 0.8250, F1: 0.8242
2025-07-15 21:08:18 - model_ensemble - INFO - ============================================================
2025-07-15 21:08:18 - model_ensemble - INFO - 集成学习结果总结
2025-07-15 21:08:18 - model_ensemble - INFO - ============================================================
2025-07-15 21:08:18 - model_ensemble - INFO - 最佳集成模型: stacking
2025-07-15 21:08:18 - model_ensemble - INFO - 最佳F1分数: 0.8242
2025-07-15 21:08:18 - model_ensemble - INFO - 最佳准确率: 0.8250
2025-07-15 21:08:18 - model_ensemble - INFO - 
所有集成模型性能对比:
2025-07-15 21:08:18 - model_ensemble - INFO -   voting_soft     - 准确率: 0.8000, 精确率: 0.8000, 召回率: 0.8000, F1: 0.8000, AUC: 0.8900
2025-07-15 21:08:18 - model_ensemble - INFO -   voting_hard     - 准确率: 0.8000, 精确率: 0.8000, 召回率: 0.8000, F1: 0.7979, AUC: 0.0000
2025-07-15 21:08:18 - model_ensemble - INFO -   stacking        - 准确率: 0.8250, 精确率: 0.8245, 召回率: 0.8250, F1: 0.8242, AUC: 0.8875
2025-07-15 21:08:18 - model_ensemble - INFO - 步骤3: 保存集成学习结果
2025-07-15 21:08:18 - model_ensemble - INFO - 集成学习结果已保存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\ensemble\ensemble_results_20250715_210818.joblib
2025-07-15 21:16:00 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-07-15 21:16:00 - model_ensemble - INFO - ============================================================
2025-07-15 21:16:00 - model_ensemble - INFO - 开始运行集成学习管道
2025-07-15 21:16:00 - model_ensemble - INFO - ============================================================
2025-07-15 21:16:00 - model_ensemble - INFO - 基础模型: ['RandomForest', 'XGBoost']
2025-07-15 21:16:00 - model_ensemble - INFO - 集成方法: ['voting']
2025-07-15 21:16:00 - model_ensemble - INFO - 步骤1: 训练基础模型
2025-07-15 21:16:00 - model_ensemble - INFO - 训练基础模型: RandomForest
2025-07-15 21:16:00 - model_ensemble - INFO -   RandomForest 训练完成
2025-07-15 21:16:00 - model_ensemble - INFO - 训练基础模型: XGBoost
2025-07-15 21:16:00 - model_ensemble - INFO -   XGBoost 训练完成
2025-07-15 21:16:00 - model_ensemble - INFO - 成功训练了 2 个基础模型
2025-07-15 21:16:00 - model_ensemble - INFO - 步骤2: 运行集成方法 - voting
2025-07-15 21:16:00 - model_ensemble - INFO -   训练 voting_soft 集成模型
2025-07-15 21:16:00 - model_ensemble - INFO - 开始训练集成模型，方法: voting
2025-07-15 21:16:00 - model_ensemble - INFO -     voting_soft - 准确率: 0.8667, F1: 0.8665
2025-07-15 21:16:00 - model_ensemble - INFO -   训练 voting_hard 集成模型
2025-07-15 21:16:00 - model_ensemble - INFO - 开始训练集成模型，方法: voting
2025-07-15 21:16:00 - model_ensemble - INFO -     voting_hard - 准确率: 0.8833, F1: 0.8830
2025-07-15 21:16:00 - model_ensemble - INFO - ============================================================
2025-07-15 21:16:00 - model_ensemble - INFO - 集成学习结果总结
2025-07-15 21:16:00 - model_ensemble - INFO - ============================================================
2025-07-15 21:16:00 - model_ensemble - INFO - 最佳集成模型: voting_hard
2025-07-15 21:16:00 - model_ensemble - INFO - 最佳F1分数: 0.8830
2025-07-15 21:16:00 - model_ensemble - INFO - 最佳准确率: 0.8833
2025-07-15 21:16:00 - model_ensemble - INFO - 
所有集成模型性能对比:
2025-07-15 21:16:00 - model_ensemble - INFO -   voting_soft     - 准确率: 0.8667, 精确率: 0.8683, 召回率: 0.8667, F1: 0.8665, AUC: 0.9278
2025-07-15 21:16:00 - model_ensemble - INFO -   voting_hard     - 准确率: 0.8833, 精确率: 0.8872, 召回率: 0.8833, F1: 0.8830, AUC: 0.0000
2025-07-15 21:16:01 - model_ensemble - INFO - ============================================================
2025-07-15 21:16:01 - model_ensemble - INFO - 开始运行集成学习管道
2025-07-15 21:16:01 - model_ensemble - INFO - ============================================================
2025-07-15 21:16:01 - model_ensemble - INFO - 基础模型: ['RandomForest', 'XGBoost']
2025-07-15 21:16:01 - model_ensemble - INFO - 集成方法: ['voting', 'stacking']
2025-07-15 21:16:01 - model_ensemble - INFO - 步骤1: 训练基础模型
2025-07-15 21:16:01 - model_ensemble - INFO - 训练基础模型: RandomForest
2025-07-15 21:16:01 - model_ensemble - INFO -   RandomForest 训练完成
2025-07-15 21:16:01 - model_ensemble - INFO - 训练基础模型: XGBoost
2025-07-15 21:16:01 - model_ensemble - INFO -   XGBoost 训练完成
2025-07-15 21:16:01 - model_ensemble - INFO - 成功训练了 2 个基础模型
2025-07-15 21:16:01 - model_ensemble - INFO - 步骤2: 运行集成方法 - voting
2025-07-15 21:16:01 - model_ensemble - INFO -   训练 voting_soft 集成模型
2025-07-15 21:16:01 - model_ensemble - INFO - 开始训练集成模型，方法: voting
2025-07-15 21:16:01 - model_ensemble - INFO -     voting_soft - 准确率: 0.8500, F1: 0.8465
2025-07-15 21:16:01 - model_ensemble - INFO -   训练 voting_hard 集成模型
2025-07-15 21:16:01 - model_ensemble - INFO - 开始训练集成模型，方法: voting
2025-07-15 21:16:01 - model_ensemble - INFO -     voting_hard - 准确率: 0.9000, F1: 0.8990
2025-07-15 21:16:01 - model_ensemble - INFO - 步骤2: 运行集成方法 - stacking
2025-07-15 21:16:01 - model_ensemble - INFO - 开始训练集成模型，方法: stacking
2025-07-15 21:16:02 - model_ensemble - INFO -   stacking - 准确率: 0.8500, F1: 0.8465
2025-07-15 21:16:02 - model_ensemble - INFO - ============================================================
2025-07-15 21:16:02 - model_ensemble - INFO - 集成学习结果总结
2025-07-15 21:16:02 - model_ensemble - INFO - ============================================================
2025-07-15 21:16:02 - model_ensemble - INFO - 最佳集成模型: voting_hard
2025-07-15 21:16:02 - model_ensemble - INFO - 最佳F1分数: 0.8990
2025-07-15 21:16:02 - model_ensemble - INFO - 最佳准确率: 0.9000
2025-07-15 21:16:02 - model_ensemble - INFO - 
所有集成模型性能对比:
2025-07-15 21:16:02 - model_ensemble - INFO -   voting_soft     - 准确率: 0.8500, 精确率: 0.8846, 召回率: 0.8500, F1: 0.8465, AUC: 0.9800
2025-07-15 21:16:02 - model_ensemble - INFO -   voting_hard     - 准确率: 0.9000, 精确率: 0.9167, 召回率: 0.9000, F1: 0.8990, AUC: 0.0000
2025-07-15 21:16:02 - model_ensemble - INFO -   stacking        - 准确率: 0.8500, 精确率: 0.8846, 召回率: 0.8500, F1: 0.8465, AUC: 0.9800
2025-07-15 21:16:02 - model_ensemble - INFO - 步骤3: 保存集成学习结果
2025-07-15 21:16:02 - model_ensemble - INFO - 集成学习结果已保存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\ensemble\ensemble_results_20250715_211602.joblib
2025-07-15 21:17:57 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-07-15 21:17:57 - model_ensemble - INFO - ============================================================
2025-07-15 21:17:57 - model_ensemble - INFO - 开始运行集成学习管道
2025-07-15 21:17:57 - model_ensemble - INFO - ============================================================
2025-07-15 21:17:57 - model_ensemble - INFO - 基础模型: ['RandomForest', 'XGBoost']
2025-07-15 21:17:57 - model_ensemble - INFO - 集成方法: ['voting']
2025-07-15 21:17:57 - model_ensemble - INFO - 步骤1: 训练基础模型
2025-07-15 21:17:57 - model_ensemble - INFO - 训练基础模型: RandomForest
2025-07-15 21:17:57 - model_ensemble - INFO -   RandomForest 训练完成
2025-07-15 21:17:57 - model_ensemble - INFO - 训练基础模型: XGBoost
2025-07-15 21:17:57 - model_ensemble - INFO -   XGBoost 训练完成
2025-07-15 21:17:57 - model_ensemble - INFO - 成功训练了 2 个基础模型
2025-07-15 21:17:57 - model_ensemble - INFO - 步骤2: 运行集成方法 - voting
2025-07-15 21:17:57 - model_ensemble - INFO -   训练 voting_soft 集成模型
2025-07-15 21:17:57 - model_ensemble - INFO - 开始训练集成模型，方法: voting
2025-07-15 21:17:57 - model_ensemble - INFO -     voting_soft - 准确率: 0.8667, F1: 0.8665
2025-07-15 21:17:57 - model_ensemble - INFO -   训练 voting_hard 集成模型
2025-07-15 21:17:57 - model_ensemble - INFO - 开始训练集成模型，方法: voting
2025-07-15 21:17:57 - model_ensemble - INFO -     voting_hard - 准确率: 0.8833, F1: 0.8830
2025-07-15 21:17:57 - model_ensemble - INFO - ============================================================
2025-07-15 21:17:57 - model_ensemble - INFO - 集成学习结果总结
2025-07-15 21:17:57 - model_ensemble - INFO - ============================================================
2025-07-15 21:17:57 - model_ensemble - INFO - 最佳集成模型: voting_hard
2025-07-15 21:17:57 - model_ensemble - INFO - 最佳F1分数: 0.8830
2025-07-15 21:17:57 - model_ensemble - INFO - 最佳准确率: 0.8833
2025-07-15 21:17:57 - model_ensemble - INFO - 
所有集成模型性能对比:
2025-07-15 21:17:57 - model_ensemble - INFO -   voting_soft     - 准确率: 0.8667, 精确率: 0.8683, 召回率: 0.8667, F1: 0.8665, AUC: 0.9278
2025-07-15 21:17:57 - model_ensemble - INFO -   voting_hard     - 准确率: 0.8833, 精确率: 0.8872, 召回率: 0.8833, F1: 0.8830, AUC: 0.0000
2025-07-15 21:17:58 - model_ensemble - INFO - ============================================================
2025-07-15 21:17:58 - model_ensemble - INFO - 开始运行集成学习管道
2025-07-15 21:17:58 - model_ensemble - INFO - ============================================================
2025-07-15 21:17:58 - model_ensemble - INFO - 基础模型: ['RandomForest', 'XGBoost']
2025-07-15 21:17:58 - model_ensemble - INFO - 集成方法: ['voting', 'stacking']
2025-07-15 21:17:58 - model_ensemble - INFO - 步骤1: 训练基础模型
2025-07-15 21:17:58 - model_ensemble - INFO - 训练基础模型: RandomForest
2025-07-15 21:17:58 - model_ensemble - INFO -   RandomForest 训练完成
2025-07-15 21:17:58 - model_ensemble - INFO - 训练基础模型: XGBoost
2025-07-15 21:17:58 - model_ensemble - INFO -   XGBoost 训练完成
2025-07-15 21:17:58 - model_ensemble - INFO - 成功训练了 2 个基础模型
2025-07-15 21:17:58 - model_ensemble - INFO - 步骤2: 运行集成方法 - voting
2025-07-15 21:17:58 - model_ensemble - INFO -   训练 voting_soft 集成模型
2025-07-15 21:17:58 - model_ensemble - INFO - 开始训练集成模型，方法: voting
2025-07-15 21:17:58 - model_ensemble - INFO -     voting_soft - 准确率: 0.8500, F1: 0.8465
2025-07-15 21:17:58 - model_ensemble - INFO -   训练 voting_hard 集成模型
2025-07-15 21:17:58 - model_ensemble - INFO - 开始训练集成模型，方法: voting
2025-07-15 21:17:58 - model_ensemble - INFO -     voting_hard - 准确率: 0.9000, F1: 0.8990
2025-07-15 21:17:58 - model_ensemble - INFO - 步骤2: 运行集成方法 - stacking
2025-07-15 21:17:58 - model_ensemble - INFO - 开始训练集成模型，方法: stacking
2025-07-15 21:17:59 - model_ensemble - INFO -   stacking - 准确率: 0.8500, F1: 0.8465
2025-07-15 21:17:59 - model_ensemble - INFO - ============================================================
2025-07-15 21:17:59 - model_ensemble - INFO - 集成学习结果总结
2025-07-15 21:17:59 - model_ensemble - INFO - ============================================================
2025-07-15 21:17:59 - model_ensemble - INFO - 最佳集成模型: voting_hard
2025-07-15 21:17:59 - model_ensemble - INFO - 最佳F1分数: 0.8990
2025-07-15 21:17:59 - model_ensemble - INFO - 最佳准确率: 0.9000
2025-07-15 21:17:59 - model_ensemble - INFO - 
所有集成模型性能对比:
2025-07-15 21:17:59 - model_ensemble - INFO -   voting_soft     - 准确率: 0.8500, 精确率: 0.8846, 召回率: 0.8500, F1: 0.8465, AUC: 0.9800
2025-07-15 21:17:59 - model_ensemble - INFO -   voting_hard     - 准确率: 0.9000, 精确率: 0.9167, 召回率: 0.9000, F1: 0.8990, AUC: 0.0000
2025-07-15 21:17:59 - model_ensemble - INFO -   stacking        - 准确率: 0.8500, 精确率: 0.8846, 召回率: 0.8500, F1: 0.8465, AUC: 0.9800
2025-07-15 21:17:59 - model_ensemble - INFO - 步骤3: 保存集成学习结果
2025-07-15 21:17:59 - model_ensemble - INFO - 集成学习结果已保存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\ensemble\ensemble_results_20250715_211759.joblib
2025-07-15 21:17:59 - model_ensemble - INFO - Ensemble report saved to: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\ensemble
2025-07-15 21:20:31 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-07-15 21:20:50 - model_ensemble - INFO - ============================================================
2025-07-15 21:20:50 - model_ensemble - INFO - 开始运行集成学习管道
2025-07-15 21:20:50 - model_ensemble - INFO - ============================================================
2025-07-15 21:20:50 - model_ensemble - INFO - 基础模型: ['RandomForest', 'XGBoost', 'LightGBM', 'Logistic']
2025-07-15 21:20:50 - model_ensemble - INFO - 集成方法: ['voting', 'stacking']
2025-07-15 21:20:50 - model_ensemble - INFO - 步骤1: 训练基础模型
2025-07-15 21:20:50 - model_ensemble - INFO - 训练基础模型: RandomForest
2025-07-15 21:20:50 - model_ensemble - INFO -   RandomForest 训练完成
2025-07-15 21:20:50 - model_ensemble - INFO - 训练基础模型: XGBoost
2025-07-15 21:20:50 - model_ensemble - INFO -   XGBoost 训练完成
2025-07-15 21:20:50 - model_ensemble - INFO - 训练基础模型: LightGBM
2025-07-15 21:20:52 - model_ensemble - INFO -   LightGBM 训练完成
2025-07-15 21:20:52 - model_ensemble - INFO - 训练基础模型: Logistic
2025-07-15 21:20:52 - model_ensemble - INFO -   Logistic 训练完成
2025-07-15 21:20:52 - model_ensemble - INFO - 成功训练了 4 个基础模型
2025-07-15 21:20:52 - model_ensemble - INFO - 步骤2: 运行集成方法 - voting
2025-07-15 21:20:52 - model_ensemble - INFO -   训练 voting_soft 集成模型
2025-07-15 21:20:52 - model_ensemble - INFO - 开始训练集成模型，方法: voting
2025-07-15 21:20:52 - model_ensemble - INFO -     voting_soft - 准确率: 0.8000, F1: 0.8000
2025-07-15 21:20:52 - model_ensemble - INFO -   训练 voting_hard 集成模型
2025-07-15 21:20:52 - model_ensemble - INFO - 开始训练集成模型，方法: voting
2025-07-15 21:20:53 - model_ensemble - INFO -     voting_hard - 准确率: 0.8000, F1: 0.7979
2025-07-15 21:20:53 - model_ensemble - INFO - 步骤2: 运行集成方法 - stacking
2025-07-15 21:20:53 - model_ensemble - INFO - 开始训练集成模型，方法: stacking
2025-07-15 21:20:54 - model_ensemble - INFO -   stacking - 准确率: 0.8250, F1: 0.8242
2025-07-15 21:20:54 - model_ensemble - INFO - ============================================================
2025-07-15 21:20:54 - model_ensemble - INFO - 集成学习结果总结
2025-07-15 21:20:54 - model_ensemble - INFO - ============================================================
2025-07-15 21:20:54 - model_ensemble - INFO - 最佳集成模型: stacking
2025-07-15 21:20:54 - model_ensemble - INFO - 最佳F1分数: 0.8242
2025-07-15 21:20:54 - model_ensemble - INFO - 最佳准确率: 0.8250
2025-07-15 21:20:54 - model_ensemble - INFO - 
所有集成模型性能对比:
2025-07-15 21:20:54 - model_ensemble - INFO -   voting_soft     - 准确率: 0.8000, 精确率: 0.8000, 召回率: 0.8000, F1: 0.8000, AUC: 0.8900
2025-07-15 21:20:54 - model_ensemble - INFO -   voting_hard     - 准确率: 0.8000, 精确率: 0.8000, 召回率: 0.8000, F1: 0.7979, AUC: 0.0000
2025-07-15 21:20:54 - model_ensemble - INFO -   stacking        - 准确率: 0.8250, 精确率: 0.8245, 召回率: 0.8250, F1: 0.8242, AUC: 0.8875
2025-07-15 21:20:54 - model_ensemble - INFO - 步骤3: 保存集成学习结果
2025-07-15 21:20:55 - model_ensemble - INFO - 集成学习结果已保存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\ensemble\ensemble_results_20250715_212054.joblib
2025-07-15 21:20:55 - model_ensemble - INFO - Ensemble report saved to: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\ensemble
2025-07-15 21:20:55 - model_ensemble - INFO - 步骤4: 生成SHAP可解释性分析
2025-07-15 21:20:55 - model_ensemble - INFO - 为 voting_soft 生成SHAP分析
2025-07-15 21:21:00 - model_ensemble - INFO -   voting_soft SHAP分析完成
2025-07-15 21:21:00 - model_ensemble - INFO - 为 voting_hard 生成SHAP分析
2025-07-15 21:21:07 - model_ensemble - INFO -   voting_hard SHAP分析完成
2025-07-15 21:21:07 - model_ensemble - INFO - 为 stacking 生成SHAP分析
2025-07-15 21:21:10 - model_ensemble - INFO -   stacking SHAP分析完成
2025-07-15 21:29:13 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-07-15 21:29:13 - model_ensemble - INFO - ============================================================
2025-07-15 21:29:13 - model_ensemble - INFO - 开始运行集成学习管道
2025-07-15 21:29:13 - model_ensemble - INFO - ============================================================
2025-07-15 21:29:13 - model_ensemble - INFO - 基础模型: ['RandomForest', 'XGBoost']
2025-07-15 21:29:13 - model_ensemble - INFO - 集成方法: ['voting']
2025-07-15 21:29:13 - model_ensemble - INFO - 步骤1: 训练基础模型
2025-07-15 21:29:13 - model_ensemble - INFO - 训练基础模型: RandomForest
2025-07-15 21:29:13 - model_ensemble - INFO -   RandomForest 训练完成
2025-07-15 21:29:13 - model_ensemble - INFO - 训练基础模型: XGBoost
2025-07-15 21:29:13 - model_ensemble - INFO -   XGBoost 训练完成
2025-07-15 21:29:13 - model_ensemble - INFO - 成功训练了 2 个基础模型
2025-07-15 21:29:13 - model_ensemble - INFO - 步骤2: 运行集成方法 - voting
2025-07-15 21:29:13 - model_ensemble - INFO -   训练 voting_soft 集成模型
2025-07-15 21:29:13 - model_ensemble - INFO - 开始训练集成模型，方法: voting
2025-07-15 21:29:13 - model_ensemble - INFO -     voting_soft - 准确率: 0.9500, F1: 0.9500
2025-07-15 21:29:13 - model_ensemble - INFO -   训练 voting_hard 集成模型
2025-07-15 21:29:13 - model_ensemble - INFO - 开始训练集成模型，方法: voting
2025-07-15 21:29:13 - model_ensemble - INFO -     voting_hard - 准确率: 0.9333, F1: 0.9333
2025-07-15 21:29:13 - model_ensemble - INFO - ============================================================
2025-07-15 21:29:13 - model_ensemble - INFO - 集成学习结果总结
2025-07-15 21:29:13 - model_ensemble - INFO - ============================================================
2025-07-15 21:29:13 - model_ensemble - INFO - 最佳集成模型: voting_soft
2025-07-15 21:29:13 - model_ensemble - INFO - 最佳F1分数: 0.9500
2025-07-15 21:29:13 - model_ensemble - INFO - 最佳准确率: 0.9500
2025-07-15 21:29:13 - model_ensemble - INFO - 
所有集成模型性能对比:
2025-07-15 21:29:13 - model_ensemble - INFO -   voting_soft     - 准确率: 0.9500, 精确率: 0.9505, 召回率: 0.9500, F1: 0.9500, AUC: 0.9778
2025-07-15 21:29:13 - model_ensemble - INFO -   voting_hard     - 准确率: 0.9333, 精确率: 0.9333, 召回率: 0.9333, F1: 0.9333, AUC: 0.0000
2025-07-15 21:29:13 - model_ensemble - INFO - 步骤3: 保存集成学习结果
2025-07-15 21:29:14 - model_ensemble - INFO - 集成学习结果已保存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\ensemble\ensemble_results_20250715_212913.joblib
2025-07-15 21:29:14 - model_ensemble - INFO - Ensemble report saved to: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\ensemble
2025-07-15 21:29:14 - model_ensemble - INFO - 步骤4: 生成SHAP可解释性分析
2025-07-15 21:29:14 - model_ensemble - INFO - 为 voting_soft 生成SHAP分析
2025-07-15 21:29:19 - model_ensemble - INFO -   voting_soft SHAP分析完成
2025-07-15 21:29:19 - model_ensemble - INFO - 为 voting_hard 生成SHAP分析
2025-07-15 21:29:26 - model_ensemble - INFO -   voting_hard SHAP分析完成
2025-07-15 21:32:28 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-07-15 21:32:49 - model_ensemble - INFO - ============================================================
2025-07-15 21:32:49 - model_ensemble - INFO - 开始运行集成学习管道
2025-07-15 21:32:49 - model_ensemble - INFO - ============================================================
2025-07-15 21:32:49 - model_ensemble - INFO - 基础模型: ['RandomForest', 'XGBoost', 'SVM', 'KNN']
2025-07-15 21:32:49 - model_ensemble - INFO - 集成方法: ['voting', 'stacking']
2025-07-15 21:32:49 - model_ensemble - INFO - 步骤1: 训练基础模型
2025-07-15 21:32:49 - model_ensemble - INFO - 训练基础模型: RandomForest
2025-07-15 21:32:49 - model_ensemble - INFO -   RandomForest 训练完成
2025-07-15 21:32:49 - model_ensemble - INFO - 训练基础模型: XGBoost
2025-07-15 21:32:49 - model_ensemble - INFO -   XGBoost 训练完成
2025-07-15 21:32:49 - model_ensemble - INFO - 训练基础模型: SVM
2025-07-15 21:32:49 - model_ensemble - INFO -   SVM 训练完成
2025-07-15 21:32:49 - model_ensemble - INFO - 训练基础模型: KNN
2025-07-15 21:32:49 - model_ensemble - INFO -   KNN 训练完成
2025-07-15 21:32:49 - model_ensemble - INFO - 成功训练了 4 个基础模型
2025-07-15 21:32:49 - model_ensemble - INFO - 步骤2: 运行集成方法 - voting
2025-07-15 21:32:49 - model_ensemble - INFO -   训练 voting_soft 集成模型
2025-07-15 21:32:49 - model_ensemble - INFO - 开始训练集成模型，方法: voting
2025-07-15 21:32:49 - model_ensemble - INFO -     voting_soft - 准确率: 0.8750, F1: 0.8744
2025-07-15 21:32:49 - model_ensemble - INFO -   训练 voting_hard 集成模型
2025-07-15 21:32:49 - model_ensemble - INFO - 开始训练集成模型，方法: voting
2025-07-15 21:32:50 - model_ensemble - INFO -     voting_hard - 准确率: 0.8750, F1: 0.8728
2025-07-15 21:32:50 - model_ensemble - INFO - 步骤2: 运行集成方法 - stacking
2025-07-15 21:32:50 - model_ensemble - INFO - 开始训练集成模型，方法: stacking
2025-07-15 21:32:50 - model_ensemble - INFO -   stacking - 准确率: 0.8750, F1: 0.8744
2025-07-15 21:32:50 - model_ensemble - INFO - ============================================================
2025-07-15 21:32:50 - model_ensemble - INFO - 集成学习结果总结
2025-07-15 21:32:50 - model_ensemble - INFO - ============================================================
2025-07-15 21:32:50 - model_ensemble - INFO - 最佳集成模型: voting_soft
2025-07-15 21:32:50 - model_ensemble - INFO - 最佳F1分数: 0.8744
2025-07-15 21:32:50 - model_ensemble - INFO - 最佳准确率: 0.8750
2025-07-15 21:32:50 - model_ensemble - INFO - 
所有集成模型性能对比:
2025-07-15 21:32:50 - model_ensemble - INFO -   voting_soft     - 准确率: 0.8750, 精确率: 0.8750, 召回率: 0.8750, F1: 0.8744, AUC: 0.9540
2025-07-15 21:32:50 - model_ensemble - INFO -   voting_hard     - 准确率: 0.8750, 精确率: 0.8812, 召回率: 0.8750, F1: 0.8728, AUC: 0.0000
2025-07-15 21:32:50 - model_ensemble - INFO -   stacking        - 准确率: 0.8750, 精确率: 0.8750, 召回率: 0.8750, F1: 0.8744, AUC: 0.9565
2025-07-15 21:32:50 - model_ensemble - INFO - 步骤3: 保存集成学习结果
2025-07-15 21:32:50 - model_ensemble - INFO - 集成学习结果已保存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\ensemble\ensemble_results_20250715_213250.joblib
2025-07-15 21:32:50 - model_ensemble - INFO - Ensemble report saved to: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\ensemble
2025-07-15 21:32:50 - model_ensemble - INFO - 步骤4: 生成SHAP可解释性分析
2025-07-15 21:32:50 - model_ensemble - INFO - 为 voting_soft 生成SHAP分析
2025-07-15 21:32:58 - model_ensemble - INFO -   voting_soft SHAP分析完成
2025-07-15 21:32:58 - model_ensemble - INFO - 为 voting_hard 生成SHAP分析
2025-07-15 21:33:12 - model_ensemble - INFO -   voting_hard SHAP分析完成
2025-07-15 21:33:12 - model_ensemble - INFO - 为 stacking 生成SHAP分析
2025-07-15 21:33:17 - model_ensemble - INFO -   stacking SHAP分析完成
2025-07-15 21:45:17 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-07-15 21:45:55 - model_ensemble - INFO - ============================================================
2025-07-15 21:45:55 - model_ensemble - INFO - 开始运行集成学习管道
2025-07-15 21:45:55 - model_ensemble - INFO - ============================================================
2025-07-15 21:45:55 - model_ensemble - INFO - 基础模型: ['RandomForest', 'XGBoost', 'LightGBM', 'Logistic']
2025-07-15 21:45:55 - model_ensemble - INFO - 集成方法: ['voting', 'stacking']
2025-07-15 21:45:55 - model_ensemble - INFO - 步骤1: 训练基础模型
2025-07-15 21:45:55 - model_ensemble - INFO - 训练基础模型: RandomForest
2025-07-15 21:45:55 - model_ensemble - INFO -   RandomForest 训练完成
2025-07-15 21:45:55 - model_ensemble - INFO - 训练基础模型: XGBoost
2025-07-15 21:45:55 - model_ensemble - INFO -   XGBoost 训练完成
2025-07-15 21:45:55 - model_ensemble - INFO - 训练基础模型: LightGBM
2025-07-15 21:45:57 - model_ensemble - INFO -   LightGBM 训练完成
2025-07-15 21:45:57 - model_ensemble - INFO - 训练基础模型: Logistic
2025-07-15 21:45:57 - model_ensemble - INFO -   Logistic 训练完成
2025-07-15 21:45:57 - model_ensemble - INFO - 成功训练了 4 个基础模型
2025-07-15 21:45:57 - model_ensemble - INFO - 步骤2: 运行集成方法 - voting
2025-07-15 21:45:57 - model_ensemble - INFO -   训练 voting_soft 集成模型
2025-07-15 21:45:57 - model_ensemble - INFO - 开始训练集成模型，方法: voting
2025-07-15 21:45:57 - model_ensemble - INFO -     voting_soft - 准确率: 0.7750, F1: 0.7740
2025-07-15 21:45:57 - model_ensemble - INFO -   训练 voting_hard 集成模型
2025-07-15 21:45:57 - model_ensemble - INFO - 开始训练集成模型，方法: voting
2025-07-15 21:45:57 - model_ensemble - INFO -     voting_hard - 准确率: 0.7250, F1: 0.7148
2025-07-15 21:45:57 - model_ensemble - INFO - 步骤2: 运行集成方法 - stacking
2025-07-15 21:45:57 - model_ensemble - INFO - 开始训练集成模型，方法: stacking
2025-07-15 21:45:59 - model_ensemble - INFO -   stacking - 准确率: 0.7750, F1: 0.7740
2025-07-15 21:45:59 - model_ensemble - INFO - ============================================================
2025-07-15 21:45:59 - model_ensemble - INFO - 集成学习结果总结
2025-07-15 21:45:59 - model_ensemble - INFO - ============================================================
2025-07-15 21:45:59 - model_ensemble - INFO - 最佳集成模型: voting_soft
2025-07-15 21:45:59 - model_ensemble - INFO - 最佳F1分数: 0.7740
2025-07-15 21:45:59 - model_ensemble - INFO - 最佳准确率: 0.7750
2025-07-15 21:45:59 - model_ensemble - INFO - 
所有集成模型性能对比:
2025-07-15 21:45:59 - model_ensemble - INFO -   voting_soft     - 准确率: 0.7750, 精确率: 0.7740, 召回率: 0.7750, F1: 0.7740, AUC: 0.8312
2025-07-15 21:45:59 - model_ensemble - INFO -   voting_hard     - 准确率: 0.7250, 精确率: 0.7295, 召回率: 0.7250, F1: 0.7148, AUC: 0.0000
2025-07-15 21:45:59 - model_ensemble - INFO -   stacking        - 准确率: 0.7750, 精确率: 0.7740, 召回率: 0.7750, F1: 0.7740, AUC: 0.8389
2025-07-15 21:45:59 - model_ensemble - INFO - 步骤3: 保存集成学习结果
2025-07-15 21:45:59 - model_ensemble - INFO - 集成学习结果已保存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\ensemble\ensemble_results_20250715_214559.joblib
2025-07-15 21:45:59 - model_ensemble - INFO - Ensemble report saved to: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\ensemble
2025-07-15 21:45:59 - model_ensemble - INFO - 步骤4: 生成SHAP可解释性分析
2025-07-15 21:45:59 - model_ensemble - INFO - 为 voting_soft 生成SHAP分析
2025-07-15 21:46:04 - model_ensemble - INFO -   voting_soft SHAP分析完成
2025-07-15 21:46:04 - model_ensemble - INFO - 为 voting_hard 生成SHAP分析
2025-07-15 21:46:09 - model_ensemble - INFO -   voting_hard SHAP分析完成
2025-07-15 21:46:09 - model_ensemble - INFO - 为 stacking 生成SHAP分析
2025-07-15 21:46:12 - model_ensemble - INFO -   stacking SHAP分析完成
2025-07-15 21:59:34 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-07-15 21:59:34 - model_ensemble - INFO - ============================================================
2025-07-15 21:59:34 - model_ensemble - INFO - 开始运行集成学习管道
2025-07-15 21:59:34 - model_ensemble - INFO - ============================================================
2025-07-15 21:59:34 - model_ensemble - INFO - 基础模型: ['RandomForest', 'XGBoost']
2025-07-15 21:59:34 - model_ensemble - INFO - 集成方法: ['voting']
2025-07-15 21:59:34 - model_ensemble - INFO - 步骤1: 训练基础模型
2025-07-15 21:59:34 - model_ensemble - INFO - 训练基础模型: RandomForest
2025-07-15 21:59:34 - model_ensemble - INFO -   RandomForest 训练完成
2025-07-15 21:59:34 - model_ensemble - INFO - 训练基础模型: XGBoost
2025-07-15 21:59:34 - model_ensemble - INFO -   XGBoost 训练完成
2025-07-15 21:59:34 - model_ensemble - INFO - 成功训练了 2 个基础模型
2025-07-15 21:59:34 - model_ensemble - INFO - 步骤2: 运行集成方法 - voting
2025-07-15 21:59:34 - model_ensemble - INFO -   训练 voting_soft 集成模型
2025-07-15 21:59:34 - model_ensemble - INFO - 开始训练集成模型，方法: voting
2025-07-15 21:59:34 - model_ensemble - INFO -     voting_soft - 准确率: 0.9222, F1: 0.9221
2025-07-15 21:59:34 - model_ensemble - INFO -   训练 voting_hard 集成模型
2025-07-15 21:59:34 - model_ensemble - INFO - 开始训练集成模型，方法: voting
2025-07-15 21:59:34 - model_ensemble - INFO -     voting_hard - 准确率: 0.9556, F1: 0.9556
2025-07-15 21:59:34 - model_ensemble - INFO - ============================================================
2025-07-15 21:59:34 - model_ensemble - INFO - 集成学习结果总结
2025-07-15 21:59:34 - model_ensemble - INFO - ============================================================
2025-07-15 21:59:34 - model_ensemble - INFO - 最佳集成模型: voting_hard
2025-07-15 21:59:34 - model_ensemble - INFO - 最佳F1分数: 0.9556
2025-07-15 21:59:34 - model_ensemble - INFO - 最佳准确率: 0.9556
2025-07-15 21:59:34 - model_ensemble - INFO - 
所有集成模型性能对比:
2025-07-15 21:59:34 - model_ensemble - INFO -   voting_soft     - 准确率: 0.9222, 精确率: 0.9240, 召回率: 0.9222, F1: 0.9221, AUC: 0.9911
2025-07-15 21:59:34 - model_ensemble - INFO -   voting_hard     - 准确率: 0.9556, 精确率: 0.9556, 召回率: 0.9556, F1: 0.9556, AUC: 0.0000
2025-07-15 21:59:34 - model_ensemble - INFO - 步骤3: 保存集成学习结果
2025-07-15 21:59:35 - model_ensemble - INFO - 集成学习结果已保存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\ensemble\ensemble_results_20250715_215934.joblib
2025-07-15 21:59:35 - model_ensemble - INFO - Ensemble report saved to: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\ensemble
2025-07-15 21:59:35 - model_ensemble - INFO - 步骤4: 生成SHAP可解释性分析
2025-07-15 21:59:35 - model_ensemble - INFO - 为 voting_soft 生成SHAP分析
2025-07-15 21:59:35 - model_ensemble - WARNING -   voting_soft SHAP分析未生成任何图表
2025-07-15 21:59:35 - model_ensemble - INFO -   voting_soft SHAP分析完成
2025-07-15 21:59:35 - model_ensemble - INFO - 为 voting_hard 生成SHAP分析
2025-07-15 21:59:35 - model_ensemble - WARNING -   voting_hard SHAP分析未生成任何图表
2025-07-15 21:59:35 - model_ensemble - INFO -   voting_hard SHAP分析完成
2025-07-15 22:03:27 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-07-15 22:03:27 - model_ensemble - INFO - ============================================================
2025-07-15 22:03:27 - model_ensemble - INFO - 开始运行集成学习管道
2025-07-15 22:03:27 - model_ensemble - INFO - ============================================================
2025-07-15 22:03:27 - model_ensemble - INFO - 基础模型: ['RandomForest', 'XGBoost']
2025-07-15 22:03:27 - model_ensemble - INFO - 集成方法: ['voting', 'stacking']
2025-07-15 22:03:27 - model_ensemble - INFO - 步骤1: 训练基础模型
2025-07-15 22:03:27 - model_ensemble - INFO - 训练基础模型: RandomForest
2025-07-15 22:03:27 - model_ensemble - INFO -   RandomForest 训练完成
2025-07-15 22:03:27 - model_ensemble - INFO - 训练基础模型: XGBoost
2025-07-15 22:03:27 - model_ensemble - INFO -   XGBoost 训练完成
2025-07-15 22:03:27 - model_ensemble - INFO - 成功训练了 2 个基础模型
2025-07-15 22:03:27 - model_ensemble - INFO - 步骤2: 运行集成方法 - voting
2025-07-15 22:03:27 - model_ensemble - INFO -   训练 voting_soft 集成模型
2025-07-15 22:03:27 - model_ensemble - INFO - 开始训练集成模型，方法: voting
2025-07-15 22:03:28 - model_ensemble - INFO -     voting_soft - 准确率: 0.9500, F1: 0.9500
2025-07-15 22:03:28 - model_ensemble - INFO -   训练 voting_hard 集成模型
2025-07-15 22:03:28 - model_ensemble - INFO - 开始训练集成模型，方法: voting
2025-07-15 22:03:28 - model_ensemble - INFO -     voting_hard - 准确率: 0.9333, F1: 0.9333
2025-07-15 22:03:28 - model_ensemble - INFO - 步骤2: 运行集成方法 - stacking
2025-07-15 22:03:28 - model_ensemble - INFO - 开始训练集成模型，方法: stacking
2025-07-15 22:03:28 - model_ensemble - INFO -   stacking - 准确率: 0.9333, F1: 0.9333
2025-07-15 22:03:28 - model_ensemble - INFO - ============================================================
2025-07-15 22:03:28 - model_ensemble - INFO - 集成学习结果总结
2025-07-15 22:03:28 - model_ensemble - INFO - ============================================================
2025-07-15 22:03:28 - model_ensemble - INFO - 最佳集成模型: voting_soft
2025-07-15 22:03:28 - model_ensemble - INFO - 最佳F1分数: 0.9500
2025-07-15 22:03:28 - model_ensemble - INFO - 最佳准确率: 0.9500
2025-07-15 22:03:28 - model_ensemble - INFO - 
所有集成模型性能对比:
2025-07-15 22:03:28 - model_ensemble - INFO -   voting_soft     - 准确率: 0.9500, 精确率: 0.9505, 召回率: 0.9500, F1: 0.9500, AUC: 0.9778
2025-07-15 22:03:28 - model_ensemble - INFO -   voting_hard     - 准确率: 0.9333, 精确率: 0.9333, 召回率: 0.9333, F1: 0.9333, AUC: 0.0000
2025-07-15 22:03:28 - model_ensemble - INFO -   stacking        - 准确率: 0.9333, 精确率: 0.9333, 召回率: 0.9333, F1: 0.9333, AUC: 0.9778
2025-07-15 22:03:28 - model_ensemble - INFO - 步骤3: 保存集成学习结果
2025-07-15 22:03:29 - model_ensemble - INFO - 集成学习结果已保存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\ensemble\ensemble_results_20250715_220328.joblib
2025-07-15 22:03:29 - model_ensemble - INFO - Ensemble report saved to: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\ensemble
2025-07-15 22:03:29 - model_ensemble - INFO - 步骤4: 生成SHAP可解释性分析
2025-07-15 22:03:29 - model_ensemble - INFO - 为 voting_soft 生成SHAP分析
2025-07-15 22:03:36 - model_ensemble - INFO -   voting_soft 完整SHAP分析完成
2025-07-15 22:03:36 - model_ensemble - INFO -     生成的图表类型: ['summary', 'importance', 'force', 'waterfall', 'dependence', 'decision']
2025-07-15 22:03:36 - model_ensemble - INFO -     summary: ['dot', 'bar']
2025-07-15 22:03:36 - model_ensemble - INFO -     importance: 1 个文件
2025-07-15 22:03:36 - model_ensemble - INFO -     force: 3 个文件
2025-07-15 22:03:36 - model_ensemble - INFO -     waterfall: 3 个文件
2025-07-15 22:03:36 - model_ensemble - INFO -     dependence: 5 个文件
2025-07-15 22:03:36 - model_ensemble - INFO -     decision: 1 个文件
2025-07-15 22:03:36 - model_ensemble - INFO -   voting_soft SHAP分析完成
2025-07-15 22:03:36 - model_ensemble - INFO - 为 voting_hard 生成SHAP分析
2025-07-15 22:03:48 - model_ensemble - INFO -   voting_hard 完整SHAP分析完成
2025-07-15 22:03:48 - model_ensemble - INFO -     生成的图表类型: ['summary', 'importance', 'force', 'waterfall', 'dependence', 'decision']
2025-07-15 22:03:48 - model_ensemble - INFO -     summary: ['dot', 'bar']
2025-07-15 22:03:48 - model_ensemble - INFO -     importance: 1 个文件
2025-07-15 22:03:48 - model_ensemble - INFO -     force: 3 个文件
2025-07-15 22:03:48 - model_ensemble - INFO -     waterfall: 3 个文件
2025-07-15 22:03:48 - model_ensemble - INFO -     dependence: 5 个文件
2025-07-15 22:03:48 - model_ensemble - INFO -     decision: 1 个文件
2025-07-15 22:03:48 - model_ensemble - INFO -   voting_hard SHAP分析完成
2025-07-15 22:03:48 - model_ensemble - INFO - 为 stacking 生成SHAP分析
2025-07-15 22:03:55 - model_ensemble - INFO -   stacking 完整SHAP分析完成
2025-07-15 22:03:55 - model_ensemble - INFO -     生成的图表类型: ['summary', 'importance', 'force', 'waterfall', 'dependence', 'decision']
2025-07-15 22:03:55 - model_ensemble - INFO -     summary: ['dot', 'bar']
2025-07-15 22:03:55 - model_ensemble - INFO -     importance: 1 个文件
2025-07-15 22:03:55 - model_ensemble - INFO -     force: 3 个文件
2025-07-15 22:03:55 - model_ensemble - INFO -     waterfall: 3 个文件
2025-07-15 22:03:55 - model_ensemble - INFO -     dependence: 5 个文件
2025-07-15 22:03:55 - model_ensemble - INFO -     decision: 1 个文件
2025-07-15 22:03:55 - model_ensemble - INFO -   stacking SHAP分析完成
2025-07-15 22:06:46 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-07-15 22:07:24 - model_ensemble - INFO - ============================================================
2025-07-15 22:07:24 - model_ensemble - INFO - 开始运行集成学习管道
2025-07-15 22:07:24 - model_ensemble - INFO - ============================================================
2025-07-15 22:07:24 - model_ensemble - INFO - 基础模型: ['RandomForest', 'XGBoost', 'LightGBM', 'Logistic', 'SVM']
2025-07-15 22:07:24 - model_ensemble - INFO - 集成方法: ['voting', 'stacking']
2025-07-15 22:07:24 - model_ensemble - INFO - 步骤1: 训练基础模型
2025-07-15 22:07:24 - model_ensemble - INFO - 训练基础模型: RandomForest
2025-07-15 22:07:24 - model_ensemble - INFO -   RandomForest 训练完成
2025-07-15 22:07:24 - model_ensemble - INFO - 训练基础模型: XGBoost
2025-07-15 22:07:24 - model_ensemble - INFO -   XGBoost 训练完成
2025-07-15 22:07:24 - model_ensemble - INFO - 训练基础模型: LightGBM
2025-07-15 22:07:26 - model_ensemble - INFO -   LightGBM 训练完成
2025-07-15 22:07:26 - model_ensemble - INFO - 训练基础模型: Logistic
2025-07-15 22:07:26 - model_ensemble - INFO -   Logistic 训练完成
2025-07-15 22:07:26 - model_ensemble - INFO - 训练基础模型: SVM
2025-07-15 22:07:26 - model_ensemble - INFO -   SVM 训练完成
2025-07-15 22:07:26 - model_ensemble - INFO - 成功训练了 5 个基础模型
2025-07-15 22:07:26 - model_ensemble - INFO - 步骤2: 运行集成方法 - voting
2025-07-15 22:07:26 - model_ensemble - INFO -   训练 voting_soft 集成模型
2025-07-15 22:07:26 - model_ensemble - INFO - 开始训练集成模型，方法: voting
2025-07-15 22:07:26 - model_ensemble - INFO -     voting_soft - 准确率: 0.6667, F1: 0.6667
2025-07-15 22:07:26 - model_ensemble - INFO -   训练 voting_hard 集成模型
2025-07-15 22:07:26 - model_ensemble - INFO - 开始训练集成模型，方法: voting
2025-07-15 22:07:27 - model_ensemble - INFO -     voting_hard - 准确率: 0.7222, F1: 0.7196
2025-07-15 22:07:27 - model_ensemble - INFO - 步骤2: 运行集成方法 - stacking
2025-07-15 22:07:27 - model_ensemble - INFO - 开始训练集成模型，方法: stacking
2025-07-15 22:07:29 - model_ensemble - INFO -   stacking - 准确率: 0.5000, F1: 0.4749
2025-07-15 22:07:29 - model_ensemble - INFO - ============================================================
2025-07-15 22:07:29 - model_ensemble - INFO - 集成学习结果总结
2025-07-15 22:07:29 - model_ensemble - INFO - ============================================================
2025-07-15 22:07:29 - model_ensemble - INFO - 最佳集成模型: voting_hard
2025-07-15 22:07:29 - model_ensemble - INFO - 最佳F1分数: 0.7196
2025-07-15 22:07:29 - model_ensemble - INFO - 最佳准确率: 0.7222
2025-07-15 22:07:29 - model_ensemble - INFO - 
所有集成模型性能对比:
2025-07-15 22:07:29 - model_ensemble - INFO -   voting_soft     - 准确率: 0.6667, 精确率: 0.6833, 召回率: 0.6667, F1: 0.6667, AUC: 0.6500
2025-07-15 22:07:29 - model_ensemble - INFO -   voting_hard     - 准确率: 0.7222, 精确率: 0.7590, 召回率: 0.7222, F1: 0.7196, AUC: 0.0000
2025-07-15 22:07:29 - model_ensemble - INFO -   stacking        - 准确率: 0.5000, 精确率: 0.4769, 召回率: 0.5000, F1: 0.4749, AUC: 0.6625
2025-07-15 22:07:29 - model_ensemble - INFO - 步骤3: 保存集成学习结果
2025-07-15 22:07:29 - model_ensemble - INFO - 集成学习结果已保存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\ensemble\ensemble_results_20250715_220729.joblib
2025-07-15 22:07:29 - model_ensemble - INFO - Ensemble report saved to: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\ensemble
2025-07-15 22:07:29 - model_ensemble - INFO - 步骤4: 生成SHAP可解释性分析
2025-07-15 22:07:29 - model_ensemble - INFO - 为 voting_soft 生成SHAP分析
2025-07-15 22:07:32 - model_ensemble - INFO -   voting_soft 完整SHAP分析完成
2025-07-15 22:07:32 - model_ensemble - INFO -     生成的图表类型: ['summary', 'importance', 'force', 'waterfall', 'dependence', 'decision']
2025-07-15 22:07:32 - model_ensemble - INFO -     summary: ['dot', 'bar']
2025-07-15 22:07:32 - model_ensemble - INFO -     importance: 1 个文件
2025-07-15 22:07:32 - model_ensemble - INFO -     force: 3 个文件
2025-07-15 22:07:32 - model_ensemble - INFO -     waterfall: 3 个文件
2025-07-15 22:07:32 - model_ensemble - INFO -     dependence: 3 个文件
2025-07-15 22:07:32 - model_ensemble - INFO -     decision: 1 个文件
2025-07-15 22:07:32 - model_ensemble - INFO -   voting_soft SHAP分析完成
2025-07-15 22:07:32 - model_ensemble - INFO - 为 voting_hard 生成SHAP分析
2025-07-15 22:07:36 - model_ensemble - INFO -   voting_hard 完整SHAP分析完成
2025-07-15 22:07:36 - model_ensemble - INFO -     生成的图表类型: ['summary', 'importance', 'force', 'waterfall', 'dependence', 'decision']
2025-07-15 22:07:36 - model_ensemble - INFO -     summary: ['dot', 'bar']
2025-07-15 22:07:36 - model_ensemble - INFO -     importance: 1 个文件
2025-07-15 22:07:36 - model_ensemble - INFO -     force: 3 个文件
2025-07-15 22:07:36 - model_ensemble - INFO -     waterfall: 3 个文件
2025-07-15 22:07:36 - model_ensemble - INFO -     dependence: 3 个文件
2025-07-15 22:07:36 - model_ensemble - INFO -     decision: 1 个文件
2025-07-15 22:07:36 - model_ensemble - INFO -   voting_hard SHAP分析完成
2025-07-15 22:07:36 - model_ensemble - INFO - 为 stacking 生成SHAP分析
2025-07-15 22:07:39 - model_ensemble - INFO -   stacking 完整SHAP分析完成
2025-07-15 22:07:39 - model_ensemble - INFO -     生成的图表类型: ['summary', 'importance', 'force', 'waterfall', 'dependence', 'decision']
2025-07-15 22:07:39 - model_ensemble - INFO -     summary: ['dot', 'bar']
2025-07-15 22:07:39 - model_ensemble - INFO -     importance: 1 个文件
2025-07-15 22:07:39 - model_ensemble - INFO -     force: 3 个文件
2025-07-15 22:07:39 - model_ensemble - INFO -     waterfall: 3 个文件
2025-07-15 22:07:39 - model_ensemble - INFO -     dependence: 3 个文件
2025-07-15 22:07:39 - model_ensemble - INFO -     decision: 1 个文件
2025-07-15 22:07:39 - model_ensemble - INFO -   stacking SHAP分析完成
2025-07-15 22:12:57 - model_ensemble - INFO - ============================================================
2025-07-15 22:12:57 - model_ensemble - INFO - 开始运行集成学习管道
2025-07-15 22:12:57 - model_ensemble - INFO - ============================================================
2025-07-15 22:12:57 - model_ensemble - INFO - 基础模型: ['RandomForest', 'XGBoost', 'LightGBM', 'Logistic', 'SVM']
2025-07-15 22:12:57 - model_ensemble - INFO - 集成方法: ['voting', 'stacking']
2025-07-15 22:12:57 - model_ensemble - INFO - 步骤1: 训练基础模型
2025-07-15 22:12:57 - model_ensemble - INFO - 训练基础模型: RandomForest
2025-07-15 22:12:58 - model_ensemble - INFO -   RandomForest 训练完成
2025-07-15 22:12:58 - model_ensemble - INFO - 训练基础模型: XGBoost
2025-07-15 22:12:58 - model_ensemble - INFO -   XGBoost 训练完成
2025-07-15 22:12:58 - model_ensemble - INFO - 训练基础模型: LightGBM
2025-07-15 22:12:58 - model_ensemble - INFO -   LightGBM 训练完成
2025-07-15 22:12:58 - model_ensemble - INFO - 训练基础模型: Logistic
2025-07-15 22:12:58 - model_ensemble - INFO -   Logistic 训练完成
2025-07-15 22:12:58 - model_ensemble - INFO - 训练基础模型: SVM
2025-07-15 22:12:58 - model_ensemble - INFO -   SVM 训练完成
2025-07-15 22:12:58 - model_ensemble - INFO - 成功训练了 5 个基础模型
2025-07-15 22:12:58 - model_ensemble - INFO - 步骤2: 运行集成方法 - voting
2025-07-15 22:12:58 - model_ensemble - INFO -   训练 voting_soft 集成模型
2025-07-15 22:12:58 - model_ensemble - INFO - 开始训练集成模型，方法: voting
2025-07-15 22:12:58 - model_ensemble - INFO -     voting_soft - 准确率: 0.8750, F1: 0.8744
2025-07-15 22:12:58 - model_ensemble - INFO -   训练 voting_hard 集成模型
2025-07-15 22:12:58 - model_ensemble - INFO - 开始训练集成模型，方法: voting
2025-07-15 22:12:58 - model_ensemble - INFO -     voting_hard - 准确率: 0.8750, F1: 0.8744
2025-07-15 22:12:58 - model_ensemble - INFO - 步骤2: 运行集成方法 - stacking
2025-07-15 22:12:58 - model_ensemble - INFO - 开始训练集成模型，方法: stacking
2025-07-15 22:13:00 - model_ensemble - INFO -   stacking - 准确率: 0.8750, F1: 0.8744
2025-07-15 22:13:00 - model_ensemble - INFO - ============================================================
2025-07-15 22:13:00 - model_ensemble - INFO - 集成学习结果总结
2025-07-15 22:13:00 - model_ensemble - INFO - ============================================================
2025-07-15 22:13:00 - model_ensemble - INFO - 最佳集成模型: voting_soft
2025-07-15 22:13:00 - model_ensemble - INFO - 最佳F1分数: 0.8744
2025-07-15 22:13:00 - model_ensemble - INFO - 最佳准确率: 0.8750
2025-07-15 22:13:00 - model_ensemble - INFO - 
所有集成模型性能对比:
2025-07-15 22:13:00 - model_ensemble - INFO -   voting_soft     - 准确率: 0.8750, 精确率: 0.8750, 召回率: 0.8750, F1: 0.8744, AUC: 0.9616
2025-07-15 22:13:00 - model_ensemble - INFO -   voting_hard     - 准确率: 0.8750, 精确率: 0.8750, 召回率: 0.8750, F1: 0.8744, AUC: 0.0000
2025-07-15 22:13:00 - model_ensemble - INFO -   stacking        - 准确率: 0.8750, 精确率: 0.8750, 召回率: 0.8750, F1: 0.8744, AUC: 0.9540
2025-07-15 22:13:00 - model_ensemble - INFO - 步骤3: 保存集成学习结果
2025-07-15 22:13:00 - model_ensemble - INFO - 集成学习结果已保存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\ensemble\ensemble_results_20250715_221300.joblib
2025-07-15 22:13:00 - model_ensemble - INFO - Ensemble report saved to: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\ensemble
2025-07-15 22:13:00 - model_ensemble - INFO - 步骤4: 生成SHAP可解释性分析
2025-07-15 22:13:00 - model_ensemble - INFO - 为 voting_soft 生成SHAP分析
2025-07-15 22:13:08 - model_ensemble - INFO -   voting_soft 完整SHAP分析完成
2025-07-15 22:13:08 - model_ensemble - INFO -     生成的图表类型: ['summary', 'importance', 'force', 'waterfall', 'dependence', 'decision']
2025-07-15 22:13:08 - model_ensemble - INFO -     summary: ['dot', 'bar']
2025-07-15 22:13:08 - model_ensemble - INFO -     importance: 1 个文件
2025-07-15 22:13:08 - model_ensemble - INFO -     force: 3 个文件
2025-07-15 22:13:08 - model_ensemble - INFO -     waterfall: 3 个文件
2025-07-15 22:13:08 - model_ensemble - INFO -     dependence: 5 个文件
2025-07-15 22:13:08 - model_ensemble - INFO -     decision: 1 个文件
2025-07-15 22:13:08 - model_ensemble - INFO -   voting_soft SHAP分析完成
2025-07-15 22:13:08 - model_ensemble - INFO - 为 voting_hard 生成SHAP分析
2025-07-15 22:13:16 - model_ensemble - INFO -   voting_hard 完整SHAP分析完成
2025-07-15 22:13:16 - model_ensemble - INFO -     生成的图表类型: ['summary', 'importance', 'force', 'waterfall', 'dependence', 'decision']
2025-07-15 22:13:16 - model_ensemble - INFO -     summary: ['dot', 'bar']
2025-07-15 22:13:16 - model_ensemble - INFO -     importance: 1 个文件
2025-07-15 22:13:16 - model_ensemble - INFO -     force: 3 个文件
2025-07-15 22:13:16 - model_ensemble - INFO -     waterfall: 3 个文件
2025-07-15 22:13:16 - model_ensemble - INFO -     dependence: 5 个文件
2025-07-15 22:13:16 - model_ensemble - INFO -     decision: 1 个文件
2025-07-15 22:13:16 - model_ensemble - INFO -   voting_hard SHAP分析完成
2025-07-15 22:13:16 - model_ensemble - INFO - 为 stacking 生成SHAP分析
2025-07-15 22:13:24 - model_ensemble - INFO -   stacking 完整SHAP分析完成
2025-07-15 22:13:24 - model_ensemble - INFO -     生成的图表类型: ['summary', 'importance', 'force', 'waterfall', 'dependence', 'decision']
2025-07-15 22:13:24 - model_ensemble - INFO -     summary: ['dot', 'bar']
2025-07-15 22:13:24 - model_ensemble - INFO -     importance: 1 个文件
2025-07-15 22:13:24 - model_ensemble - INFO -     force: 3 个文件
2025-07-15 22:13:24 - model_ensemble - INFO -     waterfall: 3 个文件
2025-07-15 22:13:24 - model_ensemble - INFO -     dependence: 5 个文件
2025-07-15 22:13:24 - model_ensemble - INFO -     decision: 1 个文件
2025-07-15 22:13:24 - model_ensemble - INFO -   stacking SHAP分析完成
2025-07-15 22:20:53 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-07-15 22:20:53 - model_ensemble - INFO - ============================================================
2025-07-15 22:20:53 - model_ensemble - INFO - 开始运行集成学习管道
2025-07-15 22:20:53 - model_ensemble - INFO - ============================================================
2025-07-15 22:20:53 - model_ensemble - INFO - 基础模型: ['RandomForest']
2025-07-15 22:20:53 - model_ensemble - INFO - 集成方法: ['voting']
2025-07-15 22:20:53 - model_ensemble - INFO - 步骤1: 训练基础模型
2025-07-15 22:20:53 - model_ensemble - INFO - 训练基础模型: RandomForest
2025-07-15 22:20:53 - model_ensemble - INFO -   RandomForest 训练完成
2025-07-15 22:20:53 - model_ensemble - INFO - 成功训练了 1 个基础模型
2025-07-15 22:20:53 - model_ensemble - INFO - 步骤2: 运行集成方法 - voting
2025-07-15 22:20:53 - model_ensemble - INFO -   训练 voting_soft 集成模型
2025-07-15 22:20:53 - model_ensemble - INFO - 开始训练集成模型，方法: voting
2025-07-15 22:20:53 - model_ensemble - INFO -     voting_soft - 准确率: 0.9000, F1: 0.8999
2025-07-15 22:20:53 - model_ensemble - INFO -   训练 voting_hard 集成模型
2025-07-15 22:20:53 - model_ensemble - INFO - 开始训练集成模型，方法: voting
2025-07-15 22:20:53 - model_ensemble - INFO -     voting_hard - 准确率: 0.9000, F1: 0.8999
2025-07-15 22:20:53 - model_ensemble - INFO - ============================================================
2025-07-15 22:20:53 - model_ensemble - INFO - 集成学习结果总结
2025-07-15 22:20:53 - model_ensemble - INFO - ============================================================
2025-07-15 22:20:53 - model_ensemble - INFO - 最佳集成模型: voting_soft
2025-07-15 22:20:53 - model_ensemble - INFO - 最佳F1分数: 0.8999
2025-07-15 22:20:53 - model_ensemble - INFO - 最佳准确率: 0.9000
2025-07-15 22:20:53 - model_ensemble - INFO - 
所有集成模型性能对比:
2025-07-15 22:20:53 - model_ensemble - INFO -   voting_soft     - 准确率: 0.9000, 精确率: 0.9018, 召回率: 0.9000, F1: 0.8999, AUC: 0.9394
2025-07-15 22:20:53 - model_ensemble - INFO -   voting_hard     - 准确率: 0.9000, 精确率: 0.9018, 召回率: 0.9000, F1: 0.8999, AUC: 0.0000
2025-07-15 22:20:53 - model_ensemble - INFO - 步骤3: 保存集成学习结果
2025-07-15 22:20:54 - model_ensemble - INFO - 集成学习结果已保存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\ensemble\ensemble_results_20250715_222053.joblib
2025-07-15 22:20:54 - model_ensemble - INFO - Ensemble report saved to: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\ensemble
2025-07-15 22:20:54 - model_ensemble - INFO - 步骤4: 生成SHAP可解释性分析
2025-07-15 22:20:54 - model_ensemble - INFO - 为 voting_soft 生成SHAP分析
2025-07-15 22:21:06 - model_ensemble - INFO -   voting_soft 完整SHAP分析完成
2025-07-15 22:21:06 - model_ensemble - INFO -     生成的图表类型: ['summary', 'importance', 'force', 'waterfall', 'dependence', 'decision']
2025-07-15 22:21:06 - model_ensemble - INFO -     summary: ['dot', 'bar']
2025-07-15 22:21:06 - model_ensemble - INFO -     importance: 1 个文件
2025-07-15 22:21:06 - model_ensemble - INFO -     force: 3 个文件
2025-07-15 22:21:06 - model_ensemble - INFO -     waterfall: 3 个文件
2025-07-15 22:21:06 - model_ensemble - INFO -     dependence: 5 个文件
2025-07-15 22:21:06 - model_ensemble - INFO -     decision: 1 个文件
2025-07-15 22:21:06 - model_ensemble - INFO -   voting_soft SHAP分析完成
2025-07-15 22:21:06 - model_ensemble - INFO - 为 voting_hard 生成SHAP分析
2025-07-15 22:21:29 - model_ensemble - INFO -   voting_hard 完整SHAP分析完成
2025-07-15 22:21:29 - model_ensemble - INFO -     生成的图表类型: ['summary', 'importance', 'force', 'waterfall', 'dependence', 'decision']
2025-07-15 22:21:29 - model_ensemble - INFO -     summary: ['dot', 'bar']
2025-07-15 22:21:29 - model_ensemble - INFO -     importance: 1 个文件
2025-07-15 22:21:29 - model_ensemble - INFO -     force: 3 个文件
2025-07-15 22:21:29 - model_ensemble - INFO -     waterfall: 3 个文件
2025-07-15 22:21:29 - model_ensemble - INFO -     dependence: 5 个文件
2025-07-15 22:21:29 - model_ensemble - INFO -     decision: 1 个文件
2025-07-15 22:21:29 - model_ensemble - INFO -   voting_hard SHAP分析完成
2025-07-15 22:27:42 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-07-15 22:31:33 - model_ensemble - INFO - ============================================================
2025-07-15 22:31:33 - model_ensemble - INFO - 开始运行集成学习管道
2025-07-15 22:31:33 - model_ensemble - INFO - ============================================================
2025-07-15 22:31:33 - model_ensemble - INFO - 基础模型: ['RandomForest', 'XGBoost', 'LightGBM', 'Logistic', 'SVM', 'KNN']
2025-07-15 22:31:33 - model_ensemble - INFO - 集成方法: ['voting', 'bagging', 'boosting', 'stacking']
2025-07-15 22:31:33 - model_ensemble - INFO - 步骤1: 训练基础模型
2025-07-15 22:31:33 - model_ensemble - INFO - 训练基础模型: RandomForest
2025-07-15 22:31:34 - model_ensemble - INFO -   RandomForest 训练完成
2025-07-15 22:31:34 - model_ensemble - INFO - 训练基础模型: XGBoost
2025-07-15 22:31:34 - model_ensemble - INFO -   XGBoost 训练完成
2025-07-15 22:31:34 - model_ensemble - INFO - 训练基础模型: LightGBM
2025-07-15 22:31:34 - model_ensemble - INFO -   LightGBM 训练完成
2025-07-15 22:31:34 - model_ensemble - INFO - 训练基础模型: Logistic
2025-07-15 22:31:34 - model_ensemble - INFO -   Logistic 训练完成
2025-07-15 22:31:34 - model_ensemble - INFO - 训练基础模型: SVM
2025-07-15 22:31:34 - model_ensemble - INFO -   SVM 训练完成
2025-07-15 22:31:34 - model_ensemble - INFO - 训练基础模型: KNN
2025-07-15 22:31:34 - model_ensemble - INFO -   KNN 训练完成
2025-07-15 22:31:34 - model_ensemble - INFO - 成功训练了 6 个基础模型
2025-07-15 22:31:34 - model_ensemble - INFO - 步骤2: 运行集成方法 - voting
2025-07-15 22:31:34 - model_ensemble - INFO -   训练 voting_soft 集成模型
2025-07-15 22:31:34 - model_ensemble - INFO - 开始训练集成模型，方法: voting
2025-07-15 22:31:34 - model_ensemble - INFO -     voting_soft - 准确率: 0.8500, F1: 0.8500
2025-07-15 22:31:34 - model_ensemble - INFO -   训练 voting_hard 集成模型
2025-07-15 22:31:34 - model_ensemble - INFO - 开始训练集成模型，方法: voting
2025-07-15 22:31:35 - model_ensemble - INFO -     voting_hard - 准确率: 0.8500, F1: 0.8484
2025-07-15 22:31:35 - model_ensemble - INFO - 步骤2: 运行集成方法 - bagging
2025-07-15 22:31:35 - model_ensemble - INFO - 开始训练集成模型，方法: bagging
2025-07-15 22:31:35 - model_ensemble - ERROR - 集成方法 bagging 失败: __init__() got an unexpected keyword argument 'base_estimator'
2025-07-15 22:31:35 - model_ensemble - INFO - 步骤2: 运行集成方法 - boosting
2025-07-15 22:31:35 - model_ensemble - INFO - 开始训练集成模型，方法: boosting
2025-07-15 22:31:35 - model_ensemble - ERROR - 集成方法 boosting 失败: __init__() got an unexpected keyword argument 'base_estimator'
2025-07-15 22:31:35 - model_ensemble - INFO - 步骤2: 运行集成方法 - stacking
2025-07-15 22:31:35 - model_ensemble - INFO - 开始训练集成模型，方法: stacking
2025-07-15 22:31:36 - model_ensemble - INFO -   stacking - 准确率: 0.8750, F1: 0.8744
2025-07-15 22:31:36 - model_ensemble - INFO - ============================================================
2025-07-15 22:31:36 - model_ensemble - INFO - 集成学习结果总结
2025-07-15 22:31:36 - model_ensemble - INFO - ============================================================
2025-07-15 22:31:36 - model_ensemble - INFO - 最佳集成模型: stacking
2025-07-15 22:31:36 - model_ensemble - INFO - 最佳F1分数: 0.8744
2025-07-15 22:31:36 - model_ensemble - INFO - 最佳准确率: 0.8750
2025-07-15 22:31:36 - model_ensemble - INFO - 
所有集成模型性能对比:
2025-07-15 22:31:36 - model_ensemble - INFO -   voting_soft     - 准确率: 0.8500, 精确率: 0.8500, 召回率: 0.8500, F1: 0.8500, AUC: 0.9591
2025-07-15 22:31:36 - model_ensemble - INFO -   voting_hard     - 准确率: 0.8500, 精确率: 0.8513, 召回率: 0.8500, F1: 0.8484, AUC: 0.0000
2025-07-15 22:31:36 - model_ensemble - INFO -   stacking        - 准确率: 0.8750, 精确率: 0.8750, 召回率: 0.8750, F1: 0.8744, AUC: 0.9540
2025-07-15 22:31:36 - model_ensemble - INFO - 步骤3: 保存集成学习结果
2025-07-15 22:31:37 - model_ensemble - INFO - 集成学习结果已保存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\ensemble\ensemble_results_20250715_223136.joblib
2025-07-15 22:31:37 - model_ensemble - INFO - Ensemble report saved to: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\ensemble
2025-07-15 22:31:37 - model_ensemble - INFO - 步骤4: 生成SHAP可解释性分析
2025-07-15 22:31:37 - model_ensemble - INFO - 为 voting_soft 生成SHAP分析
2025-07-15 22:31:45 - model_ensemble - INFO -   voting_soft 完整SHAP分析完成
2025-07-15 22:31:45 - model_ensemble - INFO -     生成的图表类型: ['summary', 'importance', 'force', 'waterfall', 'dependence', 'decision']
2025-07-15 22:31:45 - model_ensemble - INFO -     summary: ['dot', 'bar']
2025-07-15 22:31:45 - model_ensemble - INFO -     importance: 1 个文件
2025-07-15 22:31:45 - model_ensemble - INFO -     force: 3 个文件
2025-07-15 22:31:45 - model_ensemble - INFO -     waterfall: 3 个文件
2025-07-15 22:31:45 - model_ensemble - INFO -     dependence: 5 个文件
2025-07-15 22:31:45 - model_ensemble - INFO -     decision: 1 个文件
2025-07-15 22:31:45 - model_ensemble - INFO -   voting_soft SHAP分析完成
2025-07-15 22:31:45 - model_ensemble - INFO - 为 voting_hard 生成SHAP分析
2025-07-15 22:32:05 - model_ensemble - INFO -   voting_hard 完整SHAP分析完成
2025-07-15 22:32:05 - model_ensemble - INFO -     生成的图表类型: ['summary', 'importance', 'force', 'waterfall', 'dependence', 'decision']
2025-07-15 22:32:05 - model_ensemble - INFO -     summary: ['dot', 'bar']
2025-07-15 22:32:05 - model_ensemble - INFO -     importance: 1 个文件
2025-07-15 22:32:05 - model_ensemble - INFO -     force: 3 个文件
2025-07-15 22:32:05 - model_ensemble - INFO -     waterfall: 3 个文件
2025-07-15 22:32:05 - model_ensemble - INFO -     dependence: 5 个文件
2025-07-15 22:32:05 - model_ensemble - INFO -     decision: 1 个文件
2025-07-15 22:32:05 - model_ensemble - INFO -   voting_hard SHAP分析完成
2025-07-15 22:32:05 - model_ensemble - INFO - 为 stacking 生成SHAP分析
2025-07-15 22:32:15 - model_ensemble - INFO -   stacking 完整SHAP分析完成
2025-07-15 22:32:15 - model_ensemble - INFO -     生成的图表类型: ['summary', 'importance', 'force', 'waterfall', 'dependence', 'decision']
2025-07-15 22:32:15 - model_ensemble - INFO -     summary: ['dot', 'bar']
2025-07-15 22:32:15 - model_ensemble - INFO -     importance: 1 个文件
2025-07-15 22:32:15 - model_ensemble - INFO -     force: 3 个文件
2025-07-15 22:32:15 - model_ensemble - INFO -     waterfall: 3 个文件
2025-07-15 22:32:15 - model_ensemble - INFO -     dependence: 5 个文件
2025-07-15 22:32:15 - model_ensemble - INFO -     decision: 1 个文件
2025-07-15 22:32:15 - model_ensemble - INFO -   stacking SHAP分析完成
2025-07-15 22:34:22 - model_ensemble - INFO - ============================================================
2025-07-15 22:34:22 - model_ensemble - INFO - 开始运行集成学习管道
2025-07-15 22:34:22 - model_ensemble - INFO - ============================================================
2025-07-15 22:34:22 - model_ensemble - INFO - 基础模型: ['RandomForest', 'XGBoost', 'LightGBM', 'Logistic', 'SVM', 'KNN']
2025-07-15 22:34:22 - model_ensemble - INFO - 集成方法: ['voting', 'bagging', 'boosting', 'stacking']
2025-07-15 22:34:22 - model_ensemble - INFO - 步骤1: 训练基础模型
2025-07-15 22:34:22 - model_ensemble - INFO - 训练基础模型: RandomForest
2025-07-15 22:34:22 - model_ensemble - INFO -   RandomForest 训练完成
2025-07-15 22:34:22 - model_ensemble - INFO - 训练基础模型: XGBoost
2025-07-15 22:34:22 - model_ensemble - INFO -   XGBoost 训练完成
2025-07-15 22:34:22 - model_ensemble - INFO - 训练基础模型: LightGBM
2025-07-15 22:34:22 - model_ensemble - INFO -   LightGBM 训练完成
2025-07-15 22:34:22 - model_ensemble - INFO - 训练基础模型: Logistic
2025-07-15 22:34:22 - model_ensemble - INFO -   Logistic 训练完成
2025-07-15 22:34:22 - model_ensemble - INFO - 训练基础模型: SVM
2025-07-15 22:34:22 - model_ensemble - INFO -   SVM 训练完成
2025-07-15 22:34:22 - model_ensemble - INFO - 训练基础模型: KNN
2025-07-15 22:34:22 - model_ensemble - INFO -   KNN 训练完成
2025-07-15 22:34:22 - model_ensemble - INFO - 成功训练了 6 个基础模型
2025-07-15 22:34:22 - model_ensemble - INFO - 步骤2: 运行集成方法 - voting
2025-07-15 22:34:22 - model_ensemble - INFO -   训练 voting_soft 集成模型
2025-07-15 22:34:22 - model_ensemble - INFO - 开始训练集成模型，方法: voting
2025-07-15 22:34:23 - model_ensemble - INFO -     voting_soft - 准确率: 0.8500, F1: 0.8500
2025-07-15 22:34:23 - model_ensemble - INFO -   训练 voting_hard 集成模型
2025-07-15 22:34:23 - model_ensemble - INFO - 开始训练集成模型，方法: voting
2025-07-15 22:34:23 - model_ensemble - INFO -     voting_hard - 准确率: 0.8500, F1: 0.8484
2025-07-15 22:34:23 - model_ensemble - INFO - 步骤2: 运行集成方法 - bagging
2025-07-15 22:34:23 - model_ensemble - INFO - 开始训练集成模型，方法: bagging
2025-07-15 22:34:23 - model_ensemble - ERROR - 集成方法 bagging 失败: __init__() got an unexpected keyword argument 'base_estimator'
2025-07-15 22:34:23 - model_ensemble - INFO - 步骤2: 运行集成方法 - boosting
2025-07-15 22:34:23 - model_ensemble - INFO - 开始训练集成模型，方法: boosting
2025-07-15 22:34:23 - model_ensemble - ERROR - 集成方法 boosting 失败: __init__() got an unexpected keyword argument 'base_estimator'
2025-07-15 22:34:23 - model_ensemble - INFO - 步骤2: 运行集成方法 - stacking
2025-07-15 22:34:23 - model_ensemble - INFO - 开始训练集成模型，方法: stacking
2025-07-15 22:34:25 - model_ensemble - INFO -   stacking - 准确率: 0.8750, F1: 0.8744
2025-07-15 22:34:25 - model_ensemble - INFO - ============================================================
2025-07-15 22:34:25 - model_ensemble - INFO - 集成学习结果总结
2025-07-15 22:34:25 - model_ensemble - INFO - ============================================================
2025-07-15 22:34:25 - model_ensemble - INFO - 最佳集成模型: stacking
2025-07-15 22:34:25 - model_ensemble - INFO - 最佳F1分数: 0.8744
2025-07-15 22:34:25 - model_ensemble - INFO - 最佳准确率: 0.8750
2025-07-15 22:34:25 - model_ensemble - INFO - 
所有集成模型性能对比:
2025-07-15 22:34:25 - model_ensemble - INFO -   voting_soft     - 准确率: 0.8500, 精确率: 0.8500, 召回率: 0.8500, F1: 0.8500, AUC: 0.9591
2025-07-15 22:34:25 - model_ensemble - INFO -   voting_hard     - 准确率: 0.8500, 精确率: 0.8513, 召回率: 0.8500, F1: 0.8484, AUC: 0.0000
2025-07-15 22:34:25 - model_ensemble - INFO -   stacking        - 准确率: 0.8750, 精确率: 0.8750, 召回率: 0.8750, F1: 0.8744, AUC: 0.9540
2025-07-15 22:34:25 - model_ensemble - INFO - 步骤3: 保存集成学习结果
2025-07-15 22:34:25 - model_ensemble - INFO - 集成学习结果已保存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\ensemble\ensemble_results_20250715_223425.joblib
2025-07-15 22:34:25 - model_ensemble - INFO - Ensemble report saved to: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\ensemble
2025-07-15 22:34:25 - model_ensemble - INFO - 步骤4: 生成SHAP可解释性分析
2025-07-15 22:34:25 - model_ensemble - INFO - 为 voting_soft 生成SHAP分析
2025-07-15 22:34:34 - model_ensemble - INFO -   voting_soft 完整SHAP分析完成
2025-07-15 22:34:34 - model_ensemble - INFO -     生成的图表类型: ['summary', 'importance', 'force', 'waterfall', 'dependence', 'decision']
2025-07-15 22:34:34 - model_ensemble - INFO -     summary: ['dot', 'bar']
2025-07-15 22:34:34 - model_ensemble - INFO -     importance: 1 个文件
2025-07-15 22:34:34 - model_ensemble - INFO -     force: 3 个文件
2025-07-15 22:34:34 - model_ensemble - INFO -     waterfall: 3 个文件
2025-07-15 22:34:34 - model_ensemble - INFO -     dependence: 5 个文件
2025-07-15 22:34:34 - model_ensemble - INFO -     decision: 1 个文件
2025-07-15 22:34:34 - model_ensemble - INFO -   voting_soft SHAP分析完成
2025-07-15 22:34:34 - model_ensemble - INFO - 为 voting_hard 生成SHAP分析
2025-07-15 22:34:55 - model_ensemble - INFO -   voting_hard 完整SHAP分析完成
2025-07-15 22:34:55 - model_ensemble - INFO -     生成的图表类型: ['summary', 'importance', 'force', 'waterfall', 'dependence', 'decision']
2025-07-15 22:34:55 - model_ensemble - INFO -     summary: ['dot', 'bar']
2025-07-15 22:34:55 - model_ensemble - INFO -     importance: 1 个文件
2025-07-15 22:34:55 - model_ensemble - INFO -     force: 3 个文件
2025-07-15 22:34:55 - model_ensemble - INFO -     waterfall: 3 个文件
2025-07-15 22:34:55 - model_ensemble - INFO -     dependence: 5 个文件
2025-07-15 22:34:55 - model_ensemble - INFO -     decision: 1 个文件
2025-07-15 22:34:55 - model_ensemble - INFO -   voting_hard SHAP分析完成
2025-07-15 22:34:55 - model_ensemble - INFO - 为 stacking 生成SHAP分析
2025-07-15 22:35:05 - model_ensemble - INFO -   stacking 完整SHAP分析完成
2025-07-15 22:35:05 - model_ensemble - INFO -     生成的图表类型: ['summary', 'importance', 'force', 'waterfall', 'dependence', 'decision']
2025-07-15 22:35:05 - model_ensemble - INFO -     summary: ['dot', 'bar']
2025-07-15 22:35:05 - model_ensemble - INFO -     importance: 1 个文件
2025-07-15 22:35:05 - model_ensemble - INFO -     force: 3 个文件
2025-07-15 22:35:05 - model_ensemble - INFO -     waterfall: 3 个文件
2025-07-15 22:35:05 - model_ensemble - INFO -     dependence: 5 个文件
2025-07-15 22:35:05 - model_ensemble - INFO -     decision: 1 个文件
2025-07-15 22:35:05 - model_ensemble - INFO -   stacking SHAP分析完成
2025-07-15 22:42:55 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-07-15 22:42:55 - model_ensemble - INFO - ============================================================
2025-07-15 22:42:55 - model_ensemble - INFO - 开始运行集成学习管道
2025-07-15 22:42:55 - model_ensemble - INFO - ============================================================
2025-07-15 22:42:55 - model_ensemble - INFO - 基础模型: ['RandomForest']
2025-07-15 22:42:55 - model_ensemble - INFO - 集成方法: ['stacking']
2025-07-15 22:42:55 - model_ensemble - INFO - 步骤1: 训练基础模型
2025-07-15 22:42:55 - model_ensemble - INFO - 训练基础模型: RandomForest
2025-07-15 22:42:55 - model_ensemble - INFO -   RandomForest 训练完成
2025-07-15 22:42:55 - model_ensemble - INFO - 成功训练了 1 个基础模型
2025-07-15 22:42:55 - model_ensemble - INFO - 步骤2: 运行集成方法 - stacking
2025-07-15 22:42:55 - model_ensemble - INFO - 开始训练集成模型，方法: stacking
2025-07-15 22:42:55 - model_ensemble - INFO -   stacking - 准确率: 0.9833, F1: 0.9833
2025-07-15 22:42:55 - model_ensemble - INFO - ============================================================
2025-07-15 22:42:55 - model_ensemble - INFO - 集成学习结果总结
2025-07-15 22:42:55 - model_ensemble - INFO - ============================================================
2025-07-15 22:42:55 - model_ensemble - INFO - 最佳集成模型: stacking
2025-07-15 22:42:55 - model_ensemble - INFO - 最佳F1分数: 0.9833
2025-07-15 22:42:55 - model_ensemble - INFO - 最佳准确率: 0.9833
2025-07-15 22:42:55 - model_ensemble - INFO - 
所有集成模型性能对比:
2025-07-15 22:42:55 - model_ensemble - INFO -   stacking        - 准确率: 0.9833, 精确率: 0.9839, 召回率: 0.9833, F1: 0.9833, AUC: 0.9911
2025-07-15 22:42:55 - model_ensemble - INFO - 步骤3: 保存集成学习结果
2025-07-15 22:42:55 - model_ensemble - INFO - 集成学习结果已保存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\ensemble\ensemble_results_20250715_224255.joblib
2025-07-15 22:42:55 - model_ensemble - INFO - Ensemble report saved to: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\ensemble
2025-07-15 22:42:55 - model_ensemble - INFO - 步骤4: 生成SHAP可解释性分析
2025-07-15 22:42:55 - model_ensemble - INFO - 为 stacking 生成SHAP分析
2025-07-15 22:43:01 - model_ensemble - INFO -   stacking 完整SHAP分析完成
2025-07-15 22:43:01 - model_ensemble - INFO -     生成的图表类型: ['summary', 'importance', 'force', 'waterfall', 'dependence', 'decision']
2025-07-15 22:43:01 - model_ensemble - INFO -     importance: 1 个文件
2025-07-15 22:43:01 - model_ensemble - INFO -     force: 3 个文件
2025-07-15 22:43:01 - model_ensemble - INFO -     waterfall: 3 个文件
2025-07-15 22:43:01 - model_ensemble - INFO -     dependence: 5 个文件
2025-07-15 22:43:01 - model_ensemble - INFO -     decision: 1 个文件
2025-07-15 22:43:01 - model_ensemble - INFO -   stacking SHAP分析完成
2025-07-15 22:46:58 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-07-15 22:47:38 - model_ensemble - INFO - ============================================================
2025-07-15 22:47:38 - model_ensemble - INFO - 开始运行集成学习管道
2025-07-15 22:47:38 - model_ensemble - INFO - ============================================================
2025-07-15 22:47:38 - model_ensemble - INFO - 基础模型: ['RandomForest', 'XGBoost', 'SVM', 'KNN']
2025-07-15 22:47:38 - model_ensemble - INFO - 集成方法: ['voting', 'stacking']
2025-07-15 22:47:38 - model_ensemble - INFO - 步骤1: 训练基础模型
2025-07-15 22:47:38 - model_ensemble - INFO - 训练基础模型: RandomForest
2025-07-15 22:47:38 - model_ensemble - INFO -   RandomForest 训练完成
2025-07-15 22:47:38 - model_ensemble - INFO - 训练基础模型: XGBoost
2025-07-15 22:47:38 - model_ensemble - INFO -   XGBoost 训练完成
2025-07-15 22:47:38 - model_ensemble - INFO - 训练基础模型: SVM
2025-07-15 22:47:38 - model_ensemble - INFO -   SVM 训练完成
2025-07-15 22:47:38 - model_ensemble - INFO - 训练基础模型: KNN
2025-07-15 22:47:38 - model_ensemble - INFO -   KNN 训练完成
2025-07-15 22:47:38 - model_ensemble - INFO - 成功训练了 4 个基础模型
2025-07-15 22:47:38 - model_ensemble - INFO - 步骤2: 运行集成方法 - voting
2025-07-15 22:47:38 - model_ensemble - INFO -   训练 voting_soft 集成模型
2025-07-15 22:47:38 - model_ensemble - INFO - 开始训练集成模型，方法: voting
2025-07-15 22:47:38 - model_ensemble - INFO -     voting_soft - 准确率: 0.7750, F1: 0.7710
2025-07-15 22:47:38 - model_ensemble - INFO -   训练 voting_hard 集成模型
2025-07-15 22:47:38 - model_ensemble - INFO - 开始训练集成模型，方法: voting
2025-07-15 22:47:39 - model_ensemble - INFO -     voting_hard - 准确率: 0.6500, F1: 0.6221
2025-07-15 22:47:39 - model_ensemble - INFO - 步骤2: 运行集成方法 - stacking
2025-07-15 22:47:39 - model_ensemble - INFO - 开始训练集成模型，方法: stacking
2025-07-15 22:47:39 - model_ensemble - INFO -   stacking - 准确率: 0.8000, F1: 0.7979
2025-07-15 22:47:39 - model_ensemble - INFO - ============================================================
2025-07-15 22:47:39 - model_ensemble - INFO - 集成学习结果总结
2025-07-15 22:47:39 - model_ensemble - INFO - ============================================================
2025-07-15 22:47:39 - model_ensemble - INFO - 最佳集成模型: stacking
2025-07-15 22:47:39 - model_ensemble - INFO - 最佳F1分数: 0.7979
2025-07-15 22:47:39 - model_ensemble - INFO - 最佳准确率: 0.8000
2025-07-15 22:47:39 - model_ensemble - INFO - 
所有集成模型性能对比:
2025-07-15 22:47:39 - model_ensemble - INFO -   voting_soft     - 准确率: 0.7750, 精确率: 0.7762, 召回率: 0.7750, F1: 0.7710, AUC: 0.8389
2025-07-15 22:47:39 - model_ensemble - INFO -   voting_hard     - 准确率: 0.6500, 精确率: 0.6543, 召回率: 0.6500, F1: 0.6221, AUC: 0.0000
2025-07-15 22:47:39 - model_ensemble - INFO -   stacking        - 准确率: 0.8000, 精确率: 0.8000, 召回率: 0.8000, F1: 0.7979, AUC: 0.8389
2025-07-15 22:47:39 - model_ensemble - INFO - 步骤3: 保存集成学习结果
2025-07-15 22:47:39 - model_ensemble - INFO - 集成学习结果已保存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\ensemble\ensemble_results_20250715_224739.joblib
2025-07-15 22:47:39 - model_ensemble - INFO - Ensemble report saved to: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\ensemble
2025-07-15 22:47:39 - model_ensemble - INFO - 步骤4: 生成SHAP可解释性分析
2025-07-15 22:47:39 - model_ensemble - INFO - 为 voting_soft 生成SHAP分析
2025-07-15 22:47:50 - model_ensemble - INFO -   voting_soft 完整SHAP分析完成
2025-07-15 22:47:50 - model_ensemble - INFO -     生成的图表类型: ['summary', 'importance', 'force', 'waterfall', 'dependence', 'decision']
2025-07-15 22:47:50 - model_ensemble - INFO -     summary: ['dot', 'bar']
2025-07-15 22:47:50 - model_ensemble - INFO -     importance: 1 个文件
2025-07-15 22:47:50 - model_ensemble - INFO -     force: 3 个文件
2025-07-15 22:47:50 - model_ensemble - INFO -     waterfall: 3 个文件
2025-07-15 22:47:50 - model_ensemble - INFO -     dependence: 5 个文件
2025-07-15 22:47:50 - model_ensemble - INFO -     decision: 1 个文件
2025-07-15 22:47:50 - model_ensemble - INFO -   voting_soft SHAP分析完成
2025-07-15 22:47:50 - model_ensemble - INFO - 为 voting_hard 生成SHAP分析
2025-07-15 22:48:08 - model_ensemble - INFO -   voting_hard 完整SHAP分析完成
2025-07-15 22:48:08 - model_ensemble - INFO -     生成的图表类型: ['summary', 'importance', 'force', 'waterfall', 'dependence', 'decision']
2025-07-15 22:48:08 - model_ensemble - INFO -     summary: ['dot', 'bar']
2025-07-15 22:48:08 - model_ensemble - INFO -     importance: 1 个文件
2025-07-15 22:48:08 - model_ensemble - INFO -     force: 3 个文件
2025-07-15 22:48:08 - model_ensemble - INFO -     waterfall: 3 个文件
2025-07-15 22:48:08 - model_ensemble - INFO -     dependence: 5 个文件
2025-07-15 22:48:08 - model_ensemble - INFO -     decision: 1 个文件
2025-07-15 22:48:08 - model_ensemble - INFO -   voting_hard SHAP分析完成
2025-07-15 22:48:08 - model_ensemble - INFO - 为 stacking 生成SHAP分析
2025-07-15 22:48:19 - model_ensemble - INFO -   stacking 完整SHAP分析完成
2025-07-15 22:48:19 - model_ensemble - INFO -     生成的图表类型: ['summary', 'importance', 'force', 'waterfall', 'dependence', 'decision']
2025-07-15 22:48:19 - model_ensemble - INFO -     summary: ['dot', 'bar']
2025-07-15 22:48:19 - model_ensemble - INFO -     importance: 1 个文件
2025-07-15 22:48:19 - model_ensemble - INFO -     force: 3 个文件
2025-07-15 22:48:19 - model_ensemble - INFO -     waterfall: 3 个文件
2025-07-15 22:48:19 - model_ensemble - INFO -     dependence: 5 个文件
2025-07-15 22:48:19 - model_ensemble - INFO -     decision: 1 个文件
2025-07-15 22:48:19 - model_ensemble - INFO -   stacking SHAP分析完成
2025-07-15 23:08:42 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-07-15 23:09:03 - model_ensemble - INFO - ============================================================
2025-07-15 23:09:03 - model_ensemble - INFO - 开始运行集成学习管道
2025-07-15 23:09:03 - model_ensemble - INFO - ============================================================
2025-07-15 23:09:03 - model_ensemble - INFO - 基础模型: ['RandomForest', 'XGBoost', 'LightGBM', 'Logistic']
2025-07-15 23:09:03 - model_ensemble - INFO - 集成方法: ['stacking']
2025-07-15 23:09:03 - model_ensemble - INFO - 步骤1: 训练基础模型
2025-07-15 23:09:03 - model_ensemble - INFO - 训练基础模型: RandomForest
2025-07-15 23:09:03 - model_ensemble - INFO -   RandomForest 训练完成
2025-07-15 23:09:03 - model_ensemble - INFO - 训练基础模型: XGBoost
2025-07-15 23:09:03 - model_ensemble - INFO -   XGBoost 训练完成
2025-07-15 23:09:03 - model_ensemble - INFO - 训练基础模型: LightGBM
2025-07-15 23:09:05 - model_ensemble - INFO -   LightGBM 训练完成
2025-07-15 23:09:05 - model_ensemble - INFO - 训练基础模型: Logistic
2025-07-15 23:09:05 - model_ensemble - INFO -   Logistic 训练完成
2025-07-15 23:09:05 - model_ensemble - INFO - 成功训练了 4 个基础模型
2025-07-15 23:09:05 - model_ensemble - INFO - 步骤2: 运行集成方法 - stacking
2025-07-15 23:09:05 - model_ensemble - INFO - 开始训练集成模型，方法: stacking
2025-07-15 23:09:07 - model_ensemble - INFO -   stacking - 准确率: 0.8250, F1: 0.8242
2025-07-15 23:09:07 - model_ensemble - INFO - ============================================================
2025-07-15 23:09:07 - model_ensemble - INFO - 集成学习结果总结
2025-07-15 23:09:07 - model_ensemble - INFO - ============================================================
2025-07-15 23:09:07 - model_ensemble - INFO - 最佳集成模型: stacking
2025-07-15 23:09:07 - model_ensemble - INFO - 最佳F1分数: 0.8242
2025-07-15 23:09:07 - model_ensemble - INFO - 最佳准确率: 0.8250
2025-07-15 23:09:07 - model_ensemble - INFO - 
所有集成模型性能对比:
2025-07-15 23:09:07 - model_ensemble - INFO -   stacking        - 准确率: 0.8250, 精确率: 0.8245, 召回率: 0.8250, F1: 0.8242, AUC: 0.8875
2025-07-15 23:09:07 - model_ensemble - INFO - 步骤3: 保存集成学习结果
2025-07-15 23:09:07 - model_ensemble - INFO - 集成学习结果已保存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\ensemble\ensemble_results_20250715_230907.joblib
2025-07-15 23:09:07 - model_ensemble - INFO - Ensemble report saved to: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\ensemble
2025-07-15 23:09:07 - model_ensemble - INFO - 步骤4: 生成SHAP可解释性分析
2025-07-15 23:09:07 - model_ensemble - INFO - 为 stacking 生成SHAP分析
2025-07-15 23:09:12 - model_ensemble - INFO -   stacking 完整SHAP分析完成
2025-07-15 23:09:12 - model_ensemble - INFO -     生成的图表类型: ['summary', 'importance', 'force', 'waterfall', 'dependence', 'decision']
2025-07-15 23:09:12 - model_ensemble - INFO -     summary: ['dot', 'bar']
2025-07-15 23:09:12 - model_ensemble - INFO -     importance: 1 个文件
2025-07-15 23:09:12 - model_ensemble - INFO -     force: 3 个文件
2025-07-15 23:09:12 - model_ensemble - INFO -     waterfall: 0 个文件
2025-07-15 23:09:12 - model_ensemble - INFO -     dependence: 0 个文件
2025-07-15 23:09:12 - model_ensemble - INFO -     decision: 1 个文件
2025-07-15 23:09:12 - model_ensemble - INFO -   stacking SHAP分析完成
2025-07-15 23:11:55 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-07-15 23:12:16 - model_ensemble - INFO - ============================================================
2025-07-15 23:12:16 - model_ensemble - INFO - 开始运行集成学习管道
2025-07-15 23:12:16 - model_ensemble - INFO - ============================================================
2025-07-15 23:12:16 - model_ensemble - INFO - 基础模型: ['RandomForest', 'XGBoost', 'LightGBM', 'Logistic']
2025-07-15 23:12:16 - model_ensemble - INFO - 集成方法: ['stacking']
2025-07-15 23:12:16 - model_ensemble - INFO - 步骤1: 训练基础模型
2025-07-15 23:12:16 - model_ensemble - INFO - 训练基础模型: RandomForest
2025-07-15 23:12:16 - model_ensemble - INFO -   RandomForest 训练完成
2025-07-15 23:12:16 - model_ensemble - INFO - 训练基础模型: XGBoost
2025-07-15 23:12:16 - model_ensemble - INFO -   XGBoost 训练完成
2025-07-15 23:12:16 - model_ensemble - INFO - 训练基础模型: LightGBM
2025-07-15 23:12:18 - model_ensemble - INFO -   LightGBM 训练完成
2025-07-15 23:12:18 - model_ensemble - INFO - 训练基础模型: Logistic
2025-07-15 23:12:18 - model_ensemble - INFO -   Logistic 训练完成
2025-07-15 23:12:18 - model_ensemble - INFO - 成功训练了 4 个基础模型
2025-07-15 23:12:18 - model_ensemble - INFO - 步骤2: 运行集成方法 - stacking
2025-07-15 23:12:18 - model_ensemble - INFO - 开始训练集成模型，方法: stacking
2025-07-15 23:12:20 - model_ensemble - INFO -   stacking - 准确率: 0.8750, F1: 0.8744
2025-07-15 23:12:20 - model_ensemble - INFO - ============================================================
2025-07-15 23:12:20 - model_ensemble - INFO - 集成学习结果总结
2025-07-15 23:12:20 - model_ensemble - INFO - ============================================================
2025-07-15 23:12:20 - model_ensemble - INFO - 最佳集成模型: stacking
2025-07-15 23:12:20 - model_ensemble - INFO - 最佳F1分数: 0.8744
2025-07-15 23:12:20 - model_ensemble - INFO - 最佳准确率: 0.8750
2025-07-15 23:12:20 - model_ensemble - INFO - 
所有集成模型性能对比:
2025-07-15 23:12:20 - model_ensemble - INFO -   stacking        - 准确率: 0.8750, 精确率: 0.8750, 召回率: 0.8750, F1: 0.8744, AUC: 0.9668
2025-07-15 23:12:20 - model_ensemble - INFO - 步骤3: 保存集成学习结果
2025-07-15 23:12:20 - model_ensemble - INFO - 集成学习结果已保存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\ensemble\ensemble_results_20250715_231220.joblib
2025-07-15 23:12:20 - model_ensemble - INFO - Ensemble report saved to: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\ensemble
2025-07-15 23:12:20 - model_ensemble - INFO - 步骤4: 生成SHAP可解释性分析
2025-07-15 23:12:20 - model_ensemble - INFO - 为 stacking 生成SHAP分析
2025-07-15 23:12:26 - model_ensemble - INFO -   stacking 完整SHAP分析完成
2025-07-15 23:12:26 - model_ensemble - INFO -     生成的图表类型: ['summary', 'importance', 'force', 'waterfall', 'dependence', 'decision']
2025-07-15 23:12:26 - model_ensemble - INFO -     summary: ['dot', 'bar']
2025-07-15 23:12:26 - model_ensemble - INFO -     importance: 1 个文件
2025-07-15 23:12:26 - model_ensemble - INFO -     force: 3 个文件
2025-07-15 23:12:26 - model_ensemble - INFO -     waterfall: 3 个文件
2025-07-15 23:12:26 - model_ensemble - INFO -     dependence: 5 个文件
2025-07-15 23:12:26 - model_ensemble - INFO -     decision: 1 个文件
2025-07-15 23:12:26 - model_ensemble - INFO -   stacking SHAP分析完成
2025-07-15 23:23:20 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-07-15 23:23:51 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-07-15 23:24:24 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-07-15 23:24:24 - model_ensemble - INFO - 开始训练集成模型，方法: stacking
2025-07-15 23:28:51 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-07-15 23:32:31 - model_ensemble - INFO - ============================================================
2025-07-15 23:32:31 - model_ensemble - INFO - 开始运行集成学习管道
2025-07-15 23:32:31 - model_ensemble - INFO - ============================================================
2025-07-15 23:32:31 - model_ensemble - INFO - 基础模型: ['RandomForest', 'XGBoost', 'SVM']
2025-07-15 23:32:31 - model_ensemble - INFO - 集成方法: ['stacking']
2025-07-15 23:32:31 - model_ensemble - INFO - 步骤1: 训练基础模型
2025-07-15 23:32:31 - model_ensemble - INFO - 训练基础模型: RandomForest
2025-07-15 23:32:31 - model_ensemble - INFO -   RandomForest 训练完成
2025-07-15 23:32:31 - model_ensemble - INFO - 训练基础模型: XGBoost
2025-07-15 23:32:31 - model_ensemble - INFO -   XGBoost 训练完成
2025-07-15 23:32:31 - model_ensemble - INFO - 训练基础模型: SVM
2025-07-15 23:32:31 - model_ensemble - INFO -   SVM 训练完成
2025-07-15 23:32:31 - model_ensemble - INFO - 成功训练了 3 个基础模型
2025-07-15 23:32:31 - model_ensemble - INFO - 步骤2: 运行集成方法 - stacking
2025-07-15 23:32:31 - model_ensemble - INFO - 开始训练集成模型，方法: stacking
2025-07-15 23:32:31 - model_ensemble - INFO -   stacking - 准确率: 0.8750, F1: 0.8744
2025-07-15 23:32:31 - model_ensemble - INFO - ============================================================
2025-07-15 23:32:31 - model_ensemble - INFO - 集成学习结果总结
2025-07-15 23:32:31 - model_ensemble - INFO - ============================================================
2025-07-15 23:32:31 - model_ensemble - INFO - 最佳集成模型: stacking
2025-07-15 23:32:31 - model_ensemble - INFO - 最佳F1分数: 0.8744
2025-07-15 23:32:31 - model_ensemble - INFO - 最佳准确率: 0.8750
2025-07-15 23:32:31 - model_ensemble - INFO - 
所有集成模型性能对比:
2025-07-15 23:32:31 - model_ensemble - INFO -   stacking        - 准确率: 0.8750, 精确率: 0.8750, 召回率: 0.8750, F1: 0.8744, AUC: 0.9182
2025-07-15 23:32:31 - model_ensemble - INFO - 步骤3: 保存集成学习结果
2025-07-15 23:32:31 - model_ensemble - INFO - 集成学习结果已保存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\ensemble\ensemble_results_20250715_233231.joblib
2025-07-15 23:32:31 - model_ensemble - INFO - Ensemble report saved to: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\ensemble
2025-07-15 23:32:31 - model_ensemble - INFO - 步骤4: 生成SHAP可解释性分析
2025-07-15 23:32:31 - model_ensemble - INFO - 为 stacking 生成SHAP分析
2025-07-15 23:32:41 - model_ensemble - INFO -   stacking 完整SHAP分析完成
2025-07-15 23:32:41 - model_ensemble - INFO -     生成的图表类型: ['summary', 'importance', 'force', 'waterfall', 'dependence', 'decision']
2025-07-15 23:32:41 - model_ensemble - INFO -     summary: ['dot', 'bar']
2025-07-15 23:32:41 - model_ensemble - INFO -     importance: 1 个文件
2025-07-15 23:32:41 - model_ensemble - INFO -     force: 3 个文件
2025-07-15 23:32:41 - model_ensemble - INFO -     waterfall: 3 个文件
2025-07-15 23:32:41 - model_ensemble - INFO -     dependence: 5 个文件
2025-07-15 23:32:41 - model_ensemble - INFO -     decision: 1 个文件
2025-07-15 23:32:41 - model_ensemble - INFO -   stacking SHAP分析完成
2025-07-16 00:00:13 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-07-16 00:00:13 - model_ensemble - INFO - 开始训练集成模型，方法: stacking
2025-07-16 00:02:49 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-07-16 00:03:11 - model_ensemble - INFO - ============================================================
2025-07-16 00:03:11 - model_ensemble - INFO - 开始运行集成学习管道
2025-07-16 00:03:11 - model_ensemble - INFO - ============================================================
2025-07-16 00:03:11 - model_ensemble - INFO - 基础模型: ['RandomForest', 'SVM', 'KNN']
2025-07-16 00:03:11 - model_ensemble - INFO - 集成方法: ['stacking']
2025-07-16 00:03:11 - model_ensemble - INFO - 步骤1: 训练基础模型
2025-07-16 00:03:11 - model_ensemble - INFO - 训练基础模型: RandomForest
2025-07-16 00:03:11 - model_ensemble - INFO -   RandomForest 训练完成
2025-07-16 00:03:11 - model_ensemble - INFO - 训练基础模型: SVM
2025-07-16 00:03:11 - model_ensemble - INFO -   SVM 训练完成
2025-07-16 00:03:11 - model_ensemble - INFO - 训练基础模型: KNN
2025-07-16 00:03:11 - model_ensemble - INFO -   KNN 训练完成
2025-07-16 00:03:11 - model_ensemble - INFO - 成功训练了 3 个基础模型
2025-07-16 00:03:11 - model_ensemble - INFO - 步骤2: 运行集成方法 - stacking
2025-07-16 00:03:11 - model_ensemble - INFO - 开始训练集成模型，方法: stacking
2025-07-16 00:03:12 - model_ensemble - INFO -   stacking - 准确率: 0.8250, F1: 0.8219
2025-07-16 00:03:12 - model_ensemble - INFO - ============================================================
2025-07-16 00:03:12 - model_ensemble - INFO - 集成学习结果总结
2025-07-16 00:03:12 - model_ensemble - INFO - ============================================================
2025-07-16 00:03:12 - model_ensemble - INFO - 最佳集成模型: stacking
2025-07-16 00:03:12 - model_ensemble - INFO - 最佳F1分数: 0.8219
2025-07-16 00:03:12 - model_ensemble - INFO - 最佳准确率: 0.8250
2025-07-16 00:03:12 - model_ensemble - INFO - 
所有集成模型性能对比:
2025-07-16 00:03:12 - model_ensemble - INFO -   stacking        - 准确率: 0.8250, 精确率: 0.8287, 召回率: 0.8250, F1: 0.8219, AUC: 0.9463
2025-07-16 00:03:12 - model_ensemble - INFO - 步骤3: 保存集成学习结果
2025-07-16 00:03:12 - model_ensemble - INFO - 集成学习结果已保存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\ensemble\ensemble_results_20250716_000312.joblib
2025-07-16 00:03:12 - model_ensemble - INFO - Ensemble report saved to: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\ensemble
2025-07-16 00:03:12 - model_ensemble - INFO - 步骤4: 生成SHAP可解释性分析
2025-07-16 00:03:12 - model_ensemble - INFO - 为 stacking 生成SHAP分析
2025-07-16 00:03:19 - model_ensemble - INFO -   stacking 完整SHAP分析完成
2025-07-16 00:03:19 - model_ensemble - INFO -     生成的图表类型: ['summary', 'importance', 'force', 'waterfall', 'dependence', 'decision']
2025-07-16 00:03:19 - model_ensemble - INFO -     summary: ['dot', 'bar']
2025-07-16 00:03:19 - model_ensemble - INFO -     importance: 1 个文件
2025-07-16 00:03:19 - model_ensemble - INFO -     force: 3 个文件
2025-07-16 00:03:19 - model_ensemble - INFO -     waterfall: 3 个文件
2025-07-16 00:03:19 - model_ensemble - INFO -     dependence: 5 个文件
2025-07-16 00:03:19 - model_ensemble - INFO -     decision: 1 个文件
2025-07-16 00:03:19 - model_ensemble - INFO -   stacking SHAP分析完成
2025-07-16 00:14:37 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-07-16 00:15:59 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-07-16 00:16:03 - model_ensemble - INFO -   stacking 基础SHAP分析完成
2025-07-16 00:16:03 - model_ensemble - INFO - 为 stacking 生成SHAP分析
2025-07-16 00:16:03 - model_ensemble - WARNING - SHAP分析失败: 'StackingClassifier' object has no attribute 'ensemble_model'
2025-07-16 00:16:48 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-07-16 00:16:51 - model_ensemble - INFO -   stacking 基础SHAP分析完成
2025-07-16 00:16:51 - model_ensemble - INFO - 为 stacking 生成SHAP分析
2025-07-16 00:16:53 - model_ensemble - INFO -   stacking 基础SHAP分析完成
2025-07-16 00:16:53 - model_ensemble - INFO -   stacking 基础SHAP分析完成
2025-07-16 00:16:53 - model_ensemble - INFO -   stacking SHAP分析完成
2025-07-16 00:18:43 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-07-16 00:19:03 - model_ensemble - INFO - ============================================================
2025-07-16 00:19:03 - model_ensemble - INFO - 开始运行集成学习管道
2025-07-16 00:19:03 - model_ensemble - INFO - ============================================================
2025-07-16 00:19:03 - model_ensemble - INFO - 基础模型: ['RandomForest', 'XGBoost', 'LightGBM', 'Logistic']
2025-07-16 00:19:03 - model_ensemble - INFO - 集成方法: ['stacking']
2025-07-16 00:19:03 - model_ensemble - INFO - 步骤1: 训练基础模型
2025-07-16 00:19:03 - model_ensemble - INFO - 训练基础模型: RandomForest
2025-07-16 00:19:03 - model_ensemble - INFO -   RandomForest 训练完成
2025-07-16 00:19:03 - model_ensemble - INFO - 训练基础模型: XGBoost
2025-07-16 00:19:03 - model_ensemble - INFO -   XGBoost 训练完成
2025-07-16 00:19:03 - model_ensemble - INFO - 训练基础模型: LightGBM
2025-07-16 00:19:04 - model_ensemble - INFO -   LightGBM 训练完成
2025-07-16 00:19:04 - model_ensemble - INFO - 训练基础模型: Logistic
2025-07-16 00:19:04 - model_ensemble - INFO -   Logistic 训练完成
2025-07-16 00:19:04 - model_ensemble - INFO - 成功训练了 4 个基础模型
2025-07-16 00:19:04 - model_ensemble - INFO - 步骤2: 运行集成方法 - stacking
2025-07-16 00:19:04 - model_ensemble - INFO - 开始训练集成模型，方法: stacking
2025-07-16 00:19:06 - model_ensemble - INFO -   stacking - 准确率: 0.8750, F1: 0.8744
2025-07-16 00:19:06 - model_ensemble - INFO - ============================================================
2025-07-16 00:19:06 - model_ensemble - INFO - 集成学习结果总结
2025-07-16 00:19:06 - model_ensemble - INFO - ============================================================
2025-07-16 00:19:06 - model_ensemble - INFO - 最佳集成模型: stacking
2025-07-16 00:19:06 - model_ensemble - INFO - 最佳F1分数: 0.8744
2025-07-16 00:19:06 - model_ensemble - INFO - 最佳准确率: 0.8750
2025-07-16 00:19:06 - model_ensemble - INFO - 
所有集成模型性能对比:
2025-07-16 00:19:06 - model_ensemble - INFO -   stacking        - 准确率: 0.8750, 精确率: 0.8750, 召回率: 0.8750, F1: 0.8744, AUC: 0.9668
2025-07-16 00:19:06 - model_ensemble - INFO - 步骤3: 保存集成学习结果
2025-07-16 00:19:06 - model_ensemble - INFO - 集成学习结果已保存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\ensemble\ensemble_results_20250716_001906.joblib
2025-07-16 00:19:06 - model_ensemble - INFO - Ensemble report saved to: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\ensemble
2025-07-16 00:19:06 - model_ensemble - INFO - 步骤4: 生成SHAP可解释性分析
2025-07-16 00:19:06 - model_ensemble - INFO - 为 stacking 生成SHAP分析
2025-07-16 00:19:12 - model_ensemble - INFO -   stacking 基础SHAP分析完成
2025-07-16 00:19:12 - model_ensemble - INFO -   stacking 基础SHAP分析完成
2025-07-16 00:19:12 - model_ensemble - INFO -   stacking SHAP分析完成
2025-07-16 00:20:52 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-07-16 00:21:03 - model_ensemble - INFO - ============================================================
2025-07-16 00:21:03 - model_ensemble - INFO - 开始运行集成学习管道
2025-07-16 00:21:03 - model_ensemble - INFO - ============================================================
2025-07-16 00:21:03 - model_ensemble - INFO - 基础模型: ['RandomForest', 'KNN']
2025-07-16 00:21:03 - model_ensemble - INFO - 集成方法: ['voting', 'stacking']
2025-07-16 00:21:03 - model_ensemble - INFO - 步骤1: 训练基础模型
2025-07-16 00:21:03 - model_ensemble - INFO - 训练基础模型: RandomForest
2025-07-16 00:21:03 - model_ensemble - INFO -   RandomForest 训练完成
2025-07-16 00:21:03 - model_ensemble - INFO - 训练基础模型: KNN
2025-07-16 00:21:03 - model_ensemble - INFO -   KNN 训练完成
2025-07-16 00:21:03 - model_ensemble - INFO - 成功训练了 2 个基础模型
2025-07-16 00:21:03 - model_ensemble - INFO - 步骤2: 运行集成方法 - voting
2025-07-16 00:21:03 - model_ensemble - INFO -   训练 voting_soft 集成模型
2025-07-16 00:21:03 - model_ensemble - INFO - 开始训练集成模型，方法: voting
2025-07-16 00:21:03 - model_ensemble - INFO -     voting_soft - 准确率: 0.8750, F1: 0.8744
2025-07-16 00:21:03 - model_ensemble - INFO -   训练 voting_hard 集成模型
2025-07-16 00:21:03 - model_ensemble - INFO - 开始训练集成模型，方法: voting
2025-07-16 00:21:03 - model_ensemble - INFO -     voting_hard - 准确率: 0.8500, F1: 0.8484
2025-07-16 00:21:03 - model_ensemble - INFO - 步骤2: 运行集成方法 - stacking
2025-07-16 00:21:03 - model_ensemble - INFO - 开始训练集成模型，方法: stacking
2025-07-16 00:21:04 - model_ensemble - INFO -   stacking - 准确率: 0.8750, F1: 0.8744
2025-07-16 00:21:04 - model_ensemble - INFO - ============================================================
2025-07-16 00:21:04 - model_ensemble - INFO - 集成学习结果总结
2025-07-16 00:21:04 - model_ensemble - INFO - ============================================================
2025-07-16 00:21:04 - model_ensemble - INFO - 最佳集成模型: voting_soft
2025-07-16 00:21:04 - model_ensemble - INFO - 最佳F1分数: 0.8744
2025-07-16 00:21:04 - model_ensemble - INFO - 最佳准确率: 0.8750
2025-07-16 00:21:04 - model_ensemble - INFO - 
所有集成模型性能对比:
2025-07-16 00:21:04 - model_ensemble - INFO -   voting_soft     - 准确率: 0.8750, 精确率: 0.8750, 召回率: 0.8750, F1: 0.8744, AUC: 0.9233
2025-07-16 00:21:04 - model_ensemble - INFO -   voting_hard     - 准确率: 0.8500, 精确率: 0.8513, 召回率: 0.8500, F1: 0.8484, AUC: 0.0000
2025-07-16 00:21:04 - model_ensemble - INFO -   stacking        - 准确率: 0.8750, 精确率: 0.8750, 召回率: 0.8750, F1: 0.8744, AUC: 0.9233
2025-07-16 00:21:04 - model_ensemble - INFO - 步骤3: 保存集成学习结果
2025-07-16 00:21:04 - model_ensemble - INFO - 集成学习结果已保存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\ensemble\ensemble_results_20250716_002104.joblib
2025-07-16 00:21:04 - model_ensemble - INFO - Ensemble report saved to: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\ensemble
2025-07-16 00:21:04 - model_ensemble - INFO - 步骤4: 生成SHAP可解释性分析
2025-07-16 00:21:04 - model_ensemble - INFO - 为 voting_soft 生成SHAP分析
2025-07-16 00:21:11 - model_ensemble - INFO -   voting_soft 完整SHAP分析完成
2025-07-16 00:21:11 - model_ensemble - INFO -     生成的图表类型: ['summary', 'importance', 'force', 'waterfall', 'dependence', 'decision']
2025-07-16 00:21:11 - model_ensemble - INFO -     summary: ['dot', 'bar']
2025-07-16 00:21:11 - model_ensemble - INFO -     importance: 1 个文件
2025-07-16 00:21:11 - model_ensemble - INFO -     force: 3 个文件
2025-07-16 00:21:11 - model_ensemble - INFO -     waterfall: 3 个文件
2025-07-16 00:21:11 - model_ensemble - INFO -     dependence: 5 个文件
2025-07-16 00:21:11 - model_ensemble - INFO -     decision: 1 个文件
2025-07-16 00:21:11 - model_ensemble - INFO -   voting_soft SHAP分析完成
2025-07-16 00:21:11 - model_ensemble - INFO - 为 voting_hard 生成SHAP分析
2025-07-16 00:21:36 - model_ensemble - INFO -   voting_hard 完整SHAP分析完成
2025-07-16 00:21:36 - model_ensemble - INFO -     生成的图表类型: ['summary', 'importance', 'force', 'waterfall', 'dependence', 'decision']
2025-07-16 00:21:36 - model_ensemble - INFO -     summary: ['dot', 'bar']
2025-07-16 00:21:36 - model_ensemble - INFO -     importance: 1 个文件
2025-07-16 00:21:36 - model_ensemble - INFO -     force: 3 个文件
2025-07-16 00:21:36 - model_ensemble - INFO -     waterfall: 3 个文件
2025-07-16 00:21:36 - model_ensemble - INFO -     dependence: 5 个文件
2025-07-16 00:21:36 - model_ensemble - INFO -     decision: 1 个文件
2025-07-16 00:21:36 - model_ensemble - INFO -   voting_hard SHAP分析完成
2025-07-16 00:21:36 - model_ensemble - INFO - 为 stacking 生成SHAP分析
2025-07-16 00:21:45 - model_ensemble - INFO -   stacking 完整SHAP分析完成
2025-07-16 00:21:45 - model_ensemble - INFO -     生成的图表类型: ['summary', 'importance', 'force', 'waterfall', 'dependence', 'decision']
2025-07-16 00:21:45 - model_ensemble - INFO -     summary: ['dot', 'bar']
2025-07-16 00:21:45 - model_ensemble - INFO -     importance: 1 个文件
2025-07-16 00:21:45 - model_ensemble - INFO -     force: 3 个文件
2025-07-16 00:21:45 - model_ensemble - INFO -     waterfall: 3 个文件
2025-07-16 00:21:45 - model_ensemble - INFO -     dependence: 5 个文件
2025-07-16 00:21:45 - model_ensemble - INFO -     decision: 1 个文件
2025-07-16 00:21:45 - model_ensemble - INFO -   stacking SHAP分析完成
2025-07-16 00:23:04 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-07-16 00:28:37 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-07-16 00:30:13 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-07-16 00:40:47 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-07-16 00:44:25 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-07-16 00:46:05 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-07-16 00:54:35 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-07-16 01:00:01 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-07-16 01:11:35 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-07-16 01:23:54 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-07-16 01:44:00 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-07-16 01:58:12 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-07-16 01:58:34 - model_ensemble - INFO - ============================================================
2025-07-16 01:58:34 - model_ensemble - INFO - 开始运行集成学习管道
2025-07-16 01:58:34 - model_ensemble - INFO - ============================================================
2025-07-16 01:58:34 - model_ensemble - INFO - 基础模型: ['DecisionTree', 'RandomForest', 'XGBoost', 'LightGBM']
2025-07-16 01:58:34 - model_ensemble - INFO - 集成方法: ['stacking']
2025-07-16 01:58:34 - model_ensemble - INFO - 步骤1: 训练基础模型
2025-07-16 01:58:34 - model_ensemble - INFO - 训练基础模型: DecisionTree
2025-07-16 01:58:34 - model_ensemble - INFO -   DecisionTree 训练完成
2025-07-16 01:58:34 - model_ensemble - INFO - 训练基础模型: RandomForest
2025-07-16 01:58:35 - model_ensemble - INFO -   RandomForest 训练完成
2025-07-16 01:58:35 - model_ensemble - INFO - 训练基础模型: XGBoost
2025-07-16 01:58:35 - model_ensemble - INFO -   XGBoost 训练完成
2025-07-16 01:58:35 - model_ensemble - INFO - 训练基础模型: LightGBM
2025-07-16 01:58:36 - model_ensemble - INFO -   LightGBM 训练完成
2025-07-16 01:58:36 - model_ensemble - INFO - 成功训练了 4 个基础模型
2025-07-16 01:58:36 - model_ensemble - INFO - 步骤2: 运行集成方法 - stacking
2025-07-16 01:58:36 - model_ensemble - INFO - 开始训练集成模型，方法: stacking
2025-07-16 01:58:38 - model_ensemble - INFO -   stacking - 准确率: 0.8000, F1: 0.8000
2025-07-16 01:58:38 - model_ensemble - INFO - ============================================================
2025-07-16 01:58:38 - model_ensemble - INFO - 集成学习结果总结
2025-07-16 01:58:38 - model_ensemble - INFO - ============================================================
2025-07-16 01:58:38 - model_ensemble - INFO - 最佳集成模型: stacking
2025-07-16 01:58:38 - model_ensemble - INFO - 最佳F1分数: 0.8000
2025-07-16 01:58:38 - model_ensemble - INFO - 最佳准确率: 0.8000
2025-07-16 01:58:38 - model_ensemble - INFO - 
所有集成模型性能对比:
2025-07-16 01:58:38 - model_ensemble - INFO -   stacking        - 准确率: 0.8000, 精确率: 0.8000, 召回率: 0.8000, F1: 0.8000, AUC: 0.8875
2025-07-16 01:58:38 - model_ensemble - INFO - 步骤3: 保存集成学习结果
2025-07-16 01:58:38 - model_ensemble - INFO - 集成学习结果已保存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\ensemble\ensemble_results_20250716_015838.joblib
2025-07-16 01:58:38 - model_ensemble - INFO - Ensemble report saved to: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\ensemble
2025-07-16 01:58:38 - model_ensemble - INFO - 步骤4: 生成SHAP可解释性分析
2025-07-16 01:58:38 - model_ensemble - INFO - 为 stacking 生成SHAP分析
2025-07-16 01:58:46 - model_ensemble - INFO -   stacking 完整SHAP分析完成
2025-07-16 01:58:46 - model_ensemble - INFO -     生成的图表类型: ['summary', 'importance', 'force', 'waterfall', 'dependence', 'decision']
2025-07-16 01:58:46 - model_ensemble - INFO -     summary: ['dot', 'bar']
2025-07-16 01:58:46 - model_ensemble - INFO -     importance: 1 个文件
2025-07-16 01:58:46 - model_ensemble - INFO -     force: 3 个文件
2025-07-16 01:58:46 - model_ensemble - INFO -     waterfall: 3 个文件
2025-07-16 01:58:46 - model_ensemble - INFO -     dependence: 5 个文件
2025-07-16 01:58:46 - model_ensemble - INFO -     decision: 1 个文件
2025-07-16 01:58:46 - model_ensemble - INFO -   stacking SHAP分析完成
2025-07-16 02:03:26 - model_ensemble - INFO - ============================================================
2025-07-16 02:03:26 - model_ensemble - INFO - 开始运行集成学习管道
2025-07-16 02:03:26 - model_ensemble - INFO - ============================================================
2025-07-16 02:03:26 - model_ensemble - INFO - 基础模型: ['DecisionTree', 'RandomForest', 'XGBoost', 'LightGBM', 'CatBoost', 'Logistic', 'SVM', 'KNN', 'NaiveBayes', 'NeuralNet']
2025-07-16 02:03:26 - model_ensemble - INFO - 集成方法: ['stacking']
2025-07-16 02:03:26 - model_ensemble - INFO - 步骤1: 训练基础模型
2025-07-16 02:03:26 - model_ensemble - INFO - 训练基础模型: DecisionTree
2025-07-16 02:03:26 - model_ensemble - INFO -   DecisionTree 训练完成
2025-07-16 02:03:26 - model_ensemble - INFO - 训练基础模型: RandomForest
2025-07-16 02:03:26 - model_ensemble - INFO -   RandomForest 训练完成
2025-07-16 02:03:26 - model_ensemble - INFO - 训练基础模型: XGBoost
2025-07-16 02:03:26 - model_ensemble - INFO -   XGBoost 训练完成
2025-07-16 02:03:26 - model_ensemble - INFO - 训练基础模型: LightGBM
2025-07-16 02:03:26 - model_ensemble - INFO -   LightGBM 训练完成
2025-07-16 02:03:26 - model_ensemble - INFO - 训练基础模型: CatBoost
2025-07-16 02:03:27 - model_ensemble - INFO -   CatBoost 训练完成
2025-07-16 02:03:27 - model_ensemble - INFO - 训练基础模型: Logistic
2025-07-16 02:03:27 - model_ensemble - INFO -   Logistic 训练完成
2025-07-16 02:03:27 - model_ensemble - INFO - 训练基础模型: SVM
2025-07-16 02:03:27 - model_ensemble - INFO -   SVM 训练完成
2025-07-16 02:03:27 - model_ensemble - INFO - 训练基础模型: KNN
2025-07-16 02:03:27 - model_ensemble - INFO -   KNN 训练完成
2025-07-16 02:03:27 - model_ensemble - INFO - 训练基础模型: NaiveBayes
2025-07-16 02:03:27 - model_ensemble - INFO -   NaiveBayes 训练完成
2025-07-16 02:03:27 - model_ensemble - INFO - 训练基础模型: NeuralNet
2025-07-16 02:03:28 - model_ensemble - INFO -   NeuralNet 训练完成
2025-07-16 02:03:28 - model_ensemble - INFO - 成功训练了 10 个基础模型
2025-07-16 02:03:28 - model_ensemble - INFO - 步骤2: 运行集成方法 - stacking
2025-07-16 02:03:28 - model_ensemble - INFO - 开始训练集成模型，方法: stacking
2025-07-16 02:03:37 - model_ensemble - INFO -   stacking - 准确率: 0.9000, F1: 0.8990
2025-07-16 02:03:37 - model_ensemble - INFO - ============================================================
2025-07-16 02:03:37 - model_ensemble - INFO - 集成学习结果总结
2025-07-16 02:03:37 - model_ensemble - INFO - ============================================================
2025-07-16 02:03:37 - model_ensemble - INFO - 最佳集成模型: stacking
2025-07-16 02:03:37 - model_ensemble - INFO - 最佳F1分数: 0.8990
2025-07-16 02:03:37 - model_ensemble - INFO - 最佳准确率: 0.9000
2025-07-16 02:03:37 - model_ensemble - INFO - 
所有集成模型性能对比:
2025-07-16 02:03:37 - model_ensemble - INFO -   stacking        - 准确率: 0.9000, 精确率: 0.9027, 召回率: 0.9000, F1: 0.8990, AUC: 0.9182
2025-07-16 02:03:37 - model_ensemble - INFO - 步骤3: 保存集成学习结果
2025-07-16 02:03:37 - model_ensemble - INFO - 集成学习结果已保存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\ensemble\ensemble_results_20250716_020337.joblib
2025-07-16 02:03:37 - model_ensemble - INFO - Ensemble report saved to: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\ensemble
2025-07-16 02:03:37 - model_ensemble - INFO - 步骤4: 生成SHAP可解释性分析
2025-07-16 02:03:37 - model_ensemble - INFO - 为 stacking 生成SHAP分析
2025-07-16 02:03:50 - model_ensemble - INFO -   stacking 完整SHAP分析完成
2025-07-16 02:03:50 - model_ensemble - INFO -     生成的图表类型: ['summary', 'importance', 'force', 'waterfall', 'dependence', 'decision']
2025-07-16 02:03:50 - model_ensemble - INFO -     summary: ['dot', 'bar']
2025-07-16 02:03:50 - model_ensemble - INFO -     importance: 1 个文件
2025-07-16 02:03:50 - model_ensemble - INFO -     force: 3 个文件
2025-07-16 02:03:50 - model_ensemble - INFO -     waterfall: 3 个文件
2025-07-16 02:03:50 - model_ensemble - INFO -     dependence: 5 个文件
2025-07-16 02:03:50 - model_ensemble - INFO -     decision: 1 个文件
2025-07-16 02:03:50 - model_ensemble - INFO -   stacking SHAP分析完成
2025-07-16 02:16:33 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-07-16 02:16:54 - model_ensemble - INFO - ============================================================
2025-07-16 02:16:54 - model_ensemble - INFO - 开始运行集成学习管道
2025-07-16 02:16:54 - model_ensemble - INFO - ============================================================
2025-07-16 02:16:54 - model_ensemble - INFO - 基础模型: ['RandomForest', 'XGBoost', 'LightGBM', 'Logistic']
2025-07-16 02:16:54 - model_ensemble - INFO - 集成方法: ['stacking']
2025-07-16 02:16:54 - model_ensemble - INFO - 步骤1: 训练基础模型
2025-07-16 02:16:54 - model_ensemble - INFO - 训练基础模型: RandomForest
2025-07-16 02:16:55 - model_ensemble - INFO -   RandomForest 训练完成
2025-07-16 02:16:55 - model_ensemble - INFO - 训练基础模型: XGBoost
2025-07-16 02:16:55 - model_ensemble - INFO -   XGBoost 训练完成
2025-07-16 02:16:55 - model_ensemble - INFO - 训练基础模型: LightGBM
2025-07-16 02:16:56 - model_ensemble - INFO -   LightGBM 训练完成
2025-07-16 02:16:56 - model_ensemble - INFO - 训练基础模型: Logistic
2025-07-16 02:16:56 - model_ensemble - INFO -   Logistic 训练完成
2025-07-16 02:16:56 - model_ensemble - INFO - 成功训练了 4 个基础模型
2025-07-16 02:16:56 - model_ensemble - INFO - 步骤2: 运行集成方法 - stacking
2025-07-16 02:16:56 - model_ensemble - INFO - 开始训练集成模型，方法: stacking
2025-07-16 02:16:58 - model_ensemble - INFO -   stacking - 准确率: 0.8250, F1: 0.8242
2025-07-16 02:16:58 - model_ensemble - INFO - ============================================================
2025-07-16 02:16:58 - model_ensemble - INFO - 集成学习结果总结
2025-07-16 02:16:58 - model_ensemble - INFO - ============================================================
2025-07-16 02:16:58 - model_ensemble - INFO - 最佳集成模型: stacking
2025-07-16 02:16:58 - model_ensemble - INFO - 最佳F1分数: 0.8242
2025-07-16 02:16:58 - model_ensemble - INFO - 最佳准确率: 0.8250
2025-07-16 02:16:58 - model_ensemble - INFO - 
所有集成模型性能对比:
2025-07-16 02:16:58 - model_ensemble - INFO -   stacking        - 准确率: 0.8250, 精确率: 0.8245, 召回率: 0.8250, F1: 0.8242, AUC: 0.8875
2025-07-16 02:16:58 - model_ensemble - INFO - 步骤3: 保存集成学习结果
2025-07-16 02:16:58 - model_ensemble - INFO - 集成学习结果已保存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\ensemble\ensemble_results_20250716_021658.joblib
2025-07-16 02:16:58 - model_ensemble - INFO - Ensemble report saved to: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\ensemble
2025-07-16 02:16:58 - model_ensemble - INFO - 步骤4: 生成SHAP可解释性分析
2025-07-16 02:16:58 - model_ensemble - INFO - 为 stacking 生成SHAP分析
2025-07-16 02:17:06 - model_ensemble - INFO -   stacking 完整SHAP分析完成
2025-07-16 02:17:06 - model_ensemble - INFO -     生成的图表类型: ['summary', 'importance', 'force', 'waterfall', 'dependence', 'decision']
2025-07-16 02:17:06 - model_ensemble - INFO -     summary: ['dot', 'bar']
2025-07-16 02:17:06 - model_ensemble - INFO -     importance: 1 个文件
2025-07-16 02:17:06 - model_ensemble - INFO -     force: 3 个文件
2025-07-16 02:17:06 - model_ensemble - INFO -     waterfall: 3 个文件
2025-07-16 02:17:06 - model_ensemble - INFO -     dependence: 5 个文件
2025-07-16 02:17:06 - model_ensemble - INFO -     decision: 1 个文件
2025-07-16 02:17:06 - model_ensemble - INFO -   stacking SHAP分析完成
2025-07-16 02:29:00 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-07-16 02:29:38 - model_ensemble - INFO - ============================================================
2025-07-16 02:29:38 - model_ensemble - INFO - 开始运行集成学习管道
2025-07-16 02:29:38 - model_ensemble - INFO - ============================================================
2025-07-16 02:29:38 - model_ensemble - INFO - 基础模型: ['DecisionTree', 'RandomForest', 'XGBoost', 'LightGBM', 'CatBoost', 'Logistic', 'SVM', 'KNN', 'NaiveBayes', 'NeuralNet']
2025-07-16 02:29:38 - model_ensemble - INFO - 集成方法: ['stacking']
2025-07-16 02:29:38 - model_ensemble - INFO - 步骤1: 训练基础模型
2025-07-16 02:29:38 - model_ensemble - INFO - 训练基础模型: DecisionTree
2025-07-16 02:29:38 - model_ensemble - INFO -   DecisionTree 训练完成
2025-07-16 02:29:38 - model_ensemble - INFO - 训练基础模型: RandomForest
2025-07-16 02:29:38 - model_ensemble - INFO -   RandomForest 训练完成
2025-07-16 02:29:38 - model_ensemble - INFO - 训练基础模型: XGBoost
2025-07-16 02:29:38 - model_ensemble - INFO -   XGBoost 训练完成
2025-07-16 02:29:38 - model_ensemble - INFO - 训练基础模型: LightGBM
2025-07-16 02:29:39 - model_ensemble - INFO -   LightGBM 训练完成
2025-07-16 02:29:39 - model_ensemble - INFO - 训练基础模型: CatBoost
2025-07-16 02:29:41 - model_ensemble - INFO -   CatBoost 训练完成
2025-07-16 02:29:41 - model_ensemble - INFO - 训练基础模型: Logistic
2025-07-16 02:29:41 - model_ensemble - INFO -   Logistic 训练完成
2025-07-16 02:29:41 - model_ensemble - INFO - 训练基础模型: SVM
2025-07-16 02:29:41 - model_ensemble - INFO -   SVM 训练完成
2025-07-16 02:29:41 - model_ensemble - INFO - 训练基础模型: KNN
2025-07-16 02:29:41 - model_ensemble - INFO -   KNN 训练完成
2025-07-16 02:29:41 - model_ensemble - INFO - 训练基础模型: NaiveBayes
2025-07-16 02:29:41 - model_ensemble - INFO -   NaiveBayes 训练完成
2025-07-16 02:29:41 - model_ensemble - INFO - 训练基础模型: NeuralNet
2025-07-16 02:29:41 - model_ensemble - INFO -   NeuralNet 训练完成
2025-07-16 02:29:41 - model_ensemble - INFO - 成功训练了 10 个基础模型
2025-07-16 02:29:41 - model_ensemble - INFO - 步骤2: 运行集成方法 - stacking
2025-07-16 02:29:41 - model_ensemble - INFO - 开始训练集成模型，方法: stacking
2025-07-16 02:29:51 - model_ensemble - INFO -   stacking - 准确率: 0.9000, F1: 0.8990
2025-07-16 02:29:51 - model_ensemble - INFO - ============================================================
2025-07-16 02:29:51 - model_ensemble - INFO - 集成学习结果总结
2025-07-16 02:29:51 - model_ensemble - INFO - ============================================================
2025-07-16 02:29:51 - model_ensemble - INFO - 最佳集成模型: stacking
2025-07-16 02:29:51 - model_ensemble - INFO - 最佳F1分数: 0.8990
2025-07-16 02:29:51 - model_ensemble - INFO - 最佳准确率: 0.9000
2025-07-16 02:29:51 - model_ensemble - INFO - 
所有集成模型性能对比:
2025-07-16 02:29:51 - model_ensemble - INFO -   stacking        - 准确率: 0.9000, 精确率: 0.9027, 召回率: 0.9000, F1: 0.8990, AUC: 0.9182
2025-07-16 02:29:51 - model_ensemble - INFO - 步骤3: 保存集成学习结果
2025-07-16 02:29:51 - model_ensemble - INFO - 集成学习结果已保存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\ensemble\ensemble_results_20250716_022951.joblib
2025-07-16 02:29:52 - model_ensemble - WARNING - Failed to save ensemble report: main thread is not in main loop
2025-07-16 02:29:52 - model_ensemble - INFO - 步骤4: 生成SHAP可解释性分析
2025-07-16 02:29:52 - model_ensemble - INFO - 为 stacking 生成SHAP分析
2025-07-16 02:30:06 - model_ensemble - INFO -   stacking 完整SHAP分析完成
2025-07-16 02:30:06 - model_ensemble - INFO -     生成的图表类型: ['summary', 'importance', 'force', 'waterfall', 'dependence', 'decision']
2025-07-16 02:30:06 - model_ensemble - INFO -     summary: ['dot', 'bar']
2025-07-16 02:30:06 - model_ensemble - INFO -     importance: 1 个文件
2025-07-16 02:30:06 - model_ensemble - INFO -     force: 3 个文件
2025-07-16 02:30:06 - model_ensemble - INFO -     waterfall: 3 个文件
2025-07-16 02:30:06 - model_ensemble - INFO -     dependence: 5 个文件
2025-07-16 02:30:06 - model_ensemble - INFO -     decision: 1 个文件
2025-07-16 02:30:06 - model_ensemble - INFO -   stacking SHAP分析完成
2025-07-16 22:45:04 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-07-16 23:07:15 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-07-16 23:18:55 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-07-16 23:28:36 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-07-16 23:29:11 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-07-16 23:34:44 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-07-16 23:42:16 - model_ensemble - INFO - ============================================================
2025-07-16 23:42:16 - model_ensemble - INFO - 开始运行集成学习管道
2025-07-16 23:42:16 - model_ensemble - INFO - ============================================================
2025-07-16 23:42:16 - model_ensemble - INFO - 基础模型: ['DecisionTree', 'Logistic', 'NaiveBayes']
2025-07-16 23:42:16 - model_ensemble - INFO - 集成方法: ['stacking']
2025-07-16 23:42:16 - model_ensemble - INFO - 步骤1: 训练基础模型
2025-07-16 23:42:16 - model_ensemble - INFO - 训练基础模型: DecisionTree
2025-07-16 23:42:16 - model_ensemble - INFO -   DecisionTree 训练完成
2025-07-16 23:42:16 - model_ensemble - INFO - 训练基础模型: Logistic
2025-07-16 23:42:16 - model_ensemble - INFO -   Logistic 训练完成
2025-07-16 23:42:16 - model_ensemble - INFO - 训练基础模型: NaiveBayes
2025-07-16 23:42:16 - model_ensemble - INFO -   NaiveBayes 训练完成
2025-07-16 23:42:16 - model_ensemble - INFO - 成功训练了 3 个基础模型
2025-07-16 23:42:16 - model_ensemble - INFO - 步骤2: 运行集成方法 - stacking
2025-07-16 23:42:16 - model_ensemble - INFO - 开始训练集成模型，方法: stacking
2025-07-16 23:42:16 - model_ensemble - INFO -   stacking - 准确率: 0.8000, F1: 0.7947
2025-07-16 23:42:16 - model_ensemble - INFO - ============================================================
2025-07-16 23:42:16 - model_ensemble - INFO - 集成学习结果总结
2025-07-16 23:42:16 - model_ensemble - INFO - ============================================================
2025-07-16 23:42:16 - model_ensemble - INFO - 最佳集成模型: stacking
2025-07-16 23:42:16 - model_ensemble - INFO - 最佳F1分数: 0.7947
2025-07-16 23:42:16 - model_ensemble - INFO - 最佳准确率: 0.8000
2025-07-16 23:42:16 - model_ensemble - INFO - 
所有集成模型性能对比:
2025-07-16 23:42:16 - model_ensemble - INFO -   stacking        - 准确率: 0.8000, 精确率: 0.8068, 召回率: 0.8000, F1: 0.7947, AUC: 0.8849
2025-07-16 23:42:16 - model_ensemble - INFO - 步骤3: 保存集成学习结果
2025-07-16 23:42:16 - model_ensemble - INFO - 集成学习结果已保存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\ensemble\ensemble_results_20250716_234216.joblib
2025-07-16 23:42:16 - model_ensemble - INFO - Ensemble report saved to: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\ensemble
2025-07-16 23:42:16 - model_ensemble - INFO - 步骤4: 生成SHAP可解释性分析
2025-07-16 23:42:16 - model_ensemble - INFO - 为 stacking 生成SHAP分析
2025-07-16 23:42:21 - model_ensemble - INFO -   stacking 完整SHAP分析完成
2025-07-16 23:42:21 - model_ensemble - INFO -     生成的图表类型: ['summary', 'importance', 'force', 'waterfall', 'dependence', 'decision']
2025-07-16 23:42:21 - model_ensemble - INFO -     summary: ['dot', 'bar']
2025-07-16 23:42:21 - model_ensemble - INFO -     importance: 1 个文件
2025-07-16 23:42:21 - model_ensemble - INFO -     force: 3 个文件
2025-07-16 23:42:21 - model_ensemble - INFO -     waterfall: 3 个文件
2025-07-16 23:42:21 - model_ensemble - INFO -     dependence: 5 个文件
2025-07-16 23:42:21 - model_ensemble - INFO -     decision: 1 个文件
2025-07-16 23:42:21 - model_ensemble - INFO -   stacking SHAP分析完成
2025-07-16 23:46:23 - model_ensemble - INFO - ============================================================
2025-07-16 23:46:23 - model_ensemble - INFO - 开始运行集成学习管道
2025-07-16 23:46:23 - model_ensemble - INFO - ============================================================
2025-07-16 23:46:23 - model_ensemble - INFO - 基础模型: ['DecisionTree', 'LightGBM', 'NeuralNet']
2025-07-16 23:46:23 - model_ensemble - INFO - 集成方法: ['stacking']
2025-07-16 23:46:23 - model_ensemble - INFO - 步骤1: 训练基础模型
2025-07-16 23:46:23 - model_ensemble - INFO - 训练基础模型: DecisionTree
2025-07-16 23:46:23 - model_ensemble - INFO -   DecisionTree 训练完成
2025-07-16 23:46:23 - model_ensemble - INFO - 训练基础模型: LightGBM
2025-07-16 23:46:23 - model_ensemble - INFO -   LightGBM 训练完成
2025-07-16 23:46:23 - model_ensemble - INFO - 训练基础模型: NeuralNet
2025-07-16 23:46:23 - model_ensemble - INFO -   NeuralNet 训练完成
2025-07-16 23:46:23 - model_ensemble - INFO - 成功训练了 3 个基础模型
2025-07-16 23:46:23 - model_ensemble - INFO - 步骤2: 运行集成方法 - stacking
2025-07-16 23:46:23 - model_ensemble - INFO - 开始训练集成模型，方法: stacking
2025-07-16 23:46:26 - model_ensemble - INFO -   stacking - 准确率: 0.8750, F1: 0.8754
2025-07-16 23:46:26 - model_ensemble - INFO - ============================================================
2025-07-16 23:46:26 - model_ensemble - INFO - 集成学习结果总结
2025-07-16 23:46:26 - model_ensemble - INFO - ============================================================
2025-07-16 23:46:26 - model_ensemble - INFO - 最佳集成模型: stacking
2025-07-16 23:46:26 - model_ensemble - INFO - 最佳F1分数: 0.8754
2025-07-16 23:46:26 - model_ensemble - INFO - 最佳准确率: 0.8750
2025-07-16 23:46:26 - model_ensemble - INFO - 
所有集成模型性能对比:
2025-07-16 23:46:26 - model_ensemble - INFO -   stacking        - 准确率: 0.8750, 精确率: 0.8769, 召回率: 0.8750, F1: 0.8754, AUC: 0.9258
2025-07-16 23:46:26 - model_ensemble - INFO - 步骤3: 保存集成学习结果
2025-07-16 23:46:26 - model_ensemble - INFO - 集成学习结果已保存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\ensemble\ensemble_results_20250716_234626.joblib
2025-07-16 23:46:26 - model_ensemble - INFO - Ensemble report saved to: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\ensemble
2025-07-16 23:46:26 - model_ensemble - INFO - 步骤4: 生成SHAP可解释性分析
2025-07-16 23:46:26 - model_ensemble - INFO - 为 stacking 生成SHAP分析
2025-07-16 23:46:32 - model_ensemble - INFO -   stacking 完整SHAP分析完成
2025-07-16 23:46:32 - model_ensemble - INFO -     生成的图表类型: ['summary', 'importance', 'force', 'waterfall', 'dependence', 'decision']
2025-07-16 23:46:32 - model_ensemble - INFO -     summary: ['dot', 'bar']
2025-07-16 23:46:32 - model_ensemble - INFO -     importance: 1 个文件
2025-07-16 23:46:32 - model_ensemble - INFO -     force: 3 个文件
2025-07-16 23:46:32 - model_ensemble - INFO -     waterfall: 3 个文件
2025-07-16 23:46:32 - model_ensemble - INFO -     dependence: 5 个文件
2025-07-16 23:46:32 - model_ensemble - INFO -     decision: 1 个文件
2025-07-16 23:46:32 - model_ensemble - INFO -   stacking SHAP分析完成
2025-07-16 23:47:55 - model_ensemble - INFO - ============================================================
2025-07-16 23:47:55 - model_ensemble - INFO - 开始运行集成学习管道
2025-07-16 23:47:55 - model_ensemble - INFO - ============================================================
2025-07-16 23:47:55 - model_ensemble - INFO - 基础模型: ['DecisionTree', 'KNN', 'NeuralNet']
2025-07-16 23:47:55 - model_ensemble - INFO - 集成方法: ['stacking']
2025-07-16 23:47:55 - model_ensemble - INFO - 步骤1: 训练基础模型
2025-07-16 23:47:55 - model_ensemble - INFO - 训练基础模型: DecisionTree
2025-07-16 23:47:55 - model_ensemble - INFO -   DecisionTree 训练完成
2025-07-16 23:47:55 - model_ensemble - INFO - 训练基础模型: KNN
2025-07-16 23:47:55 - model_ensemble - INFO -   KNN 训练完成
2025-07-16 23:47:55 - model_ensemble - INFO - 训练基础模型: NeuralNet
2025-07-16 23:47:55 - model_ensemble - INFO -   NeuralNet 训练完成
2025-07-16 23:47:55 - model_ensemble - INFO - 成功训练了 3 个基础模型
2025-07-16 23:47:55 - model_ensemble - INFO - 步骤2: 运行集成方法 - stacking
2025-07-16 23:47:55 - model_ensemble - INFO - 开始训练集成模型，方法: stacking
2025-07-16 23:47:57 - model_ensemble - INFO -   stacking - 准确率: 0.8750, F1: 0.8744
2025-07-16 23:47:57 - model_ensemble - INFO - ============================================================
2025-07-16 23:47:57 - model_ensemble - INFO - 集成学习结果总结
2025-07-16 23:47:57 - model_ensemble - INFO - ============================================================
2025-07-16 23:47:57 - model_ensemble - INFO - 最佳集成模型: stacking
2025-07-16 23:47:57 - model_ensemble - INFO - 最佳F1分数: 0.8744
2025-07-16 23:47:57 - model_ensemble - INFO - 最佳准确率: 0.8750
2025-07-16 23:47:57 - model_ensemble - INFO - 
所有集成模型性能对比:
2025-07-16 23:47:57 - model_ensemble - INFO -   stacking        - 准确率: 0.8750, 精确率: 0.8750, 召回率: 0.8750, F1: 0.8744, AUC: 0.9668
2025-07-16 23:47:57 - model_ensemble - INFO - 步骤3: 保存集成学习结果
2025-07-16 23:47:57 - model_ensemble - INFO - 集成学习结果已保存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\ensemble\ensemble_results_20250716_234757.joblib
2025-07-16 23:47:57 - model_ensemble - INFO - Ensemble report saved to: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\ensemble
2025-07-16 23:47:57 - model_ensemble - INFO - 步骤4: 生成SHAP可解释性分析
2025-07-16 23:47:57 - model_ensemble - INFO - 为 stacking 生成SHAP分析
2025-07-16 23:48:03 - model_ensemble - INFO -   stacking 完整SHAP分析完成
2025-07-16 23:48:03 - model_ensemble - INFO -     生成的图表类型: ['summary', 'importance', 'force', 'waterfall', 'dependence', 'decision']
2025-07-16 23:48:03 - model_ensemble - INFO -     summary: ['dot', 'bar']
2025-07-16 23:48:03 - model_ensemble - INFO -     importance: 1 个文件
2025-07-16 23:48:03 - model_ensemble - INFO -     force: 3 个文件
2025-07-16 23:48:03 - model_ensemble - INFO -     waterfall: 3 个文件
2025-07-16 23:48:03 - model_ensemble - INFO -     dependence: 5 个文件
2025-07-16 23:48:03 - model_ensemble - INFO -     decision: 1 个文件
2025-07-16 23:48:03 - model_ensemble - INFO -   stacking SHAP分析完成
2025-07-16 23:54:01 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-07-16 23:55:04 - model_ensemble - INFO - ============================================================
2025-07-16 23:55:04 - model_ensemble - INFO - 开始运行集成学习管道
2025-07-16 23:55:04 - model_ensemble - INFO - ============================================================
2025-07-16 23:55:04 - model_ensemble - INFO - 基础模型: ['DecisionTree', 'KNN', 'NeuralNet']
2025-07-16 23:55:04 - model_ensemble - INFO - 集成方法: ['stacking']
2025-07-16 23:55:04 - model_ensemble - INFO - 步骤1: 训练基础模型
2025-07-16 23:55:04 - model_ensemble - INFO - 训练基础模型: DecisionTree
2025-07-16 23:55:04 - model_ensemble - INFO -   DecisionTree 训练完成
2025-07-16 23:55:04 - model_ensemble - INFO - 训练基础模型: KNN
2025-07-16 23:55:04 - model_ensemble - INFO -   KNN 训练完成
2025-07-16 23:55:04 - model_ensemble - INFO - 训练基础模型: NeuralNet
2025-07-16 23:55:05 - model_ensemble - INFO -   NeuralNet 训练完成
2025-07-16 23:55:05 - model_ensemble - INFO - 成功训练了 3 个基础模型
2025-07-16 23:55:05 - model_ensemble - INFO - 步骤2: 运行集成方法 - stacking
2025-07-16 23:55:05 - model_ensemble - INFO - 开始训练集成模型，方法: stacking
2025-07-16 23:55:08 - model_ensemble - INFO -   stacking - 准确率: 0.8250, F1: 0.8116
2025-07-16 23:55:08 - model_ensemble - INFO - ============================================================
2025-07-16 23:55:08 - model_ensemble - INFO - 集成学习结果总结
2025-07-16 23:55:08 - model_ensemble - INFO - ============================================================
2025-07-16 23:55:08 - model_ensemble - INFO - 最佳集成模型: stacking
2025-07-16 23:55:08 - model_ensemble - INFO - 最佳F1分数: 0.8116
2025-07-16 23:55:08 - model_ensemble - INFO - 最佳准确率: 0.8250
2025-07-16 23:55:08 - model_ensemble - INFO - 
所有集成模型性能对比:
2025-07-16 23:55:08 - model_ensemble - INFO -   stacking        - 准确率: 0.8250, 精确率: 0.8328, 召回率: 0.8250, F1: 0.8116, AUC: 0.9174
2025-07-16 23:55:08 - model_ensemble - INFO - 步骤3: 保存集成学习结果
2025-07-16 23:55:08 - model_ensemble - INFO - 集成学习结果已保存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\ensemble\ensemble_results_20250716_235508.joblib
2025-07-16 23:55:08 - model_ensemble - INFO - Ensemble report saved to: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\ensemble
2025-07-16 23:55:08 - model_ensemble - INFO - 步骤4: 生成SHAP可解释性分析
2025-07-16 23:55:08 - model_ensemble - INFO - 为 stacking 生成SHAP分析
2025-07-16 23:55:13 - model_ensemble - INFO -   stacking 完整SHAP分析完成
2025-07-16 23:55:13 - model_ensemble - INFO -     生成的图表类型: ['summary', 'importance', 'force', 'waterfall', 'dependence', 'decision']
2025-07-16 23:55:13 - model_ensemble - INFO -     summary: ['dot', 'bar']
2025-07-16 23:55:13 - model_ensemble - INFO -     importance: 1 个文件
2025-07-16 23:55:13 - model_ensemble - INFO -     force: 3 个文件
2025-07-16 23:55:13 - model_ensemble - INFO -     waterfall: 3 个文件
2025-07-16 23:55:13 - model_ensemble - INFO -     dependence: 5 个文件
2025-07-16 23:55:13 - model_ensemble - INFO -     decision: 1 个文件
2025-07-16 23:55:13 - model_ensemble - INFO -   stacking SHAP分析完成
2025-07-16 23:59:25 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-07-17 00:21:58 - model_ensemble - INFO - ============================================================
2025-07-17 00:21:58 - model_ensemble - INFO - 开始运行集成学习管道
2025-07-17 00:21:58 - model_ensemble - INFO - ============================================================
2025-07-17 00:21:58 - model_ensemble - INFO - 基础模型: ['DecisionTree', 'KNN', 'NeuralNet']
2025-07-17 00:21:58 - model_ensemble - INFO - 集成方法: ['voting', 'stacking']
2025-07-17 00:21:58 - model_ensemble - INFO - 步骤1: 训练基础模型
2025-07-17 00:21:58 - model_ensemble - INFO - 训练基础模型: DecisionTree
2025-07-17 00:21:58 - model_ensemble - INFO -   DecisionTree 训练完成
2025-07-17 00:21:58 - model_ensemble - INFO - 训练基础模型: KNN
2025-07-17 00:21:58 - model_ensemble - INFO -   KNN 训练完成
2025-07-17 00:21:58 - model_ensemble - INFO - 训练基础模型: NeuralNet
2025-07-17 00:21:58 - model_ensemble - INFO -   NeuralNet 训练完成
2025-07-17 00:21:58 - model_ensemble - INFO - 成功训练了 3 个基础模型
2025-07-17 00:21:58 - model_ensemble - INFO - 步骤2: 运行集成方法 - voting
2025-07-17 00:21:58 - model_ensemble - INFO -   训练 voting_soft 集成模型
2025-07-17 00:21:58 - model_ensemble - INFO - 开始训练集成模型，方法: voting
2025-07-17 00:21:59 - model_ensemble - INFO -     voting_soft - 准确率: 0.8000, F1: 0.7952
2025-07-17 00:21:59 - model_ensemble - INFO -   训练 voting_hard 集成模型
2025-07-17 00:21:59 - model_ensemble - INFO - 开始训练集成模型，方法: voting
2025-07-17 00:21:59 - model_ensemble - INFO -     voting_hard - 准确率: 0.8500, F1: 0.8415
2025-07-17 00:21:59 - model_ensemble - INFO - 步骤2: 运行集成方法 - stacking
2025-07-17 00:21:59 - model_ensemble - INFO - 开始训练集成模型，方法: stacking
2025-07-17 00:22:02 - model_ensemble - INFO -   stacking - 准确率: 0.8250, F1: 0.8116
2025-07-17 00:22:02 - model_ensemble - INFO - ============================================================
2025-07-17 00:22:02 - model_ensemble - INFO - 集成学习结果总结
2025-07-17 00:22:02 - model_ensemble - INFO - ============================================================
2025-07-17 00:22:02 - model_ensemble - INFO - 最佳集成模型: voting_hard
2025-07-17 00:22:02 - model_ensemble - INFO - 最佳F1分数: 0.8415
2025-07-17 00:22:02 - model_ensemble - INFO - 最佳准确率: 0.8500
2025-07-17 00:22:02 - model_ensemble - INFO - 
所有集成模型性能对比:
2025-07-17 00:22:02 - model_ensemble - INFO -   voting_soft     - 准确率: 0.8000, 精确率: 0.7950, 召回率: 0.8000, F1: 0.7952, AUC: 0.8803
2025-07-17 00:22:02 - model_ensemble - INFO -   voting_hard     - 准确率: 0.8500, 精确率: 0.8550, 召回率: 0.8500, F1: 0.8415, AUC: 0.0000
2025-07-17 00:22:02 - model_ensemble - INFO -   stacking        - 准确率: 0.8250, 精确率: 0.8328, 召回率: 0.8250, F1: 0.8116, AUC: 0.9174
2025-07-17 00:22:02 - model_ensemble - INFO - 步骤3: 保存集成学习结果
2025-07-17 00:22:02 - model_ensemble - INFO - 集成学习结果已保存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\ensemble\ensemble_results_20250717_002202.joblib
2025-07-17 00:22:02 - model_ensemble - INFO - Ensemble report saved to: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\ensemble
2025-07-17 00:22:02 - model_ensemble - INFO - 步骤4: 生成SHAP可解释性分析
2025-07-17 00:22:02 - model_ensemble - INFO - 为 voting_soft 生成SHAP分析
2025-07-17 00:22:07 - model_ensemble - INFO -   voting_soft 完整SHAP分析完成
2025-07-17 00:22:07 - model_ensemble - INFO -     生成的图表类型: ['summary', 'importance', 'force', 'waterfall', 'dependence', 'decision']
2025-07-17 00:22:07 - model_ensemble - INFO -     summary: ['dot', 'bar']
2025-07-17 00:22:07 - model_ensemble - INFO -     importance: 1 个文件
2025-07-17 00:22:07 - model_ensemble - INFO -     force: 3 个文件
2025-07-17 00:22:07 - model_ensemble - INFO -     waterfall: 3 个文件
2025-07-17 00:22:07 - model_ensemble - INFO -     dependence: 5 个文件
2025-07-17 00:22:07 - model_ensemble - INFO -     decision: 1 个文件
2025-07-17 00:22:07 - model_ensemble - INFO -   voting_soft SHAP分析完成
2025-07-17 00:22:07 - model_ensemble - INFO - 为 voting_hard 生成SHAP分析
2025-07-17 00:22:19 - model_ensemble - INFO -   voting_hard 完整SHAP分析完成
2025-07-17 00:22:19 - model_ensemble - INFO -     生成的图表类型: ['summary', 'importance', 'force', 'waterfall', 'dependence', 'decision']
2025-07-17 00:22:19 - model_ensemble - INFO -     summary: ['dot', 'bar']
2025-07-17 00:22:19 - model_ensemble - INFO -     importance: 1 个文件
2025-07-17 00:22:19 - model_ensemble - INFO -     force: 3 个文件
2025-07-17 00:22:19 - model_ensemble - INFO -     waterfall: 3 个文件
2025-07-17 00:22:19 - model_ensemble - INFO -     dependence: 5 个文件
2025-07-17 00:22:19 - model_ensemble - INFO -     decision: 1 个文件
2025-07-17 00:22:19 - model_ensemble - INFO -   voting_hard SHAP分析完成
2025-07-17 00:22:19 - model_ensemble - INFO - 为 stacking 生成SHAP分析
2025-07-17 00:22:25 - model_ensemble - INFO -   stacking 完整SHAP分析完成
2025-07-17 00:22:25 - model_ensemble - INFO -     生成的图表类型: ['summary', 'importance', 'force', 'waterfall', 'dependence', 'decision']
2025-07-17 00:22:25 - model_ensemble - INFO -     summary: ['dot', 'bar']
2025-07-17 00:22:25 - model_ensemble - INFO -     importance: 1 个文件
2025-07-17 00:22:25 - model_ensemble - INFO -     force: 3 个文件
2025-07-17 00:22:25 - model_ensemble - INFO -     waterfall: 3 个文件
2025-07-17 00:22:25 - model_ensemble - INFO -     dependence: 5 个文件
2025-07-17 00:22:25 - model_ensemble - INFO -     decision: 1 个文件
2025-07-17 00:22:25 - model_ensemble - INFO -   stacking SHAP分析完成
2025-07-17 00:26:42 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-07-17 00:28:15 - model_ensemble - INFO - ============================================================
2025-07-17 00:28:15 - model_ensemble - INFO - 开始运行集成学习管道
2025-07-17 00:28:15 - model_ensemble - INFO - ============================================================
2025-07-17 00:28:15 - model_ensemble - INFO - 基础模型: ['DecisionTree', 'KNN', 'NeuralNet']
2025-07-17 00:28:15 - model_ensemble - INFO - 集成方法: ['voting', 'stacking']
2025-07-17 00:28:15 - model_ensemble - INFO - 步骤1: 训练基础模型
2025-07-17 00:28:15 - model_ensemble - INFO - 训练基础模型: DecisionTree
2025-07-17 00:28:15 - model_ensemble - INFO -   DecisionTree 训练完成
2025-07-17 00:28:15 - model_ensemble - INFO - 训练基础模型: KNN
2025-07-17 00:28:15 - model_ensemble - INFO -   KNN 训练完成
2025-07-17 00:28:15 - model_ensemble - INFO - 训练基础模型: NeuralNet
2025-07-17 00:28:15 - model_ensemble - INFO -   NeuralNet 训练完成
2025-07-17 00:28:15 - model_ensemble - INFO - 成功训练了 3 个基础模型
2025-07-17 00:28:15 - model_ensemble - INFO - 步骤2: 运行集成方法 - voting
2025-07-17 00:28:15 - model_ensemble - INFO -   训练 voting_soft 集成模型
2025-07-17 00:28:15 - model_ensemble - INFO - 开始训练集成模型，方法: voting
2025-07-17 00:28:16 - model_ensemble - INFO -     voting_soft - 准确率: 0.8000, F1: 0.7952
2025-07-17 00:28:16 - model_ensemble - INFO -   训练 voting_hard 集成模型
2025-07-17 00:28:16 - model_ensemble - INFO - 开始训练集成模型，方法: voting
2025-07-17 00:28:16 - model_ensemble - INFO -     voting_hard - 准确率: 0.8500, F1: 0.8415
2025-07-17 00:28:16 - model_ensemble - INFO - 步骤2: 运行集成方法 - stacking
2025-07-17 00:28:16 - model_ensemble - INFO - 开始训练集成模型，方法: stacking
2025-07-17 00:28:19 - model_ensemble - INFO -   stacking - 准确率: 0.8250, F1: 0.8116
2025-07-17 00:28:19 - model_ensemble - INFO - ============================================================
2025-07-17 00:28:19 - model_ensemble - INFO - 集成学习结果总结
2025-07-17 00:28:19 - model_ensemble - INFO - ============================================================
2025-07-17 00:28:19 - model_ensemble - INFO - 最佳集成模型: voting_hard
2025-07-17 00:28:19 - model_ensemble - INFO - 最佳F1分数: 0.8415
2025-07-17 00:28:19 - model_ensemble - INFO - 最佳准确率: 0.8500
2025-07-17 00:28:19 - model_ensemble - INFO - 
所有集成模型性能对比:
2025-07-17 00:28:19 - model_ensemble - INFO -   voting_soft     - 准确率: 0.8000, 精确率: 0.7950, 召回率: 0.8000, F1: 0.7952, AUC: 0.8803
2025-07-17 00:28:19 - model_ensemble - INFO -   voting_hard     - 准确率: 0.8500, 精确率: 0.8550, 召回率: 0.8500, F1: 0.8415, AUC: 0.0000
2025-07-17 00:28:19 - model_ensemble - INFO -   stacking        - 准确率: 0.8250, 精确率: 0.8328, 召回率: 0.8250, F1: 0.8116, AUC: 0.9174
2025-07-17 00:28:19 - model_ensemble - INFO - 步骤3: 保存集成学习结果
2025-07-17 00:28:19 - model_ensemble - INFO - 集成学习结果已保存到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\ensemble\ensemble_results_20250717_002819.joblib
2025-07-17 00:28:19 - model_ensemble - INFO - Ensemble report saved to: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\ensemble
2025-07-17 00:28:19 - model_ensemble - INFO - 步骤4: 生成SHAP可解释性分析
2025-07-17 00:28:19 - model_ensemble - INFO - 为 voting_soft 生成SHAP分析
2025-07-17 00:28:24 - model_ensemble - INFO -   voting_soft 完整SHAP分析完成
2025-07-17 00:28:24 - model_ensemble - INFO -     生成的图表类型: ['summary', 'importance', 'force', 'waterfall', 'dependence', 'decision']
2025-07-17 00:28:24 - model_ensemble - INFO -     summary: ['dot', 'bar']
2025-07-17 00:28:24 - model_ensemble - INFO -     importance: 1 个文件
2025-07-17 00:28:24 - model_ensemble - INFO -     force: 3 个文件
2025-07-17 00:28:24 - model_ensemble - INFO -     waterfall: 3 个文件
2025-07-17 00:28:24 - model_ensemble - INFO -     dependence: 5 个文件
2025-07-17 00:28:24 - model_ensemble - INFO -     decision: 1 个文件
2025-07-17 00:28:24 - model_ensemble - INFO -   voting_soft SHAP分析完成
2025-07-17 00:28:24 - model_ensemble - INFO - 为 voting_hard 生成SHAP分析
2025-07-17 00:28:38 - model_ensemble - INFO -   voting_hard 完整SHAP分析完成
2025-07-17 00:28:38 - model_ensemble - INFO -     生成的图表类型: ['summary', 'importance', 'force', 'waterfall', 'dependence', 'decision']
2025-07-17 00:28:38 - model_ensemble - INFO -     summary: ['dot', 'bar']
2025-07-17 00:28:38 - model_ensemble - INFO -     importance: 1 个文件
2025-07-17 00:28:38 - model_ensemble - INFO -     force: 3 个文件
2025-07-17 00:28:38 - model_ensemble - INFO -     waterfall: 3 个文件
2025-07-17 00:28:38 - model_ensemble - INFO -     dependence: 5 个文件
2025-07-17 00:28:38 - model_ensemble - INFO -     decision: 1 个文件
2025-07-17 00:28:38 - model_ensemble - INFO -   voting_hard SHAP分析完成
2025-07-17 00:28:38 - model_ensemble - INFO - 为 stacking 生成SHAP分析
2025-07-17 00:28:46 - model_ensemble - INFO -   stacking 完整SHAP分析完成
2025-07-17 00:28:46 - model_ensemble - INFO -     生成的图表类型: ['summary', 'importance', 'force', 'waterfall', 'dependence', 'decision']
2025-07-17 00:28:46 - model_ensemble - INFO -     summary: ['dot', 'bar']
2025-07-17 00:28:46 - model_ensemble - INFO -     importance: 1 个文件
2025-07-17 00:28:46 - model_ensemble - INFO -     force: 3 个文件
2025-07-17 00:28:46 - model_ensemble - INFO -     waterfall: 3 个文件
2025-07-17 00:28:46 - model_ensemble - INFO -     dependence: 5 个文件
2025-07-17 00:28:46 - model_ensemble - INFO -     decision: 1 个文件
2025-07-17 00:28:46 - model_ensemble - INFO -   stacking SHAP分析完成
2025-07-18 19:52:15 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-07-18 19:53:29 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-07-18 19:55:46 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-07-18 19:57:07 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-07-18 19:57:43 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-07-18 20:14:55 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-07-18 20:45:40 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-07-19 01:34:21 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-07-19 01:49:34 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-07-19 02:36:30 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-08-07 00:22:25 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-08-07 01:07:03 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-08-07 01:30:46 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-08-07 01:36:19 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-08-07 01:46:02 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-08-07 01:52:37 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-08-07 01:58:44 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-08-07 02:02:18 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-08-07 02:12:03 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-08-07 02:15:56 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-08-07 02:40:54 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-08-07 02:46:29 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-08-07 02:59:50 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-08-07 03:01:15 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-08-07 03:10:58 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-08-07 03:14:56 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-08-07 03:15:37 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-08-07 03:22:26 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-08-07 03:23:19 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-08-07 03:24:40 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-08-07 03:32:58 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-08-07 03:33:55 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-08-07 03:44:36 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-08-07 03:47:37 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-08-07 03:59:53 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-08-07 04:01:56 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-08-07 04:12:21 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-08-07 04:13:31 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-08-07 04:16:47 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-08-07 04:19:14 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-08-07 04:20:19 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-08-07 04:35:38 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-08-07 04:43:48 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-08-07 04:44:57 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-08-07 04:51:55 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-08-07 04:55:05 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-08-07 05:08:55 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-08-07 05:19:29 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-08-07 05:46:29 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-08-07 05:46:40 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-08-07 05:47:08 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-08-07 05:47:44 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-08-07 05:48:51 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-08-07 05:49:47 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-08-07 05:50:41 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-08-07 05:51:27 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-08-07 05:51:57 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-08-07 05:53:05 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-08-07 05:53:40 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-08-07 05:56:01 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-08-07 06:00:07 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-08-07 06:01:41 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-08-07 06:02:31 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-08-07 06:02:57 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-08-07 06:05:07 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-08-07 06:08:20 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-08-07 06:18:32 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-08-07 06:21:20 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-08-07 06:22:00 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-08-07 06:25:31 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-08-07 06:32:37 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-08-07 06:43:53 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-08-07 06:54:30 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-08-07 06:56:18 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-08-07 07:09:03 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-08-07 07:21:11 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-08-07 07:32:11 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-08-07 07:42:19 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-08-07 07:46:27 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-08-07 07:48:03 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-08-07 07:49:06 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-08-07 08:00:46 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-08-07 08:09:07 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-08-07 08:10:51 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-08-07 08:15:39 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-08-07 08:18:31 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-08-07 08:23:42 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-08-07 08:24:59 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-08-07 08:54:18 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-08-07 08:58:33 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-08-07 09:09:43 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-08-07 09:11:13 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-08-07 09:17:19 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-08-07 09:22:50 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-08-07 09:31:45 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-08-07 09:38:13 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
