2025-08-07 08:59:53 - hyperparameter_tuning - INFO - 开始对 RandomForest 进行超参数调优，试验次数: 50
2025-08-07 08:59:53 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 4
2025-08-07 08:59:53 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-07 08:59:53 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-07 08:59:53 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 50, 'n_jobs': 4, 'timeout': 1800, 'callbacks': [<function tune_model.<locals>.early_stopping_callback at 0x000001CA529461F0>]}
2025-08-07 08:59:54 - hyperparameter_tuning - INFO - Trial 2: 发现更好的得分 0.9786
2025-08-07 08:59:55 - hyperparameter_tuning - INFO - Trial 3: 发现更好的得分 0.9809
2025-08-07 09:00:01 - hyperparameter_tuning - INFO - Trial 11: 发现更好的得分 0.9826
2025-08-07 09:00:10 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-07 09:00:10 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9835
2025-08-07 09:00:10 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-07 09:00:10 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9835
2025-08-07 09:00:10 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-07 09:00:10 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9835
2025-08-07 09:00:11 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-07 09:00:11 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9835
2025-08-07 09:00:11 - hyperparameter_tuning - INFO - 模型 RandomForest 的最佳参数: {'n_estimators': 152, 'max_depth': 6, 'min_samples_split': 19, 'min_samples_leaf': 11, 'max_features': 'log2'}
2025-08-07 09:00:11 - hyperparameter_tuning - INFO - 模型 RandomForest 的最佳得分: 0.9835
2025-08-07 09:00:11 - hyperparameter_tuning - INFO - 实际执行试验次数: 24/50
2025-08-07 09:00:11 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-07 09:00:11 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-07 09:00:11 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\RandomForest\optimization_history_20250807_090011.html
2025-08-07 09:00:11 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-07 09:00:11 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\RandomForest\param_importances_20250807_090011.html
2025-08-07 09:00:11 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 18.16 秒
2025-08-07 09:00:11 - hyperparameter_tuning - INFO - 开始对 CatBoost 进行超参数调优，试验次数: 50
2025-08-07 09:00:11 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 4
2025-08-07 09:00:11 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-07 09:00:11 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-07 09:00:11 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 50, 'n_jobs': 4, 'timeout': 1800, 'callbacks': [<function tune_model.<locals>.early_stopping_callback at 0x000001CA55E57EE0>]}
2025-08-07 09:00:11 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-07 09:00:11 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-07 09:00:11 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-07 09:00:11 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-07 09:00:15 - hyperparameter_tuning - INFO - Trial 0: 发现更好的得分 0.9754
2025-08-07 09:00:15 - hyperparameter_tuning - ERROR - 超参数调优过程中出错: 
All the 5 fits failed.
It is very likely that your model is misconfigured.
You can try to debug the error by setting error_score='raise'.

Below are more details about the failures:
--------------------------------------------------------------------------------
1 <USER> <GROUP> with the following error:
Traceback (most recent call last):
  File "D:\anaconda\envs\multi_model\lib\site-packages\sklearn\model_selection\_validation.py", line 866, in _fit_and_score
    estimator.fit(X_train, y_train, **fit_params)
  File "D:\anaconda\envs\multi_model\lib\site-packages\catboost\core.py", line 5245, in fit
    self._fit(X, y, cat_features, text_features, embedding_features, None, graph, sample_weight, None, None, None, None, baseline, use_best_model,
  File "D:\anaconda\envs\multi_model\lib\site-packages\catboost\core.py", line 2410, in _fit
    self._train(
  File "D:\anaconda\envs\multi_model\lib\site-packages\catboost\core.py", line 1790, in _train
    self._object._train(train_pool, test_pool, params, allow_clear_pool, init_model._object if init_model else None)
  File "_catboost.pyx", line 5017, in _catboost._CatBoost._train
  File "_catboost.pyx", line 5066, in _catboost._CatBoost._train
_catboost.CatBoostError: catboost/cuda/cuda_lib/devices_provider.h:190: Error: device already requested 0

--------------------------------------------------------------------------------
4 <USER> <GROUP> with the following error:
Traceback (most recent call last):
  File "D:\anaconda\envs\multi_model\lib\site-packages\sklearn\model_selection\_validation.py", line 866, in _fit_and_score
    estimator.fit(X_train, y_train, **fit_params)
  File "D:\anaconda\envs\multi_model\lib\site-packages\catboost\core.py", line 5245, in fit
    self._fit(X, y, cat_features, text_features, embedding_features, None, graph, sample_weight, None, None, None, None, baseline, use_best_model,
  File "D:\anaconda\envs\multi_model\lib\site-packages\catboost\core.py", line 2410, in _fit
    self._train(
  File "D:\anaconda\envs\multi_model\lib\site-packages\catboost\core.py", line 1790, in _train
    self._object._train(train_pool, test_pool, params, allow_clear_pool, init_model._object if init_model else None)
  File "_catboost.pyx", line 5017, in _catboost._CatBoost._train
  File "_catboost.pyx", line 5066, in _catboost._CatBoost._train
_catboost.CatBoostError: catboost/cuda/cuda_lib/cuda_manager.cpp:201: Condition violated: `State == nullptr'

2025-08-07 09:00:15 - hyperparameter_tuning - INFO - 开始对 SVM 进行超参数调优，试验次数: 50
2025-08-07 09:00:15 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 4
2025-08-07 09:00:15 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-07 09:00:15 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-07 09:00:15 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 50, 'n_jobs': 4, 'timeout': 1800, 'callbacks': [<function tune_model.<locals>.early_stopping_callback at 0x000001CA55E7F670>]}
2025-08-07 09:00:15 - hyperparameter_tuning - INFO - Trial 1: 发现更好的得分 0.9612
2025-08-07 09:00:15 - hyperparameter_tuning - INFO - Trial 0: 发现更好的得分 0.9879
2025-08-07 09:00:15 - hyperparameter_tuning - INFO - Trial 6: 发现更好的得分 0.9895
2025-08-07 09:00:15 - hyperparameter_tuning - INFO - Trial 7: 发现更好的得分 0.9919
2025-08-07 09:00:15 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-07 09:00:15 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9919
2025-08-07 09:00:15 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-07 09:00:15 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9919
2025-08-07 09:00:15 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-07 09:00:15 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9919
2025-08-07 09:00:15 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-07 09:00:15 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9919
2025-08-07 09:00:15 - hyperparameter_tuning - INFO - 模型 SVM 的最佳参数: {'C': 9.715166714363797, 'kernel': 'rbf'}
2025-08-07 09:00:15 - hyperparameter_tuning - INFO - 模型 SVM 的最佳得分: 0.9919
2025-08-07 09:00:15 - hyperparameter_tuning - INFO - 实际执行试验次数: 22/50
2025-08-07 09:00:15 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-07 09:00:16 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-07 09:00:16 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\SVM\optimization_history_20250807_090015.html
2025-08-07 09:00:16 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-07 09:00:16 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\SVM\param_importances_20250807_090016.html
2025-08-07 09:00:16 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 0.91 秒
