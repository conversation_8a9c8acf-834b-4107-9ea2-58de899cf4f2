2025-08-07 08:32:10 - hyperparameter_tuning - INFO - 开始对 DecisionTree 进行超参数调优，试验次数: 50
2025-08-07 08:32:10 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 4
2025-08-07 08:32:10 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-07 08:32:10 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-07 08:32:10 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 50, 'n_jobs': 4, 'timeout': 1800, 'callbacks': [<function tune_model.<locals>.early_stopping_callback at 0x000001E8E2211310>]}
2025-08-07 08:32:10 - hyperparameter_tuning - INFO - Trial 2: 发现更好的得分 0.9103
2025-08-07 08:32:10 - hyperparameter_tuning - INFO - Trial 0: 发现更好的得分 0.9315
2025-08-07 08:32:10 - hyperparameter_tuning - INFO - Trial 3: 发现更好的得分 0.9361
2025-08-07 08:32:10 - hyperparameter_tuning - INFO - Trial 4: 发现更好的得分 0.9454
2025-08-07 08:32:10 - hyperparameter_tuning - INFO - Trial 13: 发现更好的得分 0.9477
2025-08-07 08:32:10 - hyperparameter_tuning - INFO - Trial 20: 发现更好的得分 0.9511
2025-08-07 08:32:10 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-07 08:32:10 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9511
2025-08-07 08:32:10 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-07 08:32:10 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9511
2025-08-07 08:32:10 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-07 08:32:10 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9511
2025-08-07 08:32:10 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-07 08:32:10 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9511
2025-08-07 08:32:10 - hyperparameter_tuning - INFO - 模型 DecisionTree 的最佳参数: {'max_depth': 7, 'min_samples_split': 44, 'min_samples_leaf': 6, 'criterion': 'entropy', 'class_weight': 'balanced', 'max_features': None}
2025-08-07 08:32:10 - hyperparameter_tuning - INFO - 模型 DecisionTree 的最佳得分: 0.9511
2025-08-07 08:32:10 - hyperparameter_tuning - INFO - 实际执行试验次数: 33/50
2025-08-07 08:32:10 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-07 08:32:11 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-07 08:32:11 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\DecisionTree\optimization_history_20250807_083211.html
2025-08-07 08:32:11 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-07 08:32:11 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\DecisionTree\param_importances_20250807_083211.html
2025-08-07 08:32:11 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 1.48 秒
2025-08-07 08:32:11 - hyperparameter_tuning - INFO - 开始对 RandomForest 进行超参数调优，试验次数: 50
2025-08-07 08:32:11 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 4
2025-08-07 08:32:11 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-07 08:32:11 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-07 08:32:11 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 50, 'n_jobs': 4, 'timeout': 1800, 'callbacks': [<function tune_model.<locals>.early_stopping_callback at 0x000001E8E7808040>]}
2025-08-07 08:32:13 - hyperparameter_tuning - INFO - Trial 2: 发现更好的得分 0.9836
2025-08-07 08:32:14 - hyperparameter_tuning - INFO - Trial 5: 发现更好的得分 0.9868
2025-08-07 08:32:20 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-07 08:32:20 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9868
2025-08-07 08:32:21 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-07 08:32:21 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9868
2025-08-07 08:32:22 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-07 08:32:22 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9868
2025-08-07 08:32:22 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-07 08:32:22 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9868
2025-08-07 08:32:22 - hyperparameter_tuning - INFO - 模型 RandomForest 的最佳参数: {'n_estimators': 109, 'max_depth': 24, 'min_samples_split': 11, 'min_samples_leaf': 2, 'max_features': 'log2'}
2025-08-07 08:32:22 - hyperparameter_tuning - INFO - 模型 RandomForest 的最佳得分: 0.9868
2025-08-07 08:32:22 - hyperparameter_tuning - INFO - 实际执行试验次数: 17/50
2025-08-07 08:32:22 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-07 08:32:22 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-07 08:32:22 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\RandomForest\optimization_history_20250807_083222.html
2025-08-07 08:32:22 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-07 08:32:23 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\RandomForest\param_importances_20250807_083222.html
2025-08-07 08:32:23 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 11.41 秒
2025-08-07 08:32:23 - hyperparameter_tuning - INFO - 开始对 XGBoost 进行超参数调优，试验次数: 50
2025-08-07 08:32:23 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 4
2025-08-07 08:32:23 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-07 08:32:23 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-07 08:32:23 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 50, 'n_jobs': 4, 'timeout': 1800, 'callbacks': [<function tune_model.<locals>.early_stopping_callback at 0x000001E8E2211310>]}
2025-08-07 08:32:23 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-07 08:32:23 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-07 08:32:23 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-07 08:32:23 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-07 08:32:23 - hyperparameter_tuning - INFO - Trial 1: 发现更好的得分 0.9852
2025-08-07 08:32:23 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-07 08:32:23 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-07 08:32:23 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-07 08:32:23 - hyperparameter_tuning - INFO - Trial 3: 发现更好的得分 0.9868
2025-08-07 08:32:23 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-07 08:32:23 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-07 08:32:24 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-07 08:32:24 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-07 08:32:24 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-07 08:32:24 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-07 08:32:24 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-07 08:32:25 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-07 08:32:25 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-07 08:32:25 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-07 08:32:25 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-07 08:32:25 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9876
2025-08-07 08:32:25 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-07 08:32:25 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9876
2025-08-07 08:32:25 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-07 08:32:25 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9876
2025-08-07 08:32:25 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-07 08:32:25 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9876
2025-08-07 08:32:25 - hyperparameter_tuning - INFO - 模型 XGBoost 的最佳参数: {'n_estimators': 243, 'max_depth': 6, 'learning_rate': 0.25465187941439205, 'subsample': 0.507238644359138, 'colsample_bytree': 0.8155206307416676}
2025-08-07 08:32:25 - hyperparameter_tuning - INFO - 模型 XGBoost 的最佳得分: 0.9876
2025-08-07 08:32:25 - hyperparameter_tuning - INFO - 实际执行试验次数: 17/50
2025-08-07 08:32:25 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-07 08:32:26 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-07 08:32:26 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\XGBoost\optimization_history_20250807_083225.html
2025-08-07 08:32:26 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-07 08:32:26 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\XGBoost\param_importances_20250807_083226.html
2025-08-07 08:32:26 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 3.47 秒
2025-08-07 08:32:26 - hyperparameter_tuning - INFO - 开始对 LightGBM 进行超参数调优，试验次数: 50
2025-08-07 08:32:26 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 4
2025-08-07 08:32:26 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-07 08:32:26 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-07 08:32:26 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 50, 'n_jobs': 4, 'timeout': 1800, 'callbacks': [<function tune_model.<locals>.early_stopping_callback at 0x000001E8E7808550>]}
2025-08-07 08:32:26 - hyperparameter_tuning - INFO - Trial 1: 发现更好的得分 0.9802
2025-08-07 08:32:26 - hyperparameter_tuning - INFO - Trial 2: 发现更好的得分 0.9826
2025-08-07 08:32:26 - hyperparameter_tuning - INFO - Trial 4: 发现更好的得分 0.9843
2025-08-07 08:32:27 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-07 08:32:27 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9843
2025-08-07 08:32:27 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-07 08:32:27 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9843
2025-08-07 08:32:27 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-07 08:32:27 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9843
2025-08-07 08:32:27 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-07 08:32:27 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9843
2025-08-07 08:32:27 - hyperparameter_tuning - INFO - 模型 LightGBM 的最佳参数: {'n_estimators': 83, 'max_depth': 6, 'learning_rate': 0.13279123091250636, 'feature_fraction': 0.6778327961008619, 'bagging_fraction': 0.8559692528759305}
2025-08-07 08:32:27 - hyperparameter_tuning - INFO - 模型 LightGBM 的最佳得分: 0.9843
2025-08-07 08:32:27 - hyperparameter_tuning - INFO - 实际执行试验次数: 17/50
2025-08-07 08:32:27 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-07 08:32:28 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-07 08:32:28 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\LightGBM\optimization_history_20250807_083227.html
2025-08-07 08:32:28 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-07 08:32:28 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\LightGBM\param_importances_20250807_083228.html
2025-08-07 08:32:28 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 2.17 秒
2025-08-07 08:32:28 - hyperparameter_tuning - INFO - 开始对 CatBoost 进行超参数调优，试验次数: 50
2025-08-07 08:32:28 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 4
2025-08-07 08:32:28 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-07 08:32:28 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-07 08:32:28 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 50, 'n_jobs': 4, 'timeout': 1800, 'callbacks': [<function tune_model.<locals>.early_stopping_callback at 0x000001E8E7808EE0>]}
2025-08-07 08:32:28 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-07 08:32:28 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-07 08:32:28 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-07 08:32:28 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-07 08:32:32 - hyperparameter_tuning - INFO - Trial 0: 发现更好的得分 0.9859
2025-08-07 08:32:32 - hyperparameter_tuning - ERROR - 超参数调优过程中出错: 
All the 5 fits failed.
It is very likely that your model is misconfigured.
You can try to debug the error by setting error_score='raise'.

Below are more details about the failures:
--------------------------------------------------------------------------------
1 <USER> <GROUP> with the following error:
Traceback (most recent call last):
  File "D:\anaconda\envs\multi_model\lib\site-packages\sklearn\model_selection\_validation.py", line 866, in _fit_and_score
    estimator.fit(X_train, y_train, **fit_params)
  File "D:\anaconda\envs\multi_model\lib\site-packages\catboost\core.py", line 5245, in fit
    self._fit(X, y, cat_features, text_features, embedding_features, None, graph, sample_weight, None, None, None, None, baseline, use_best_model,
  File "D:\anaconda\envs\multi_model\lib\site-packages\catboost\core.py", line 2410, in _fit
    self._train(
  File "D:\anaconda\envs\multi_model\lib\site-packages\catboost\core.py", line 1790, in _train
    self._object._train(train_pool, test_pool, params, allow_clear_pool, init_model._object if init_model else None)
  File "_catboost.pyx", line 5017, in _catboost._CatBoost._train
  File "_catboost.pyx", line 5066, in _catboost._CatBoost._train
_catboost.CatBoostError: catboost/cuda/cuda_lib/devices_provider.h:190: Error: device already requested 0

--------------------------------------------------------------------------------
4 <USER> <GROUP> with the following error:
Traceback (most recent call last):
  File "D:\anaconda\envs\multi_model\lib\site-packages\sklearn\model_selection\_validation.py", line 866, in _fit_and_score
    estimator.fit(X_train, y_train, **fit_params)
  File "D:\anaconda\envs\multi_model\lib\site-packages\catboost\core.py", line 5245, in fit
    self._fit(X, y, cat_features, text_features, embedding_features, None, graph, sample_weight, None, None, None, None, baseline, use_best_model,
  File "D:\anaconda\envs\multi_model\lib\site-packages\catboost\core.py", line 2410, in _fit
    self._train(
  File "D:\anaconda\envs\multi_model\lib\site-packages\catboost\core.py", line 1790, in _train
    self._object._train(train_pool, test_pool, params, allow_clear_pool, init_model._object if init_model else None)
  File "_catboost.pyx", line 5017, in _catboost._CatBoost._train
  File "_catboost.pyx", line 5066, in _catboost._CatBoost._train
_catboost.CatBoostError: catboost/cuda/cuda_lib/cuda_manager.cpp:201: Condition violated: `State == nullptr'

2025-08-07 08:32:32 - hyperparameter_tuning - INFO - 开始对 Logistic 进行超参数调优，试验次数: 50
2025-08-07 08:32:32 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 4
2025-08-07 08:32:32 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-07 08:32:32 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-07 08:32:32 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 50, 'n_jobs': 4, 'timeout': 1800, 'callbacks': [<function tune_model.<locals>.early_stopping_callback at 0x000001E8E7808DC0>]}
2025-08-07 08:32:32 - hyperparameter_tuning - INFO - Trial 0: 发现更好的得分 0.9685
2025-08-07 08:32:32 - hyperparameter_tuning - INFO - Trial 3: 发现更好的得分 0.9727
2025-08-07 08:32:32 - hyperparameter_tuning - INFO - Trial 1: 发现更好的得分 0.9761
2025-08-07 08:32:32 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-07 08:32:32 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9761
2025-08-07 08:32:32 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-07 08:32:32 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9761
2025-08-07 08:32:32 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-07 08:32:32 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-07 08:32:32 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9761
2025-08-07 08:32:32 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9761
2025-08-07 08:32:32 - hyperparameter_tuning - INFO - 模型 Logistic 的最佳参数: {'C': 7.3173274166688085, 'solver': 'liblinear'}
2025-08-07 08:32:32 - hyperparameter_tuning - INFO - 模型 Logistic 的最佳得分: 0.9761
2025-08-07 08:32:32 - hyperparameter_tuning - INFO - 实际执行试验次数: 16/50
2025-08-07 08:32:32 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-07 08:32:32 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-07 08:32:32 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\Logistic\optimization_history_20250807_083232.html
2025-08-07 08:32:33 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-07 08:32:33 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\Logistic\param_importances_20250807_083232.html
2025-08-07 08:32:33 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 0.81 秒
2025-08-07 08:32:33 - hyperparameter_tuning - INFO - 开始对 SVM 进行超参数调优，试验次数: 50
2025-08-07 08:32:33 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 4
2025-08-07 08:32:33 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-07 08:32:33 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-07 08:32:33 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 50, 'n_jobs': 4, 'timeout': 1800, 'callbacks': [<function tune_model.<locals>.early_stopping_callback at 0x000001E8E7C383A0>]}
2025-08-07 08:32:33 - hyperparameter_tuning - INFO - Trial 1: 发现更好的得分 0.9650
2025-08-07 08:32:33 - hyperparameter_tuning - INFO - Trial 0: 发现更好的得分 0.9693
2025-08-07 08:32:33 - hyperparameter_tuning - INFO - Trial 4: 发现更好的得分 0.9951
2025-08-07 08:32:33 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-07 08:32:33 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-07 08:32:33 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9951
2025-08-07 08:32:33 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9951
2025-08-07 08:32:33 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-07 08:32:33 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9951
2025-08-07 08:32:33 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-07 08:32:33 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9951
2025-08-07 08:32:33 - hyperparameter_tuning - INFO - 模型 SVM 的最佳参数: {'C': 8.629665047938591, 'kernel': 'rbf'}
2025-08-07 08:32:33 - hyperparameter_tuning - INFO - 模型 SVM 的最佳得分: 0.9951
2025-08-07 08:32:33 - hyperparameter_tuning - INFO - 实际执行试验次数: 18/50
2025-08-07 08:32:33 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-07 08:32:33 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-07 08:32:33 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\SVM\optimization_history_20250807_083233.html
2025-08-07 08:32:33 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-07 08:32:33 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\SVM\param_importances_20250807_083233.html
2025-08-07 08:32:33 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 0.86 秒
2025-08-07 08:32:33 - hyperparameter_tuning - INFO - 开始对 KNN 进行超参数调优，试验次数: 50
2025-08-07 08:32:33 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 4
2025-08-07 08:32:33 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-07 08:32:33 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-07 08:32:33 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 50, 'n_jobs': 4, 'timeout': 1800, 'callbacks': [<function tune_model.<locals>.early_stopping_callback at 0x000001E8E7C385E0>]}
2025-08-07 08:32:34 - hyperparameter_tuning - INFO - Trial 0: 发现更好的得分 0.9790
2025-08-07 08:32:34 - hyperparameter_tuning - INFO - Trial 3: 发现更好的得分 0.9816
2025-08-07 08:32:34 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-07 08:32:34 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9817
2025-08-07 08:32:34 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-07 08:32:34 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-07 08:32:34 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9817
2025-08-07 08:32:34 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9817
2025-08-07 08:32:34 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-07 08:32:34 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9817
2025-08-07 08:32:34 - hyperparameter_tuning - INFO - 模型 KNN 的最佳参数: {'n_neighbors': 5}
2025-08-07 08:32:34 - hyperparameter_tuning - INFO - 模型 KNN 的最佳得分: 0.9817
2025-08-07 08:32:34 - hyperparameter_tuning - INFO - 实际执行试验次数: 15/50
2025-08-07 08:32:34 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-07 08:32:34 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-07 08:32:34 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\KNN\optimization_history_20250807_083234.html
2025-08-07 08:32:34 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-07 08:32:34 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\KNN\param_importances_20250807_083234.html
2025-08-07 08:32:34 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 0.74 秒
2025-08-07 08:32:34 - hyperparameter_tuning - INFO - 开始对 NaiveBayes 进行超参数调优，试验次数: 50
2025-08-07 08:32:34 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 4
2025-08-07 08:32:34 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-07 08:32:34 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-07 08:32:34 - hyperparameter_tuning - INFO - NaiveBayes模型无需调参
2025-08-07 08:32:34 - hyperparameter_tuning - INFO - 开始对 NeuralNet 进行超参数调优，试验次数: 50
2025-08-07 08:32:34 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 4
2025-08-07 08:32:34 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-07 08:32:34 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-07 08:32:34 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 50, 'n_jobs': 4, 'timeout': 1800, 'callbacks': [<function tune_model.<locals>.early_stopping_callback at 0x000001E8E75C4EE0>]}
2025-08-07 08:32:37 - hyperparameter_tuning - INFO - Trial 3: 发现更好的得分 0.9814
2025-08-07 08:32:45 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-07 08:32:45 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9814
2025-08-07 08:32:46 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-07 08:32:46 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9814
2025-08-07 08:32:47 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-07 08:32:47 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9814
2025-08-07 08:32:47 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-07 08:32:47 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9814
2025-08-07 08:32:47 - hyperparameter_tuning - INFO - 模型 NeuralNet 的最佳参数: {'hidden_layer_sizes': (100,), 'alpha': 0.004367182624548074}
2025-08-07 08:32:47 - hyperparameter_tuning - INFO - 模型 NeuralNet 的最佳得分: 0.9814
2025-08-07 08:32:47 - hyperparameter_tuning - INFO - 实际执行试验次数: 14/50
2025-08-07 08:32:47 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-07 08:32:47 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-07 08:32:47 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\NeuralNet\optimization_history_20250807_083247.html
2025-08-07 08:32:47 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-07 08:32:47 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\NeuralNet\param_importances_20250807_083247.html
2025-08-07 08:32:47 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 12.94 秒
