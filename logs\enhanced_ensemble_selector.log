2025-07-16 22:41:20 - enhanced_ensemble_selector - INFO - 开始评估 5 个基模型...
2025-07-16 22:41:20 - enhanced_ensemble_selector - INFO -   评估模型: DecisionTree
2025-07-16 22:41:20 - enhanced_ensemble_selector - INFO -     DecisionTree - 性能得分: 0.7804
2025-07-16 22:41:20 - enhanced_ensemble_selector - INFO -   评估模型: RandomForest
2025-07-16 22:41:20 - enhanced_ensemble_selector - INFO -     RandomForest - 性能得分: 0.9031
2025-07-16 22:41:20 - enhanced_ensemble_selector - INFO -   评估模型: Logistic
2025-07-16 22:41:20 - enhanced_ensemble_selector - INFO -     Logistic - 性能得分: 0.8309
2025-07-16 22:41:20 - enhanced_ensemble_selector - INFO -   评估模型: SVM
2025-07-16 22:41:21 - enhanced_ensemble_selector - INFO -     SVM - 性能得分: 0.9419
2025-07-16 22:41:21 - enhanced_ensemble_selector - INFO -   评估模型: XGBoost
2025-07-16 22:41:21 - enhanced_ensemble_selector - INFO -     XGBoost - 性能得分: 0.9249
2025-07-16 22:41:21 - enhanced_ensemble_selector - INFO - 模型多样性矩阵计算完成
2025-07-16 22:41:21 - enhanced_ensemble_selector - INFO -   DecisionTree vs RandomForest: 相关性=0.588, 多样性=0.412
2025-07-16 22:41:21 - enhanced_ensemble_selector - INFO -   DecisionTree vs Logistic: 相关性=0.541, 多样性=0.459
2025-07-16 22:41:21 - enhanced_ensemble_selector - INFO -   DecisionTree vs SVM: 相关性=0.569, 多样性=0.431
2025-07-16 22:41:21 - enhanced_ensemble_selector - INFO -   DecisionTree vs XGBoost: 相关性=0.620, 多样性=0.380
2025-07-16 22:41:21 - enhanced_ensemble_selector - INFO -   RandomForest vs Logistic: 相关性=0.713, 多样性=0.287
2025-07-16 22:41:21 - enhanced_ensemble_selector - INFO -   RandomForest vs SVM: 相关性=0.860, 多样性=0.140
2025-07-16 22:41:21 - enhanced_ensemble_selector - INFO -   RandomForest vs XGBoost: 相关性=0.848, 多样性=0.152
2025-07-16 22:41:21 - enhanced_ensemble_selector - INFO -   Logistic vs SVM: 相关性=0.720, 多样性=0.280
2025-07-16 22:41:21 - enhanced_ensemble_selector - INFO -   Logistic vs XGBoost: 相关性=0.707, 多样性=0.293
2025-07-16 22:41:21 - enhanced_ensemble_selector - INFO -   SVM vs XGBoost: 相关性=0.855, 多样性=0.145
2025-07-16 22:41:21 - enhanced_ensemble_selector - INFO - 从 5 个合格模型中选择 3 个进行集成
2025-07-16 22:41:21 - enhanced_ensemble_selector - INFO - 最优组合: ['RandomForest', 'SVM', 'XGBoost']
2025-07-16 22:41:21 - enhanced_ensemble_selector - INFO - 综合得分: 0.9233
2025-07-16 22:41:21 - enhanced_ensemble_selector - INFO - 从 5 个合格模型中选择 3 个进行集成
2025-07-16 22:41:21 - enhanced_ensemble_selector - INFO - 最优组合: ['DecisionTree', 'Logistic', 'SVM']
2025-07-16 22:41:21 - enhanced_ensemble_selector - INFO - 综合得分: 0.4784
2025-07-16 22:41:21 - enhanced_ensemble_selector - INFO - 从 5 个合格模型中选择 3 个进行集成
2025-07-16 22:41:21 - enhanced_ensemble_selector - INFO - 最优组合: ['DecisionTree', 'Logistic', 'SVM']
2025-07-16 22:41:21 - enhanced_ensemble_selector - INFO - 综合得分: 0.6559
2025-07-16 22:41:21 - enhanced_ensemble_selector - INFO - 从 5 个合格模型中选择 3 个进行集成
2025-07-16 22:41:21 - enhanced_ensemble_selector - INFO - 最优组合: ['DecisionTree', 'Logistic', 'SVM']
2025-07-16 22:41:21 - enhanced_ensemble_selector - INFO - 综合得分: 0.6559
2025-07-16 22:41:21 - enhanced_ensemble_selector - INFO - 创建了 3 个数据变体用于增强模型多样性
2025-07-16 22:46:04 - enhanced_ensemble_selector - INFO - 开始评估 10 个基模型...
2025-07-16 22:46:04 - enhanced_ensemble_selector - INFO -   评估模型: DecisionTree
2025-07-16 22:46:04 - enhanced_ensemble_selector - INFO -     DecisionTree - 性能得分: 0.8461
2025-07-16 22:46:04 - enhanced_ensemble_selector - INFO -   评估模型: RandomForest
2025-07-16 22:46:04 - enhanced_ensemble_selector - INFO -     RandomForest - 性能得分: 0.8439
2025-07-16 22:46:04 - enhanced_ensemble_selector - INFO -   评估模型: XGBoost
2025-07-16 22:46:04 - enhanced_ensemble_selector - INFO -     XGBoost - 性能得分: 0.7959
2025-07-16 22:46:04 - enhanced_ensemble_selector - INFO -   评估模型: LightGBM
2025-07-16 22:46:06 - enhanced_ensemble_selector - INFO -     LightGBM - 性能得分: 0.7759
2025-07-16 22:46:06 - enhanced_ensemble_selector - INFO -   评估模型: CatBoost
2025-07-16 22:46:07 - enhanced_ensemble_selector - INFO -     CatBoost - 性能得分: 0.8828
2025-07-16 22:46:07 - enhanced_ensemble_selector - INFO -   评估模型: Logistic
2025-07-16 22:46:07 - enhanced_ensemble_selector - INFO -     Logistic - 性能得分: 0.8449
2025-07-16 22:46:07 - enhanced_ensemble_selector - INFO -   评估模型: SVM
2025-07-16 22:46:07 - enhanced_ensemble_selector - INFO -     SVM - 性能得分: 0.8892
2025-07-16 22:46:07 - enhanced_ensemble_selector - INFO -   评估模型: KNN
2025-07-16 22:46:07 - enhanced_ensemble_selector - INFO -     KNN - 性能得分: 0.8978
2025-07-16 22:46:07 - enhanced_ensemble_selector - INFO -   评估模型: NaiveBayes
2025-07-16 22:46:07 - enhanced_ensemble_selector - INFO -     NaiveBayes - 性能得分: 0.8831
2025-07-16 22:46:07 - enhanced_ensemble_selector - INFO -   评估模型: NeuralNet
2025-07-16 22:46:07 - enhanced_ensemble_selector - INFO -     NeuralNet - 性能得分: 0.8664
2025-07-16 22:46:07 - enhanced_ensemble_selector - INFO - 模型多样性矩阵计算完成
2025-07-16 22:46:07 - enhanced_ensemble_selector - INFO -   DecisionTree vs RandomForest: 相关性=0.510, 多样性=0.490
2025-07-16 22:46:07 - enhanced_ensemble_selector - INFO -   DecisionTree vs XGBoost: 相关性=0.402, 多样性=0.598
2025-07-16 22:46:07 - enhanced_ensemble_selector - INFO -   DecisionTree vs LightGBM: 相关性=0.361, 多样性=0.639
2025-07-16 22:46:07 - enhanced_ensemble_selector - INFO -   DecisionTree vs CatBoost: 相关性=0.524, 多样性=0.476
2025-07-16 22:46:07 - enhanced_ensemble_selector - INFO -   DecisionTree vs Logistic: 相关性=0.402, 多样性=0.598
2025-07-16 22:46:07 - enhanced_ensemble_selector - INFO -   DecisionTree vs SVM: 相关性=0.524, 多样性=0.476
2025-07-16 22:46:07 - enhanced_ensemble_selector - INFO -   DecisionTree vs KNN: 相关性=0.556, 多样性=0.444
2025-07-16 22:46:07 - enhanced_ensemble_selector - INFO -   DecisionTree vs NaiveBayes: 相关性=0.510, 多样性=0.490
2025-07-16 22:46:07 - enhanced_ensemble_selector - INFO -   DecisionTree vs NeuralNet: 相关性=0.455, 多样性=0.545
2025-07-16 22:46:07 - enhanced_ensemble_selector - INFO -   RandomForest vs XGBoost: 相关性=0.698, 多样性=0.302
2025-07-16 22:46:07 - enhanced_ensemble_selector - INFO -   RandomForest vs LightGBM: 相关性=0.738, 多样性=0.262
2025-07-16 22:46:07 - enhanced_ensemble_selector - INFO -   RandomForest vs CatBoost: 相关性=0.899, 多样性=0.101
2025-07-16 22:46:07 - enhanced_ensemble_selector - INFO -   RandomForest vs Logistic: 相关性=0.800, 多样性=0.200
2025-07-16 22:46:07 - enhanced_ensemble_selector - INFO -   RandomForest vs SVM: 相关性=0.792, 多样性=0.208
2025-07-16 22:46:07 - enhanced_ensemble_selector - INFO -   RandomForest vs KNN: 相关性=0.846, 多样性=0.154
2025-07-16 22:46:07 - enhanced_ensemble_selector - INFO -   RandomForest vs NaiveBayes: 相关性=0.792, 多样性=0.208
2025-07-16 22:46:07 - enhanced_ensemble_selector - INFO -   RandomForest vs NeuralNet: 相关性=0.743, 多样性=0.257
2025-07-16 22:46:07 - enhanced_ensemble_selector - INFO -   XGBoost vs LightGBM: 相关性=0.856, 多样性=0.144
2025-07-16 22:46:07 - enhanced_ensemble_selector - INFO -   XGBoost vs CatBoost: 相关性=0.706, 多样性=0.294
2025-07-16 22:46:07 - enhanced_ensemble_selector - INFO -   XGBoost vs Logistic: 相关性=0.697, 多样性=0.303
2025-07-16 22:46:07 - enhanced_ensemble_selector - INFO -   XGBoost vs SVM: 相关性=0.601, 多样性=0.399
2025-07-16 22:46:07 - enhanced_ensemble_selector - INFO -   XGBoost vs KNN: 相关性=0.646, 多样性=0.354
2025-07-16 22:46:07 - enhanced_ensemble_selector - INFO -   XGBoost vs NaiveBayes: 相关性=0.595, 多样性=0.405
2025-07-16 22:46:07 - enhanced_ensemble_selector - INFO -   XGBoost vs NeuralNet: 相关性=0.646, 多样性=0.354
2025-07-16 22:46:07 - enhanced_ensemble_selector - INFO -   LightGBM vs CatBoost: 相关性=0.731, 多样性=0.269
2025-07-16 22:46:07 - enhanced_ensemble_selector - INFO -   LightGBM vs Logistic: 相关性=0.753, 多样性=0.247
2025-07-16 22:46:07 - enhanced_ensemble_selector - INFO -   LightGBM vs SVM: 相关性=0.623, 多样性=0.377
2025-07-16 22:46:07 - enhanced_ensemble_selector - INFO -   LightGBM vs KNN: 相关性=0.692, 多样性=0.308
2025-07-16 22:46:07 - enhanced_ensemble_selector - INFO -   LightGBM vs NaiveBayes: 相关性=0.632, 多样性=0.368
2025-07-16 22:46:07 - enhanced_ensemble_selector - INFO -   LightGBM vs NeuralNet: 相关性=0.588, 多样性=0.412
2025-07-16 22:46:07 - enhanced_ensemble_selector - INFO -   CatBoost vs Logistic: 相关性=0.811, 多样性=0.189
2025-07-16 22:46:07 - enhanced_ensemble_selector - INFO -   CatBoost vs SVM: 相关性=0.890, 多样性=0.110
2025-07-16 22:46:07 - enhanced_ensemble_selector - INFO -   CatBoost vs KNN: 相关性=0.854, 多样性=0.146
2025-07-16 22:46:07 - enhanced_ensemble_selector - INFO -   CatBoost vs NaiveBayes: 相关性=0.792, 多样性=0.208
2025-07-16 22:46:07 - enhanced_ensemble_selector - INFO -   CatBoost vs NeuralNet: 相关性=0.854, 多样性=0.146
2025-07-16 22:46:07 - enhanced_ensemble_selector - INFO -   Logistic vs SVM: 相关性=0.811, 多样性=0.189
2025-07-16 22:46:07 - enhanced_ensemble_selector - INFO -   Logistic vs KNN: 相关性=0.849, 多样性=0.151
2025-07-16 22:46:07 - enhanced_ensemble_selector - INFO -   Logistic vs NaiveBayes: 相关性=0.800, 多样性=0.200
2025-07-16 22:46:07 - enhanced_ensemble_selector - INFO -   Logistic vs NeuralNet: 相关性=0.849, 多样性=0.151
2025-07-16 22:46:07 - enhanced_ensemble_selector - INFO -   SVM vs KNN: 相关性=0.854, 多样性=0.146
2025-07-16 22:46:07 - enhanced_ensemble_selector - INFO -   SVM vs NaiveBayes: 相关性=0.792, 多样性=0.208
2025-07-16 22:46:07 - enhanced_ensemble_selector - INFO -   SVM vs NeuralNet: 相关性=0.854, 多样性=0.146
2025-07-16 22:46:07 - enhanced_ensemble_selector - INFO -   KNN vs NaiveBayes: 相关性=0.950, 多样性=0.050
2025-07-16 22:46:07 - enhanced_ensemble_selector - INFO -   KNN vs NeuralNet: 相关性=0.795, 多样性=0.205
2025-07-16 22:46:07 - enhanced_ensemble_selector - INFO -   NaiveBayes vs NeuralNet: 相关性=0.743, 多样性=0.257
2025-07-16 22:46:07 - enhanced_ensemble_selector - INFO - 从 10 个合格模型中选择 3 个进行集成
2025-07-16 22:46:07 - enhanced_ensemble_selector - INFO - 最优组合: ['DecisionTree', 'NaiveBayes', 'NeuralNet']
2025-07-16 22:46:07 - enhanced_ensemble_selector - INFO - 综合得分: 0.7617
2025-07-16 22:53:21 - enhanced_ensemble_selector - INFO - 开始评估 5 个基模型...
2025-07-16 22:53:21 - enhanced_ensemble_selector - INFO -   评估模型: DecisionTree
2025-07-16 22:53:21 - enhanced_ensemble_selector - INFO -     DecisionTree - 性能得分: 0.7708
2025-07-16 22:53:21 - enhanced_ensemble_selector - INFO -   评估模型: RandomForest
2025-07-16 22:53:21 - enhanced_ensemble_selector - INFO -     RandomForest - 性能得分: 0.9288
2025-07-16 22:53:21 - enhanced_ensemble_selector - INFO -   评估模型: Logistic
2025-07-16 22:53:21 - enhanced_ensemble_selector - INFO -     Logistic - 性能得分: 0.8346
2025-07-16 22:53:21 - enhanced_ensemble_selector - INFO -   评估模型: SVM
2025-07-16 22:53:21 - enhanced_ensemble_selector - INFO -     SVM - 性能得分: 0.9518
2025-07-16 22:53:21 - enhanced_ensemble_selector - INFO -   评估模型: XGBoost
2025-07-16 22:53:21 - enhanced_ensemble_selector - INFO -     XGBoost - 性能得分: 0.9244
2025-07-16 22:53:21 - enhanced_ensemble_selector - INFO - 模型多样性矩阵计算完成
2025-07-16 22:53:21 - enhanced_ensemble_selector - INFO -   DecisionTree vs RandomForest: 相关性=0.512, 多样性=0.488
2025-07-16 22:53:21 - enhanced_ensemble_selector - INFO -   DecisionTree vs Logistic: 相关性=0.437, 多样性=0.563
2025-07-16 22:53:21 - enhanced_ensemble_selector - INFO -   DecisionTree vs SVM: 相关性=0.498, 多样性=0.502
2025-07-16 22:53:21 - enhanced_ensemble_selector - INFO -   DecisionTree vs XGBoost: 相关性=0.551, 多样性=0.449
2025-07-16 22:53:21 - enhanced_ensemble_selector - INFO -   RandomForest vs Logistic: 相关性=0.701, 多样性=0.299
2025-07-16 22:53:21 - enhanced_ensemble_selector - INFO -   RandomForest vs SVM: 相关性=0.892, 多样性=0.108
2025-07-16 22:53:21 - enhanced_ensemble_selector - INFO -   RandomForest vs XGBoost: 相关性=0.874, 多样性=0.126
2025-07-16 22:53:21 - enhanced_ensemble_selector - INFO -   Logistic vs SVM: 相关性=0.691, 多样性=0.309
2025-07-16 22:53:21 - enhanced_ensemble_selector - INFO -   Logistic vs XGBoost: 相关性=0.625, 多样性=0.375
2025-07-16 22:53:21 - enhanced_ensemble_selector - INFO -   SVM vs XGBoost: 相关性=0.900, 多样性=0.100
2025-07-16 22:53:21 - enhanced_ensemble_selector - INFO - 从 5 个合格模型中选择 3 个进行集成
2025-07-16 22:53:21 - enhanced_ensemble_selector - INFO - 最优组合: ['DecisionTree', 'Logistic', 'SVM']
2025-07-16 22:53:21 - enhanced_ensemble_selector - INFO - 综合得分: 0.6770
2025-07-16 22:53:21 - enhanced_ensemble_selector - INFO - 开始评估 5 个基模型...
2025-07-16 22:53:21 - enhanced_ensemble_selector - INFO -   评估模型: DecisionTree
2025-07-16 22:53:21 - enhanced_ensemble_selector - INFO -     DecisionTree - 性能得分: 0.7708
2025-07-16 22:53:21 - enhanced_ensemble_selector - INFO -   评估模型: RandomForest
2025-07-16 22:53:21 - enhanced_ensemble_selector - INFO -     RandomForest - 性能得分: 0.9288
2025-07-16 22:53:21 - enhanced_ensemble_selector - INFO -   评估模型: Logistic
2025-07-16 22:53:21 - enhanced_ensemble_selector - INFO -     Logistic - 性能得分: 0.8346
2025-07-16 22:53:21 - enhanced_ensemble_selector - INFO -   评估模型: SVM
2025-07-16 22:53:21 - enhanced_ensemble_selector - INFO -     SVM - 性能得分: 0.9518
2025-07-16 22:53:21 - enhanced_ensemble_selector - INFO -   评估模型: XGBoost
2025-07-16 22:53:21 - enhanced_ensemble_selector - INFO -     XGBoost - 性能得分: 0.9244
2025-07-16 22:53:22 - enhanced_ensemble_selector - INFO - 使用改进的多样性分析完成
2025-07-16 22:53:22 - enhanced_ensemble_selector - INFO - 数据复杂度: 0.103
2025-07-16 22:53:22 - enhanced_ensemble_selector - INFO - 调整后相关性阈值: 0.600
2025-07-16 22:53:22 - enhanced_ensemble_selector - INFO - 综合多样性得分: 0.279
2025-07-16 22:53:22 - enhanced_ensemble_selector - INFO -   DecisionTree vs RandomForest: 相关性=0.737, 多样性=0.263
2025-07-16 22:53:22 - enhanced_ensemble_selector - INFO -   DecisionTree vs Logistic: 相关性=0.602, 多样性=0.398
2025-07-16 22:53:22 - enhanced_ensemble_selector - INFO -   DecisionTree vs SVM: 相关性=0.616, 多样性=0.384
2025-07-16 22:53:22 - enhanced_ensemble_selector - INFO -   DecisionTree vs XGBoost: 相关性=0.679, 多样性=0.321
2025-07-16 22:53:22 - enhanced_ensemble_selector - INFO -   RandomForest vs Logistic: 相关性=0.850, 多样性=0.150
2025-07-16 22:53:22 - enhanced_ensemble_selector - INFO -   RandomForest vs SVM: 相关性=0.921, 多样性=0.079
2025-07-16 22:53:22 - enhanced_ensemble_selector - INFO -   RandomForest vs XGBoost: 相关性=0.920, 多样性=0.080
2025-07-16 22:53:22 - enhanced_ensemble_selector - INFO -   Logistic vs SVM: 相关性=0.820, 多样性=0.180
2025-07-16 22:53:22 - enhanced_ensemble_selector - INFO -   Logistic vs XGBoost: 相关性=0.803, 多样性=0.197
2025-07-16 22:53:22 - enhanced_ensemble_selector - INFO -   SVM vs XGBoost: 相关性=0.938, 多样性=0.062
2025-07-16 22:53:22 - enhanced_ensemble_selector - INFO - 特征重要性多样性: 0.378
2025-07-16 22:53:22 - enhanced_ensemble_selector - INFO - 算法类型多样性: 0.300
2025-07-16 22:53:22 - enhanced_ensemble_selector - INFO - 算法类型分布: {'tree': 3, 'linear': 2}
2025-07-16 22:53:22 - enhanced_ensemble_selector - INFO - 从 5 个合格模型中选择 3 个进行集成
2025-07-16 22:53:22 - enhanced_ensemble_selector - INFO - 最优组合: ['DecisionTree', 'Logistic', 'SVM']
2025-07-16 22:53:22 - enhanced_ensemble_selector - INFO - 综合得分: 0.6357
2025-07-16 22:53:22 - enhanced_ensemble_selector - INFO - 开始评估 4 个基模型...
2025-07-16 22:53:22 - enhanced_ensemble_selector - INFO -   评估模型: DecisionTree
2025-07-16 22:53:22 - enhanced_ensemble_selector - INFO -     DecisionTree - 性能得分: 0.8858
2025-07-16 22:53:22 - enhanced_ensemble_selector - INFO -   评估模型: RandomForest
2025-07-16 22:53:22 - enhanced_ensemble_selector - INFO -     RandomForest - 性能得分: 0.9414
2025-07-16 22:53:22 - enhanced_ensemble_selector - INFO -   评估模型: Logistic
2025-07-16 22:53:22 - enhanced_ensemble_selector - INFO -     Logistic - 性能得分: 0.8745
2025-07-16 22:53:22 - enhanced_ensemble_selector - INFO -   评估模型: SVM
2025-07-16 22:53:22 - enhanced_ensemble_selector - INFO -     SVM - 性能得分: 0.9687
2025-07-16 22:53:22 - enhanced_ensemble_selector - INFO - 使用改进的多样性分析完成
2025-07-16 22:53:22 - enhanced_ensemble_selector - INFO - 数据复杂度: 0.116
2025-07-16 22:53:22 - enhanced_ensemble_selector - INFO - 调整后相关性阈值: 0.600
2025-07-16 22:53:22 - enhanced_ensemble_selector - INFO - 综合多样性得分: 0.215
2025-07-16 22:53:22 - enhanced_ensemble_selector - INFO -   DecisionTree vs RandomForest: 相关性=0.895, 多样性=0.105
2025-07-16 22:53:22 - enhanced_ensemble_selector - INFO -   DecisionTree vs Logistic: 相关性=0.816, 多样性=0.184
2025-07-16 22:53:22 - enhanced_ensemble_selector - INFO -   DecisionTree vs SVM: 相关性=0.820, 多样性=0.180
2025-07-16 22:53:22 - enhanced_ensemble_selector - INFO -   RandomForest vs Logistic: 相关性=0.875, 多样性=0.125
2025-07-16 22:53:22 - enhanced_ensemble_selector - INFO -   RandomForest vs SVM: 相关性=0.951, 多样性=0.049
2025-07-16 22:53:22 - enhanced_ensemble_selector - INFO -   Logistic vs SVM: 相关性=0.854, 多样性=0.146
2025-07-16 22:53:22 - enhanced_ensemble_selector - INFO - 特征重要性多样性: 0.164
2025-07-16 22:53:22 - enhanced_ensemble_selector - INFO - 算法类型多样性: 0.500
2025-07-16 22:53:22 - enhanced_ensemble_selector - INFO - 算法类型分布: {'tree': 2, 'linear': 2}
2025-07-16 22:53:22 - enhanced_ensemble_selector - INFO - 从 4 个合格模型中选择 3 个进行集成
2025-07-16 22:53:22 - enhanced_ensemble_selector - INFO - 最优组合: ['DecisionTree', 'Logistic', 'SVM']
2025-07-16 22:53:22 - enhanced_ensemble_selector - INFO - 综合得分: 0.6192
2025-07-16 22:53:22 - enhanced_ensemble_selector - INFO - 开始评估 4 个基模型...
2025-07-16 22:53:22 - enhanced_ensemble_selector - INFO -   评估模型: DecisionTree
2025-07-16 22:53:22 - enhanced_ensemble_selector - INFO -     DecisionTree - 性能得分: 0.6624
2025-07-16 22:53:22 - enhanced_ensemble_selector - INFO -   评估模型: RandomForest
2025-07-16 22:53:22 - enhanced_ensemble_selector - INFO -     RandomForest - 性能得分: 0.7973
2025-07-16 22:53:22 - enhanced_ensemble_selector - INFO -   评估模型: Logistic
2025-07-16 22:53:22 - enhanced_ensemble_selector - INFO -     Logistic - 性能得分: 0.6844
2025-07-16 22:53:22 - enhanced_ensemble_selector - INFO -   评估模型: SVM
2025-07-16 22:53:22 - enhanced_ensemble_selector - INFO -     SVM - 性能得分: 0.8690
2025-07-16 22:53:23 - enhanced_ensemble_selector - INFO - 使用改进的多样性分析完成
2025-07-16 22:53:23 - enhanced_ensemble_selector - INFO - 数据复杂度: 0.140
2025-07-16 22:53:23 - enhanced_ensemble_selector - INFO - 调整后相关性阈值: 0.600
2025-07-16 22:53:23 - enhanced_ensemble_selector - INFO - 综合多样性得分: 0.529
2025-07-16 22:53:23 - enhanced_ensemble_selector - INFO -   DecisionTree vs RandomForest: 相关性=0.605, 多样性=0.395
2025-07-16 22:53:23 - enhanced_ensemble_selector - INFO -   DecisionTree vs Logistic: 相关性=0.407, 多样性=0.593
2025-07-16 22:53:23 - enhanced_ensemble_selector - INFO -   DecisionTree vs SVM: 相关性=0.429, 多样性=0.571
2025-07-16 22:53:23 - enhanced_ensemble_selector - INFO -   RandomForest vs Logistic: 相关性=0.614, 多样性=0.386
2025-07-16 22:53:23 - enhanced_ensemble_selector - INFO -   RandomForest vs SVM: 相关性=0.806, 多样性=0.194
2025-07-16 22:53:23 - enhanced_ensemble_selector - INFO -   Logistic vs SVM: 相关性=0.528, 多样性=0.472
2025-07-16 22:53:23 - enhanced_ensemble_selector - INFO - 特征重要性多样性: 0.705
2025-07-16 22:53:23 - enhanced_ensemble_selector - INFO - 算法类型多样性: 0.500
2025-07-16 22:53:23 - enhanced_ensemble_selector - INFO - 算法类型分布: {'tree': 2, 'linear': 2}
2025-07-16 22:53:23 - enhanced_ensemble_selector - INFO - 从 4 个合格模型中选择 3 个进行集成
2025-07-16 22:53:23 - enhanced_ensemble_selector - INFO - 最优组合: ['DecisionTree', 'Logistic', 'SVM']
2025-07-16 22:53:23 - enhanced_ensemble_selector - INFO - 综合得分: 0.6463
2025-07-16 22:53:23 - enhanced_ensemble_selector - INFO - 开始评估 4 个基模型...
2025-07-16 22:53:23 - enhanced_ensemble_selector - INFO -   评估模型: DecisionTree
2025-07-16 22:53:23 - enhanced_ensemble_selector - INFO -     DecisionTree - 性能得分: 0.7708
2025-07-16 22:53:23 - enhanced_ensemble_selector - INFO -   评估模型: RandomForest
2025-07-16 22:53:23 - enhanced_ensemble_selector - INFO -     RandomForest - 性能得分: 0.9288
2025-07-16 22:53:23 - enhanced_ensemble_selector - INFO -   评估模型: Logistic
2025-07-16 22:53:23 - enhanced_ensemble_selector - INFO -     Logistic - 性能得分: 0.8346
2025-07-16 22:53:23 - enhanced_ensemble_selector - INFO -   评估模型: SVM
2025-07-16 22:53:23 - enhanced_ensemble_selector - INFO -     SVM - 性能得分: 0.9518
2025-07-16 22:53:23 - enhanced_ensemble_selector - INFO - 使用改进的多样性分析完成
2025-07-16 22:53:23 - enhanced_ensemble_selector - INFO - 数据复杂度: 0.103
2025-07-16 22:53:23 - enhanced_ensemble_selector - INFO - 调整后相关性阈值: 0.600
2025-07-16 22:53:23 - enhanced_ensemble_selector - INFO - 综合多样性得分: 0.359
2025-07-16 22:53:23 - enhanced_ensemble_selector - INFO -   DecisionTree vs RandomForest: 相关性=0.737, 多样性=0.263
2025-07-16 22:53:23 - enhanced_ensemble_selector - INFO -   DecisionTree vs Logistic: 相关性=0.602, 多样性=0.398
2025-07-16 22:53:23 - enhanced_ensemble_selector - INFO -   DecisionTree vs SVM: 相关性=0.616, 多样性=0.384
2025-07-16 22:53:23 - enhanced_ensemble_selector - INFO -   RandomForest vs Logistic: 相关性=0.850, 多样性=0.150
2025-07-16 22:53:23 - enhanced_ensemble_selector - INFO -   RandomForest vs SVM: 相关性=0.921, 多样性=0.079
2025-07-16 22:53:23 - enhanced_ensemble_selector - INFO -   Logistic vs SVM: 相关性=0.820, 多样性=0.180
2025-07-16 22:53:23 - enhanced_ensemble_selector - INFO - 特征重要性多样性: 0.458
2025-07-16 22:53:23 - enhanced_ensemble_selector - INFO - 算法类型多样性: 0.500
2025-07-16 22:53:23 - enhanced_ensemble_selector - INFO - 算法类型分布: {'tree': 2, 'linear': 2}
2025-07-16 22:53:23 - enhanced_ensemble_selector - INFO - 从 4 个合格模型中选择 3 个进行集成
2025-07-16 22:53:23 - enhanced_ensemble_selector - INFO - 最优组合: ['DecisionTree', 'Logistic', 'SVM']
2025-07-16 22:53:23 - enhanced_ensemble_selector - INFO - 综合得分: 0.6357
2025-07-16 23:04:40 - enhanced_ensemble_selector - INFO - 开始评估 5 个基模型...
2025-07-16 23:04:40 - enhanced_ensemble_selector - INFO -   评估模型: DecisionTree
2025-07-16 23:04:40 - enhanced_ensemble_selector - INFO -     DecisionTree - 性能得分: 0.7708
2025-07-16 23:04:40 - enhanced_ensemble_selector - INFO -   评估模型: RandomForest
2025-07-16 23:04:40 - enhanced_ensemble_selector - INFO -     RandomForest - 性能得分: 0.9288
2025-07-16 23:04:40 - enhanced_ensemble_selector - INFO -   评估模型: Logistic
2025-07-16 23:04:40 - enhanced_ensemble_selector - INFO -     Logistic - 性能得分: 0.8346
2025-07-16 23:04:40 - enhanced_ensemble_selector - INFO -   评估模型: SVM
2025-07-16 23:04:40 - enhanced_ensemble_selector - INFO -     SVM - 性能得分: 0.9518
2025-07-16 23:04:40 - enhanced_ensemble_selector - INFO -   评估模型: XGBoost
2025-07-16 23:04:40 - enhanced_ensemble_selector - INFO -     XGBoost - 性能得分: 0.9244
2025-07-16 23:04:41 - enhanced_ensemble_selector - INFO - 使用改进的多样性分析完成
2025-07-16 23:04:41 - enhanced_ensemble_selector - INFO - 数据复杂度: 0.103
2025-07-16 23:04:41 - enhanced_ensemble_selector - INFO - 调整后相关性阈值: 0.600
2025-07-16 23:04:41 - enhanced_ensemble_selector - INFO - 综合多样性得分: 0.279
2025-07-16 23:04:41 - enhanced_ensemble_selector - INFO -   DecisionTree vs RandomForest: 相关性=0.737, 多样性=0.263
2025-07-16 23:04:41 - enhanced_ensemble_selector - INFO -   DecisionTree vs Logistic: 相关性=0.602, 多样性=0.398
2025-07-16 23:04:41 - enhanced_ensemble_selector - INFO -   DecisionTree vs SVM: 相关性=0.616, 多样性=0.384
2025-07-16 23:04:41 - enhanced_ensemble_selector - INFO -   DecisionTree vs XGBoost: 相关性=0.679, 多样性=0.321
2025-07-16 23:04:41 - enhanced_ensemble_selector - INFO -   RandomForest vs Logistic: 相关性=0.850, 多样性=0.150
2025-07-16 23:04:41 - enhanced_ensemble_selector - INFO -   RandomForest vs SVM: 相关性=0.921, 多样性=0.079
2025-07-16 23:04:41 - enhanced_ensemble_selector - INFO -   RandomForest vs XGBoost: 相关性=0.920, 多样性=0.080
2025-07-16 23:04:41 - enhanced_ensemble_selector - INFO -   Logistic vs SVM: 相关性=0.820, 多样性=0.180
2025-07-16 23:04:41 - enhanced_ensemble_selector - INFO -   Logistic vs XGBoost: 相关性=0.803, 多样性=0.197
2025-07-16 23:04:41 - enhanced_ensemble_selector - INFO -   SVM vs XGBoost: 相关性=0.938, 多样性=0.062
2025-07-16 23:04:41 - enhanced_ensemble_selector - INFO - 特征重要性多样性: 0.378
2025-07-16 23:04:41 - enhanced_ensemble_selector - INFO - 算法类型多样性: 0.300
2025-07-16 23:04:41 - enhanced_ensemble_selector - INFO - 算法类型分布: {'tree': 3, 'linear': 2}
2025-07-16 23:04:41 - enhanced_ensemble_selector - INFO - 开始量化多样性评估...
2025-07-16 23:04:41 - enhanced_ensemble_selector - INFO - 量化多样性评估完成:
2025-07-16 23:04:41 - enhanced_ensemble_selector - INFO -   Q统计量平均多样性: 0.383
2025-07-16 23:04:41 - enhanced_ensemble_selector - INFO -   多样性变异系数: 0.316
2025-07-16 23:04:41 - enhanced_ensemble_selector - INFO -   熵多样性: 0.289
2025-07-16 23:04:41 - enhanced_ensemble_selector - INFO - 从 5 个合格模型中选择 3 个进行集成
2025-07-16 23:04:41 - enhanced_ensemble_selector - INFO - 最优组合: ['DecisionTree', 'Logistic', 'SVM']
2025-07-16 23:04:41 - enhanced_ensemble_selector - INFO - 综合得分: 0.6357
2025-07-16 23:04:41 - enhanced_ensemble_selector - INFO - 从 5 个合格模型中选择 3 个进行集成
2025-07-16 23:04:41 - enhanced_ensemble_selector - INFO - 使用量化多样性评估进行模型选择...
2025-07-16 23:04:41 - enhanced_ensemble_selector - ERROR - 无法获取测试标签，回退到传统方法
2025-07-16 23:07:42 - enhanced_ensemble_selector - INFO - 开始评估 10 个基模型...
2025-07-16 23:07:42 - enhanced_ensemble_selector - INFO -   评估模型: DecisionTree
2025-07-16 23:07:42 - enhanced_ensemble_selector - INFO -     DecisionTree - 性能得分: 0.8461
2025-07-16 23:07:42 - enhanced_ensemble_selector - INFO -   评估模型: RandomForest
2025-07-16 23:07:42 - enhanced_ensemble_selector - INFO -     RandomForest - 性能得分: 0.8439
2025-07-16 23:07:42 - enhanced_ensemble_selector - INFO -   评估模型: XGBoost
2025-07-16 23:07:42 - enhanced_ensemble_selector - INFO -     XGBoost - 性能得分: 0.7959
2025-07-16 23:07:42 - enhanced_ensemble_selector - INFO -   评估模型: LightGBM
2025-07-16 23:07:44 - enhanced_ensemble_selector - INFO -     LightGBM - 性能得分: 0.7759
2025-07-16 23:07:44 - enhanced_ensemble_selector - INFO -   评估模型: CatBoost
2025-07-16 23:07:45 - enhanced_ensemble_selector - INFO -     CatBoost - 性能得分: 0.8828
2025-07-16 23:07:45 - enhanced_ensemble_selector - INFO -   评估模型: Logistic
2025-07-16 23:07:45 - enhanced_ensemble_selector - INFO -     Logistic - 性能得分: 0.8449
2025-07-16 23:07:45 - enhanced_ensemble_selector - INFO -   评估模型: SVM
2025-07-16 23:07:45 - enhanced_ensemble_selector - INFO -     SVM - 性能得分: 0.8892
2025-07-16 23:07:45 - enhanced_ensemble_selector - INFO -   评估模型: KNN
2025-07-16 23:07:45 - enhanced_ensemble_selector - INFO -     KNN - 性能得分: 0.8978
2025-07-16 23:07:45 - enhanced_ensemble_selector - INFO -   评估模型: NaiveBayes
2025-07-16 23:07:45 - enhanced_ensemble_selector - INFO -     NaiveBayes - 性能得分: 0.8831
2025-07-16 23:07:45 - enhanced_ensemble_selector - INFO -   评估模型: NeuralNet
2025-07-16 23:07:46 - enhanced_ensemble_selector - INFO -     NeuralNet - 性能得分: 0.8664
2025-07-16 23:07:46 - enhanced_ensemble_selector - INFO - 使用改进的多样性分析完成
2025-07-16 23:07:46 - enhanced_ensemble_selector - INFO - 数据复杂度: 0.232
2025-07-16 23:07:46 - enhanced_ensemble_selector - INFO - 调整后相关性阈值: 0.600
2025-07-16 23:07:46 - enhanced_ensemble_selector - INFO - 综合多样性得分: 0.402
2025-07-16 23:07:46 - enhanced_ensemble_selector - INFO -   DecisionTree vs RandomForest: 相关性=0.866, 多样性=0.134
2025-07-16 23:07:46 - enhanced_ensemble_selector - INFO -   DecisionTree vs XGBoost: 相关性=0.800, 多样性=0.200
2025-07-16 23:07:46 - enhanced_ensemble_selector - INFO -   DecisionTree vs LightGBM: 相关性=0.789, 多样性=0.211
2025-07-16 23:07:46 - enhanced_ensemble_selector - INFO -   DecisionTree vs CatBoost: 相关性=0.848, 多样性=0.152
2025-07-16 23:07:46 - enhanced_ensemble_selector - INFO -   DecisionTree vs Logistic: 相关性=0.733, 多样性=0.267
2025-07-16 23:07:46 - enhanced_ensemble_selector - INFO -   DecisionTree vs SVM: 相关性=0.722, 多样性=0.278
2025-07-16 23:07:46 - enhanced_ensemble_selector - INFO -   DecisionTree vs KNN: 相关性=0.726, 多样性=0.274
2025-07-16 23:07:46 - enhanced_ensemble_selector - INFO -   DecisionTree vs NaiveBayes: 相关性=0.708, 多样性=0.292
2025-07-16 23:07:46 - enhanced_ensemble_selector - INFO -   DecisionTree vs NeuralNet: 相关性=0.733, 多样性=0.267
2025-07-16 23:07:46 - enhanced_ensemble_selector - INFO -   RandomForest vs XGBoost: 相关性=0.906, 多样性=0.094
2025-07-16 23:07:46 - enhanced_ensemble_selector - INFO -   RandomForest vs LightGBM: 相关性=0.918, 多样性=0.082
2025-07-16 23:07:46 - enhanced_ensemble_selector - INFO -   RandomForest vs CatBoost: 相关性=0.977, 多样性=0.023
2025-07-16 23:07:46 - enhanced_ensemble_selector - INFO -   RandomForest vs Logistic: 相关性=0.903, 多样性=0.097
2025-07-16 23:07:46 - enhanced_ensemble_selector - INFO -   RandomForest vs SVM: 相关性=0.885, 多样性=0.115
2025-07-16 23:07:46 - enhanced_ensemble_selector - INFO -   RandomForest vs KNN: 相关性=0.887, 多样性=0.113
2025-07-16 23:07:46 - enhanced_ensemble_selector - INFO -   RandomForest vs NaiveBayes: 相关性=0.810, 多样性=0.190
2025-07-16 23:07:46 - enhanced_ensemble_selector - INFO -   RandomForest vs NeuralNet: 相关性=0.865, 多样性=0.135
2025-07-16 23:07:46 - enhanced_ensemble_selector - INFO -   XGBoost vs LightGBM: 相关性=0.956, 多样性=0.044
2025-07-16 23:07:46 - enhanced_ensemble_selector - INFO -   XGBoost vs CatBoost: 相关性=0.911, 多样性=0.089
2025-07-16 23:07:46 - enhanced_ensemble_selector - INFO -   XGBoost vs Logistic: 相关性=0.848, 多样性=0.152
2025-07-16 23:07:46 - enhanced_ensemble_selector - INFO -   XGBoost vs SVM: 相关性=0.758, 多样性=0.242
2025-07-16 23:07:46 - enhanced_ensemble_selector - INFO -   XGBoost vs KNN: 相关性=0.775, 多样性=0.225
2025-07-16 23:07:46 - enhanced_ensemble_selector - INFO -   XGBoost vs NaiveBayes: 相关性=0.702, 多样性=0.298
2025-07-16 23:07:46 - enhanced_ensemble_selector - INFO -   XGBoost vs NeuralNet: 相关性=0.786, 多样性=0.214
2025-07-16 23:07:46 - enhanced_ensemble_selector - INFO -   LightGBM vs CatBoost: 相关性=0.939, 多样性=0.061
2025-07-16 23:07:46 - enhanced_ensemble_selector - INFO -   LightGBM vs Logistic: 相关性=0.834, 多样性=0.166
2025-07-16 23:07:46 - enhanced_ensemble_selector - INFO -   LightGBM vs SVM: 相关性=0.769, 多样性=0.231
2025-07-16 23:07:46 - enhanced_ensemble_selector - INFO -   LightGBM vs KNN: 相关性=0.778, 多样性=0.222
2025-07-16 23:07:46 - enhanced_ensemble_selector - INFO -   LightGBM vs NaiveBayes: 相关性=0.670, 多样性=0.330
2025-07-16 23:07:46 - enhanced_ensemble_selector - INFO -   LightGBM vs NeuralNet: 相关性=0.796, 多样性=0.204
2025-07-16 23:07:46 - enhanced_ensemble_selector - INFO -   CatBoost vs Logistic: 相关性=0.910, 多样性=0.090
2025-07-16 23:07:46 - enhanced_ensemble_selector - INFO -   CatBoost vs SVM: 相关性=0.896, 多样性=0.104
2025-07-16 23:07:46 - enhanced_ensemble_selector - INFO -   CatBoost vs KNN: 相关性=0.898, 多样性=0.102
2025-07-16 23:07:46 - enhanced_ensemble_selector - INFO -   CatBoost vs NaiveBayes: 相关性=0.826, 多样性=0.174
2025-07-16 23:07:46 - enhanced_ensemble_selector - INFO -   CatBoost vs NeuralNet: 相关性=0.904, 多样性=0.096
2025-07-16 23:07:46 - enhanced_ensemble_selector - INFO -   Logistic vs SVM: 相关性=0.901, 多样性=0.099
2025-07-16 23:07:46 - enhanced_ensemble_selector - INFO -   Logistic vs KNN: 相关性=0.890, 多样性=0.110
2025-07-16 23:07:46 - enhanced_ensemble_selector - INFO -   Logistic vs NaiveBayes: 相关性=0.832, 多样性=0.168
2025-07-16 23:07:46 - enhanced_ensemble_selector - INFO -   Logistic vs NeuralNet: 相关性=0.945, 多样性=0.055
2025-07-16 23:07:46 - enhanced_ensemble_selector - INFO -   SVM vs KNN: 相关性=0.942, 多样性=0.058
2025-07-16 23:07:46 - enhanced_ensemble_selector - INFO -   SVM vs NaiveBayes: 相关性=0.870, 多样性=0.130
2025-07-16 23:07:46 - enhanced_ensemble_selector - INFO -   SVM vs NeuralNet: 相关性=0.908, 多样性=0.092
2025-07-16 23:07:46 - enhanced_ensemble_selector - INFO -   KNN vs NaiveBayes: 相关性=0.959, 多样性=0.041
2025-07-16 23:07:46 - enhanced_ensemble_selector - INFO -   KNN vs NeuralNet: 相关性=0.866, 多样性=0.134
2025-07-16 23:07:46 - enhanced_ensemble_selector - INFO -   NaiveBayes vs NeuralNet: 相关性=0.796, 多样性=0.204
2025-07-16 23:07:46 - enhanced_ensemble_selector - INFO - 特征重要性多样性: 0.413
2025-07-16 23:07:46 - enhanced_ensemble_selector - INFO - 算法类型多样性: 1.000
2025-07-16 23:07:46 - enhanced_ensemble_selector - INFO - 算法类型分布: {'tree': 5, 'linear': 2, 'instance': 1, 'probabilistic': 1, 'neural': 1}
2025-07-16 23:07:46 - enhanced_ensemble_selector - INFO - 开始量化多样性评估...
2025-07-16 23:07:46 - enhanced_ensemble_selector - INFO - 量化多样性评估完成:
2025-07-16 23:07:46 - enhanced_ensemble_selector - INFO -   Q统计量平均多样性: 0.351
2025-07-16 23:07:46 - enhanced_ensemble_selector - INFO -   多样性变异系数: 0.424
2025-07-16 23:07:46 - enhanced_ensemble_selector - INFO -   熵多样性: 0.299
2025-07-16 23:07:46 - enhanced_ensemble_selector - INFO - 从 10 个合格模型中选择 3 个进行集成
2025-07-16 23:07:46 - enhanced_ensemble_selector - INFO - 使用量化多样性评估进行模型选择...
2025-07-16 23:07:46 - enhanced_ensemble_selector - ERROR - 无法获取测试标签，回退到传统方法
2025-07-16 23:09:17 - enhanced_ensemble_selector - INFO - 开始评估 10 个基模型...
2025-07-16 23:09:17 - enhanced_ensemble_selector - INFO -   评估模型: DecisionTree
2025-07-16 23:09:17 - enhanced_ensemble_selector - INFO -     DecisionTree - 性能得分: 0.8461
2025-07-16 23:09:17 - enhanced_ensemble_selector - INFO -   评估模型: RandomForest
2025-07-16 23:09:17 - enhanced_ensemble_selector - INFO -     RandomForest - 性能得分: 0.8439
2025-07-16 23:09:17 - enhanced_ensemble_selector - INFO -   评估模型: XGBoost
2025-07-16 23:09:17 - enhanced_ensemble_selector - INFO -     XGBoost - 性能得分: 0.7959
2025-07-16 23:09:17 - enhanced_ensemble_selector - INFO -   评估模型: LightGBM
2025-07-16 23:09:17 - enhanced_ensemble_selector - INFO -     LightGBM - 性能得分: 0.7759
2025-07-16 23:09:17 - enhanced_ensemble_selector - INFO -   评估模型: CatBoost
2025-07-16 23:09:18 - enhanced_ensemble_selector - INFO -     CatBoost - 性能得分: 0.8828
2025-07-16 23:09:18 - enhanced_ensemble_selector - INFO -   评估模型: Logistic
2025-07-16 23:09:18 - enhanced_ensemble_selector - INFO -     Logistic - 性能得分: 0.8449
2025-07-16 23:09:18 - enhanced_ensemble_selector - INFO -   评估模型: SVM
2025-07-16 23:09:18 - enhanced_ensemble_selector - INFO -     SVM - 性能得分: 0.8892
2025-07-16 23:09:18 - enhanced_ensemble_selector - INFO -   评估模型: KNN
2025-07-16 23:09:18 - enhanced_ensemble_selector - INFO -     KNN - 性能得分: 0.8978
2025-07-16 23:09:18 - enhanced_ensemble_selector - INFO -   评估模型: NaiveBayes
2025-07-16 23:09:18 - enhanced_ensemble_selector - INFO -     NaiveBayes - 性能得分: 0.8831
2025-07-16 23:09:18 - enhanced_ensemble_selector - INFO -   评估模型: NeuralNet
2025-07-16 23:09:18 - enhanced_ensemble_selector - INFO -     NeuralNet - 性能得分: 0.8664
2025-07-16 23:09:18 - enhanced_ensemble_selector - INFO - 使用改进的多样性分析完成
2025-07-16 23:09:18 - enhanced_ensemble_selector - INFO - 数据复杂度: 0.232
2025-07-16 23:09:18 - enhanced_ensemble_selector - INFO - 调整后相关性阈值: 0.600
2025-07-16 23:09:18 - enhanced_ensemble_selector - INFO - 综合多样性得分: 0.402
2025-07-16 23:09:18 - enhanced_ensemble_selector - INFO -   DecisionTree vs RandomForest: 相关性=0.866, 多样性=0.134
2025-07-16 23:09:18 - enhanced_ensemble_selector - INFO -   DecisionTree vs XGBoost: 相关性=0.800, 多样性=0.200
2025-07-16 23:09:18 - enhanced_ensemble_selector - INFO -   DecisionTree vs LightGBM: 相关性=0.789, 多样性=0.211
2025-07-16 23:09:18 - enhanced_ensemble_selector - INFO -   DecisionTree vs CatBoost: 相关性=0.848, 多样性=0.152
2025-07-16 23:09:18 - enhanced_ensemble_selector - INFO -   DecisionTree vs Logistic: 相关性=0.733, 多样性=0.267
2025-07-16 23:09:18 - enhanced_ensemble_selector - INFO -   DecisionTree vs SVM: 相关性=0.722, 多样性=0.278
2025-07-16 23:09:18 - enhanced_ensemble_selector - INFO -   DecisionTree vs KNN: 相关性=0.726, 多样性=0.274
2025-07-16 23:09:18 - enhanced_ensemble_selector - INFO -   DecisionTree vs NaiveBayes: 相关性=0.708, 多样性=0.292
2025-07-16 23:09:18 - enhanced_ensemble_selector - INFO -   DecisionTree vs NeuralNet: 相关性=0.733, 多样性=0.267
2025-07-16 23:09:18 - enhanced_ensemble_selector - INFO -   RandomForest vs XGBoost: 相关性=0.906, 多样性=0.094
2025-07-16 23:09:18 - enhanced_ensemble_selector - INFO -   RandomForest vs LightGBM: 相关性=0.918, 多样性=0.082
2025-07-16 23:09:18 - enhanced_ensemble_selector - INFO -   RandomForest vs CatBoost: 相关性=0.977, 多样性=0.023
2025-07-16 23:09:18 - enhanced_ensemble_selector - INFO -   RandomForest vs Logistic: 相关性=0.903, 多样性=0.097
2025-07-16 23:09:18 - enhanced_ensemble_selector - INFO -   RandomForest vs SVM: 相关性=0.885, 多样性=0.115
2025-07-16 23:09:18 - enhanced_ensemble_selector - INFO -   RandomForest vs KNN: 相关性=0.887, 多样性=0.113
2025-07-16 23:09:18 - enhanced_ensemble_selector - INFO -   RandomForest vs NaiveBayes: 相关性=0.810, 多样性=0.190
2025-07-16 23:09:18 - enhanced_ensemble_selector - INFO -   RandomForest vs NeuralNet: 相关性=0.865, 多样性=0.135
2025-07-16 23:09:18 - enhanced_ensemble_selector - INFO -   XGBoost vs LightGBM: 相关性=0.956, 多样性=0.044
2025-07-16 23:09:18 - enhanced_ensemble_selector - INFO -   XGBoost vs CatBoost: 相关性=0.911, 多样性=0.089
2025-07-16 23:09:18 - enhanced_ensemble_selector - INFO -   XGBoost vs Logistic: 相关性=0.848, 多样性=0.152
2025-07-16 23:09:18 - enhanced_ensemble_selector - INFO -   XGBoost vs SVM: 相关性=0.758, 多样性=0.242
2025-07-16 23:09:18 - enhanced_ensemble_selector - INFO -   XGBoost vs KNN: 相关性=0.775, 多样性=0.225
2025-07-16 23:09:18 - enhanced_ensemble_selector - INFO -   XGBoost vs NaiveBayes: 相关性=0.702, 多样性=0.298
2025-07-16 23:09:18 - enhanced_ensemble_selector - INFO -   XGBoost vs NeuralNet: 相关性=0.786, 多样性=0.214
2025-07-16 23:09:18 - enhanced_ensemble_selector - INFO -   LightGBM vs CatBoost: 相关性=0.939, 多样性=0.061
2025-07-16 23:09:18 - enhanced_ensemble_selector - INFO -   LightGBM vs Logistic: 相关性=0.834, 多样性=0.166
2025-07-16 23:09:18 - enhanced_ensemble_selector - INFO -   LightGBM vs SVM: 相关性=0.769, 多样性=0.231
2025-07-16 23:09:18 - enhanced_ensemble_selector - INFO -   LightGBM vs KNN: 相关性=0.778, 多样性=0.222
2025-07-16 23:09:18 - enhanced_ensemble_selector - INFO -   LightGBM vs NaiveBayes: 相关性=0.670, 多样性=0.330
2025-07-16 23:09:18 - enhanced_ensemble_selector - INFO -   LightGBM vs NeuralNet: 相关性=0.796, 多样性=0.204
2025-07-16 23:09:18 - enhanced_ensemble_selector - INFO -   CatBoost vs Logistic: 相关性=0.910, 多样性=0.090
2025-07-16 23:09:18 - enhanced_ensemble_selector - INFO -   CatBoost vs SVM: 相关性=0.896, 多样性=0.104
2025-07-16 23:09:18 - enhanced_ensemble_selector - INFO -   CatBoost vs KNN: 相关性=0.898, 多样性=0.102
2025-07-16 23:09:18 - enhanced_ensemble_selector - INFO -   CatBoost vs NaiveBayes: 相关性=0.826, 多样性=0.174
2025-07-16 23:09:18 - enhanced_ensemble_selector - INFO -   CatBoost vs NeuralNet: 相关性=0.904, 多样性=0.096
2025-07-16 23:09:18 - enhanced_ensemble_selector - INFO -   Logistic vs SVM: 相关性=0.901, 多样性=0.099
2025-07-16 23:09:18 - enhanced_ensemble_selector - INFO -   Logistic vs KNN: 相关性=0.890, 多样性=0.110
2025-07-16 23:09:18 - enhanced_ensemble_selector - INFO -   Logistic vs NaiveBayes: 相关性=0.832, 多样性=0.168
2025-07-16 23:09:18 - enhanced_ensemble_selector - INFO -   Logistic vs NeuralNet: 相关性=0.945, 多样性=0.055
2025-07-16 23:09:18 - enhanced_ensemble_selector - INFO -   SVM vs KNN: 相关性=0.942, 多样性=0.058
2025-07-16 23:09:18 - enhanced_ensemble_selector - INFO -   SVM vs NaiveBayes: 相关性=0.870, 多样性=0.130
2025-07-16 23:09:18 - enhanced_ensemble_selector - INFO -   SVM vs NeuralNet: 相关性=0.908, 多样性=0.092
2025-07-16 23:09:18 - enhanced_ensemble_selector - INFO -   KNN vs NaiveBayes: 相关性=0.959, 多样性=0.041
2025-07-16 23:09:18 - enhanced_ensemble_selector - INFO -   KNN vs NeuralNet: 相关性=0.866, 多样性=0.134
2025-07-16 23:09:18 - enhanced_ensemble_selector - INFO -   NaiveBayes vs NeuralNet: 相关性=0.796, 多样性=0.204
2025-07-16 23:09:18 - enhanced_ensemble_selector - INFO - 特征重要性多样性: 0.413
2025-07-16 23:09:18 - enhanced_ensemble_selector - INFO - 算法类型多样性: 1.000
2025-07-16 23:09:18 - enhanced_ensemble_selector - INFO - 算法类型分布: {'tree': 5, 'linear': 2, 'instance': 1, 'probabilistic': 1, 'neural': 1}
2025-07-16 23:09:18 - enhanced_ensemble_selector - INFO - 开始量化多样性评估...
2025-07-16 23:09:19 - enhanced_ensemble_selector - INFO - 量化多样性评估完成:
2025-07-16 23:09:19 - enhanced_ensemble_selector - INFO -   Q统计量平均多样性: 0.351
2025-07-16 23:09:19 - enhanced_ensemble_selector - INFO -   多样性变异系数: 0.424
2025-07-16 23:09:19 - enhanced_ensemble_selector - INFO -   熵多样性: 0.299
2025-07-16 23:09:19 - enhanced_ensemble_selector - INFO - 从 10 个合格模型中选择 3 个进行集成
2025-07-16 23:09:19 - enhanced_ensemble_selector - INFO - 使用量化多样性评估进行模型选择...
2025-07-16 23:09:19 - enhanced_ensemble_selector - ERROR - 无法获取测试标签，回退到传统方法
2025-07-16 23:15:58 - enhanced_ensemble_selector - INFO - 开始评估 5 个基模型...
2025-07-16 23:15:58 - enhanced_ensemble_selector - INFO -   评估模型: DecisionTree
2025-07-16 23:15:58 - enhanced_ensemble_selector - INFO -     DecisionTree - 性能得分: 0.8093
2025-07-16 23:15:58 - enhanced_ensemble_selector - INFO -   评估模型: RandomForest
2025-07-16 23:15:58 - enhanced_ensemble_selector - INFO -     RandomForest - 性能得分: 0.9070
2025-07-16 23:15:58 - enhanced_ensemble_selector - INFO -   评估模型: Logistic
2025-07-16 23:15:58 - enhanced_ensemble_selector - INFO -     Logistic - 性能得分: 0.8292
2025-07-16 23:15:58 - enhanced_ensemble_selector - INFO -   评估模型: SVM
2025-07-16 23:15:58 - enhanced_ensemble_selector - INFO -     SVM - 性能得分: 0.9160
2025-07-16 23:15:58 - enhanced_ensemble_selector - INFO -   评估模型: XGBoost
2025-07-16 23:15:58 - enhanced_ensemble_selector - INFO -     XGBoost - 性能得分: 0.8872
2025-07-16 23:15:59 - enhanced_ensemble_selector - INFO - 使用改进的多样性分析完成
2025-07-16 23:15:59 - enhanced_ensemble_selector - INFO - 数据复杂度: 0.119
2025-07-16 23:15:59 - enhanced_ensemble_selector - INFO - 调整后相关性阈值: 0.600
2025-07-16 23:15:59 - enhanced_ensemble_selector - INFO - 综合多样性得分: 0.259
2025-07-16 23:15:59 - enhanced_ensemble_selector - INFO -   DecisionTree vs RandomForest: 相关性=0.828, 多样性=0.172
2025-07-16 23:15:59 - enhanced_ensemble_selector - INFO -   DecisionTree vs Logistic: 相关性=0.699, 多样性=0.301
2025-07-16 23:15:59 - enhanced_ensemble_selector - INFO -   DecisionTree vs SVM: 相关性=0.762, 多样性=0.238
2025-07-16 23:15:59 - enhanced_ensemble_selector - INFO -   DecisionTree vs XGBoost: 相关性=0.795, 多样性=0.205
2025-07-16 23:15:59 - enhanced_ensemble_selector - INFO -   RandomForest vs Logistic: 相关性=0.850, 多样性=0.150
2025-07-16 23:15:59 - enhanced_ensemble_selector - INFO -   RandomForest vs SVM: 相关性=0.938, 多样性=0.062
2025-07-16 23:15:59 - enhanced_ensemble_selector - INFO -   RandomForest vs XGBoost: 相关性=0.918, 多样性=0.082
2025-07-16 23:15:59 - enhanced_ensemble_selector - INFO -   Logistic vs SVM: 相关性=0.794, 多样性=0.206
2025-07-16 23:15:59 - enhanced_ensemble_selector - INFO -   Logistic vs XGBoost: 相关性=0.780, 多样性=0.220
2025-07-16 23:15:59 - enhanced_ensemble_selector - INFO -   SVM vs XGBoost: 相关性=0.923, 多样性=0.077
2025-07-16 23:15:59 - enhanced_ensemble_selector - INFO - 特征重要性多样性: 0.379
2025-07-16 23:15:59 - enhanced_ensemble_selector - INFO - 算法类型多样性: 0.300
2025-07-16 23:15:59 - enhanced_ensemble_selector - INFO - 算法类型分布: {'tree': 3, 'linear': 2}
2025-07-16 23:15:59 - enhanced_ensemble_selector - INFO - 开始量化多样性评估...
2025-07-16 23:15:59 - enhanced_ensemble_selector - INFO - 量化多样性评估完成:
2025-07-16 23:15:59 - enhanced_ensemble_selector - INFO -   Q统计量平均多样性: 0.338
2025-07-16 23:15:59 - enhanced_ensemble_selector - INFO -   多样性变异系数: 0.129
2025-07-16 23:15:59 - enhanced_ensemble_selector - INFO -   熵多样性: 0.261
2025-07-16 23:15:59 - enhanced_ensemble_selector - INFO - 从 5 个合格模型中选择 3 个进行集成
2025-07-16 23:15:59 - enhanced_ensemble_selector - INFO - 使用量化多样性评估进行模型选择...
2025-07-16 23:15:59 - enhanced_ensemble_selector - INFO - 开始评估 4 个基模型...
2025-07-16 23:15:59 - enhanced_ensemble_selector - INFO -   评估模型: DecisionTree
2025-07-16 23:15:59 - enhanced_ensemble_selector - INFO -     DecisionTree - 性能得分: 0.7279
2025-07-16 23:15:59 - enhanced_ensemble_selector - INFO -   评估模型: RandomForest
2025-07-16 23:15:59 - enhanced_ensemble_selector - INFO -     RandomForest - 性能得分: 0.8630
2025-07-16 23:15:59 - enhanced_ensemble_selector - INFO -   评估模型: Logistic
2025-07-16 23:15:59 - enhanced_ensemble_selector - INFO -     Logistic - 性能得分: 0.7726
2025-07-16 23:15:59 - enhanced_ensemble_selector - INFO -   评估模型: SVM
2025-07-16 23:15:59 - enhanced_ensemble_selector - INFO -     SVM - 性能得分: 0.8912
2025-07-16 23:15:59 - enhanced_ensemble_selector - INFO - 使用改进的多样性分析完成
2025-07-16 23:15:59 - enhanced_ensemble_selector - INFO - 数据复杂度: 0.133
2025-07-16 23:15:59 - enhanced_ensemble_selector - INFO - 调整后相关性阈值: 0.600
2025-07-16 23:15:59 - enhanced_ensemble_selector - INFO - 综合多样性得分: 0.435
2025-07-16 23:15:59 - enhanced_ensemble_selector - INFO -   DecisionTree vs RandomForest: 相关性=0.744, 多样性=0.256
2025-07-16 23:15:59 - enhanced_ensemble_selector - INFO -   DecisionTree vs Logistic: 相关性=0.520, 多样性=0.480
2025-07-16 23:15:59 - enhanced_ensemble_selector - INFO -   DecisionTree vs SVM: 相关性=0.670, 多样性=0.330
2025-07-16 23:15:59 - enhanced_ensemble_selector - INFO -   RandomForest vs Logistic: 相关性=0.761, 多样性=0.239
2025-07-16 23:15:59 - enhanced_ensemble_selector - INFO -   RandomForest vs SVM: 相关性=0.915, 多样性=0.085
2025-07-16 23:15:59 - enhanced_ensemble_selector - INFO -   Logistic vs SVM: 相关性=0.723, 多样性=0.277
2025-07-16 23:15:59 - enhanced_ensemble_selector - INFO - 特征重要性多样性: 0.653
2025-07-16 23:15:59 - enhanced_ensemble_selector - INFO - 算法类型多样性: 0.500
2025-07-16 23:15:59 - enhanced_ensemble_selector - INFO - 算法类型分布: {'tree': 2, 'linear': 2}
2025-07-16 23:15:59 - enhanced_ensemble_selector - INFO - 开始量化多样性评估...
2025-07-16 23:15:59 - enhanced_ensemble_selector - INFO - 量化多样性评估完成:
2025-07-16 23:15:59 - enhanced_ensemble_selector - INFO -   Q统计量平均多样性: 0.410
2025-07-16 23:15:59 - enhanced_ensemble_selector - INFO -   多样性变异系数: 0.277
2025-07-16 23:15:59 - enhanced_ensemble_selector - INFO -   熵多样性: 0.340
2025-07-16 23:15:59 - enhanced_ensemble_selector - INFO - 从 4 个合格模型中选择 3 个进行集成
2025-07-16 23:15:59 - enhanced_ensemble_selector - INFO - 最优组合: ['RandomForest', 'Logistic', 'SVM']
2025-07-16 23:15:59 - enhanced_ensemble_selector - INFO - 综合得分: 0.8423
2025-07-16 23:15:59 - enhanced_ensemble_selector - INFO - 从 4 个合格模型中选择 3 个进行集成
2025-07-16 23:15:59 - enhanced_ensemble_selector - INFO - 最优组合: ['DecisionTree', 'Logistic', 'SVM']
2025-07-16 23:15:59 - enhanced_ensemble_selector - INFO - 综合得分: 0.4645
2025-07-16 23:15:59 - enhanced_ensemble_selector - INFO - 从 4 个合格模型中选择 3 个进行集成
2025-07-16 23:15:59 - enhanced_ensemble_selector - INFO - 最优组合: ['DecisionTree', 'Logistic', 'SVM']
2025-07-16 23:15:59 - enhanced_ensemble_selector - INFO - 综合得分: 0.6206
2025-07-16 23:15:59 - enhanced_ensemble_selector - INFO - 从 4 个合格模型中选择 3 个进行集成
2025-07-16 23:15:59 - enhanced_ensemble_selector - INFO - 使用量化多样性评估进行模型选择...
2025-07-16 23:17:08 - enhanced_ensemble_selector - INFO - 开始评估 5 个基模型...
2025-07-16 23:17:08 - enhanced_ensemble_selector - INFO -   评估模型: DecisionTree
2025-07-16 23:17:08 - enhanced_ensemble_selector - INFO -     DecisionTree - 性能得分: 0.8093
2025-07-16 23:17:08 - enhanced_ensemble_selector - INFO -   评估模型: RandomForest
2025-07-16 23:17:08 - enhanced_ensemble_selector - INFO -     RandomForest - 性能得分: 0.9070
2025-07-16 23:17:08 - enhanced_ensemble_selector - INFO -   评估模型: Logistic
2025-07-16 23:17:08 - enhanced_ensemble_selector - INFO -     Logistic - 性能得分: 0.8292
2025-07-16 23:17:08 - enhanced_ensemble_selector - INFO -   评估模型: SVM
2025-07-16 23:17:08 - enhanced_ensemble_selector - INFO -     SVM - 性能得分: 0.9160
2025-07-16 23:17:08 - enhanced_ensemble_selector - INFO -   评估模型: XGBoost
2025-07-16 23:17:08 - enhanced_ensemble_selector - INFO -     XGBoost - 性能得分: 0.8872
2025-07-16 23:17:08 - enhanced_ensemble_selector - INFO - 使用改进的多样性分析完成
2025-07-16 23:17:08 - enhanced_ensemble_selector - INFO - 数据复杂度: 0.119
2025-07-16 23:17:08 - enhanced_ensemble_selector - INFO - 调整后相关性阈值: 0.600
2025-07-16 23:17:08 - enhanced_ensemble_selector - INFO - 综合多样性得分: 0.259
2025-07-16 23:17:08 - enhanced_ensemble_selector - INFO -   DecisionTree vs RandomForest: 相关性=0.828, 多样性=0.172
2025-07-16 23:17:08 - enhanced_ensemble_selector - INFO -   DecisionTree vs Logistic: 相关性=0.699, 多样性=0.301
2025-07-16 23:17:08 - enhanced_ensemble_selector - INFO -   DecisionTree vs SVM: 相关性=0.762, 多样性=0.238
2025-07-16 23:17:08 - enhanced_ensemble_selector - INFO -   DecisionTree vs XGBoost: 相关性=0.795, 多样性=0.205
2025-07-16 23:17:08 - enhanced_ensemble_selector - INFO -   RandomForest vs Logistic: 相关性=0.850, 多样性=0.150
2025-07-16 23:17:08 - enhanced_ensemble_selector - INFO -   RandomForest vs SVM: 相关性=0.938, 多样性=0.062
2025-07-16 23:17:08 - enhanced_ensemble_selector - INFO -   RandomForest vs XGBoost: 相关性=0.918, 多样性=0.082
2025-07-16 23:17:08 - enhanced_ensemble_selector - INFO -   Logistic vs SVM: 相关性=0.794, 多样性=0.206
2025-07-16 23:17:08 - enhanced_ensemble_selector - INFO -   Logistic vs XGBoost: 相关性=0.780, 多样性=0.220
2025-07-16 23:17:08 - enhanced_ensemble_selector - INFO -   SVM vs XGBoost: 相关性=0.923, 多样性=0.077
2025-07-16 23:17:08 - enhanced_ensemble_selector - INFO - 特征重要性多样性: 0.379
2025-07-16 23:17:08 - enhanced_ensemble_selector - INFO - 算法类型多样性: 0.300
2025-07-16 23:17:08 - enhanced_ensemble_selector - INFO - 算法类型分布: {'tree': 3, 'linear': 2}
2025-07-16 23:17:08 - enhanced_ensemble_selector - INFO - 开始量化多样性评估...
2025-07-16 23:17:08 - enhanced_ensemble_selector - INFO - 量化多样性评估完成:
2025-07-16 23:17:08 - enhanced_ensemble_selector - INFO -   Q统计量平均多样性: 0.338
2025-07-16 23:17:08 - enhanced_ensemble_selector - INFO -   多样性变异系数: 0.129
2025-07-16 23:17:08 - enhanced_ensemble_selector - INFO -   熵多样性: 0.261
2025-07-16 23:17:08 - enhanced_ensemble_selector - INFO - 从 5 个合格模型中选择 3 个进行集成
2025-07-16 23:17:08 - enhanced_ensemble_selector - INFO - 使用量化多样性评估进行模型选择...
2025-07-16 23:17:08 - enhanced_ensemble_selector - INFO - 量化评估选择完成:
2025-07-16 23:17:08 - enhanced_ensemble_selector - INFO -   最优组合: ['DecisionTree', 'Logistic', 'XGBoost']
2025-07-16 23:17:08 - enhanced_ensemble_selector - INFO -   性能得分: 0.8419
2025-07-16 23:17:08 - enhanced_ensemble_selector - INFO -   多样性得分: 0.3908
2025-07-16 23:17:08 - enhanced_ensemble_selector - INFO -   综合得分: 0.6164
2025-07-16 23:17:08 - enhanced_ensemble_selector - INFO -   多样性等级: 中等的多样性
2025-07-16 23:17:08 - enhanced_ensemble_selector - INFO - 开始评估 4 个基模型...
2025-07-16 23:17:08 - enhanced_ensemble_selector - INFO -   评估模型: DecisionTree
2025-07-16 23:17:08 - enhanced_ensemble_selector - INFO -     DecisionTree - 性能得分: 0.7279
2025-07-16 23:17:08 - enhanced_ensemble_selector - INFO -   评估模型: RandomForest
2025-07-16 23:17:08 - enhanced_ensemble_selector - INFO -     RandomForest - 性能得分: 0.8630
2025-07-16 23:17:08 - enhanced_ensemble_selector - INFO -   评估模型: Logistic
2025-07-16 23:17:08 - enhanced_ensemble_selector - INFO -     Logistic - 性能得分: 0.7726
2025-07-16 23:17:08 - enhanced_ensemble_selector - INFO -   评估模型: SVM
2025-07-16 23:17:08 - enhanced_ensemble_selector - INFO -     SVM - 性能得分: 0.8912
2025-07-16 23:17:08 - enhanced_ensemble_selector - INFO - 使用改进的多样性分析完成
2025-07-16 23:17:08 - enhanced_ensemble_selector - INFO - 数据复杂度: 0.133
2025-07-16 23:17:08 - enhanced_ensemble_selector - INFO - 调整后相关性阈值: 0.600
2025-07-16 23:17:08 - enhanced_ensemble_selector - INFO - 综合多样性得分: 0.435
2025-07-16 23:17:08 - enhanced_ensemble_selector - INFO -   DecisionTree vs RandomForest: 相关性=0.744, 多样性=0.256
2025-07-16 23:17:08 - enhanced_ensemble_selector - INFO -   DecisionTree vs Logistic: 相关性=0.520, 多样性=0.480
2025-07-16 23:17:08 - enhanced_ensemble_selector - INFO -   DecisionTree vs SVM: 相关性=0.670, 多样性=0.330
2025-07-16 23:17:08 - enhanced_ensemble_selector - INFO -   RandomForest vs Logistic: 相关性=0.761, 多样性=0.239
2025-07-16 23:17:08 - enhanced_ensemble_selector - INFO -   RandomForest vs SVM: 相关性=0.915, 多样性=0.085
2025-07-16 23:17:08 - enhanced_ensemble_selector - INFO -   Logistic vs SVM: 相关性=0.723, 多样性=0.277
2025-07-16 23:17:08 - enhanced_ensemble_selector - INFO - 特征重要性多样性: 0.653
2025-07-16 23:17:08 - enhanced_ensemble_selector - INFO - 算法类型多样性: 0.500
2025-07-16 23:17:08 - enhanced_ensemble_selector - INFO - 算法类型分布: {'tree': 2, 'linear': 2}
2025-07-16 23:17:08 - enhanced_ensemble_selector - INFO - 开始量化多样性评估...
2025-07-16 23:17:08 - enhanced_ensemble_selector - INFO - 量化多样性评估完成:
2025-07-16 23:17:08 - enhanced_ensemble_selector - INFO -   Q统计量平均多样性: 0.410
2025-07-16 23:17:08 - enhanced_ensemble_selector - INFO -   多样性变异系数: 0.277
2025-07-16 23:17:08 - enhanced_ensemble_selector - INFO -   熵多样性: 0.340
2025-07-16 23:17:08 - enhanced_ensemble_selector - INFO - 从 4 个合格模型中选择 3 个进行集成
2025-07-16 23:17:08 - enhanced_ensemble_selector - INFO - 最优组合: ['RandomForest', 'Logistic', 'SVM']
2025-07-16 23:17:08 - enhanced_ensemble_selector - INFO - 综合得分: 0.8423
2025-07-16 23:17:08 - enhanced_ensemble_selector - INFO - 从 4 个合格模型中选择 3 个进行集成
2025-07-16 23:17:08 - enhanced_ensemble_selector - INFO - 最优组合: ['DecisionTree', 'Logistic', 'SVM']
2025-07-16 23:17:08 - enhanced_ensemble_selector - INFO - 综合得分: 0.4645
2025-07-16 23:17:08 - enhanced_ensemble_selector - INFO - 从 4 个合格模型中选择 3 个进行集成
2025-07-16 23:17:08 - enhanced_ensemble_selector - INFO - 最优组合: ['DecisionTree', 'Logistic', 'SVM']
2025-07-16 23:17:08 - enhanced_ensemble_selector - INFO - 综合得分: 0.6206
2025-07-16 23:17:08 - enhanced_ensemble_selector - INFO - 从 4 个合格模型中选择 3 个进行集成
2025-07-16 23:17:08 - enhanced_ensemble_selector - INFO - 使用量化多样性评估进行模型选择...
2025-07-16 23:17:08 - enhanced_ensemble_selector - INFO - 量化评估选择完成:
2025-07-16 23:17:08 - enhanced_ensemble_selector - INFO -   最优组合: ['DecisionTree', 'RandomForest', 'Logistic']
2025-07-16 23:17:08 - enhanced_ensemble_selector - INFO -   性能得分: 0.7878
2025-07-16 23:17:08 - enhanced_ensemble_selector - INFO -   多样性得分: 0.4790
2025-07-16 23:17:08 - enhanced_ensemble_selector - INFO -   综合得分: 0.6334
2025-07-16 23:17:08 - enhanced_ensemble_selector - INFO -   多样性等级: 中等的多样性
2025-07-16 23:19:30 - enhanced_ensemble_selector - INFO - 开始评估 10 个基模型...
2025-07-16 23:19:30 - enhanced_ensemble_selector - INFO -   评估模型: DecisionTree
2025-07-16 23:19:30 - enhanced_ensemble_selector - INFO -     DecisionTree - 性能得分: 0.8461
2025-07-16 23:19:30 - enhanced_ensemble_selector - INFO -   评估模型: RandomForest
2025-07-16 23:19:30 - enhanced_ensemble_selector - INFO -     RandomForest - 性能得分: 0.8439
2025-07-16 23:19:30 - enhanced_ensemble_selector - INFO -   评估模型: XGBoost
2025-07-16 23:19:30 - enhanced_ensemble_selector - INFO -     XGBoost - 性能得分: 0.7959
2025-07-16 23:19:30 - enhanced_ensemble_selector - INFO -   评估模型: LightGBM
2025-07-16 23:19:32 - enhanced_ensemble_selector - INFO -     LightGBM - 性能得分: 0.7759
2025-07-16 23:19:32 - enhanced_ensemble_selector - INFO -   评估模型: CatBoost
2025-07-16 23:19:33 - enhanced_ensemble_selector - INFO -     CatBoost - 性能得分: 0.8828
2025-07-16 23:19:33 - enhanced_ensemble_selector - INFO -   评估模型: Logistic
2025-07-16 23:19:33 - enhanced_ensemble_selector - INFO -     Logistic - 性能得分: 0.8449
2025-07-16 23:19:33 - enhanced_ensemble_selector - INFO -   评估模型: SVM
2025-07-16 23:19:33 - enhanced_ensemble_selector - INFO -     SVM - 性能得分: 0.8892
2025-07-16 23:19:33 - enhanced_ensemble_selector - INFO -   评估模型: KNN
2025-07-16 23:19:33 - enhanced_ensemble_selector - INFO -     KNN - 性能得分: 0.8978
2025-07-16 23:19:33 - enhanced_ensemble_selector - INFO -   评估模型: NaiveBayes
2025-07-16 23:19:33 - enhanced_ensemble_selector - INFO -     NaiveBayes - 性能得分: 0.8831
2025-07-16 23:19:33 - enhanced_ensemble_selector - INFO -   评估模型: NeuralNet
2025-07-16 23:19:33 - enhanced_ensemble_selector - INFO -     NeuralNet - 性能得分: 0.8664
2025-07-16 23:19:33 - enhanced_ensemble_selector - INFO - 使用改进的多样性分析完成
2025-07-16 23:19:33 - enhanced_ensemble_selector - INFO - 数据复杂度: 0.232
2025-07-16 23:19:33 - enhanced_ensemble_selector - INFO - 调整后相关性阈值: 0.600
2025-07-16 23:19:33 - enhanced_ensemble_selector - INFO - 综合多样性得分: 0.402
2025-07-16 23:19:33 - enhanced_ensemble_selector - INFO -   DecisionTree vs RandomForest: 相关性=0.866, 多样性=0.134
2025-07-16 23:19:33 - enhanced_ensemble_selector - INFO -   DecisionTree vs XGBoost: 相关性=0.800, 多样性=0.200
2025-07-16 23:19:33 - enhanced_ensemble_selector - INFO -   DecisionTree vs LightGBM: 相关性=0.789, 多样性=0.211
2025-07-16 23:19:33 - enhanced_ensemble_selector - INFO -   DecisionTree vs CatBoost: 相关性=0.848, 多样性=0.152
2025-07-16 23:19:33 - enhanced_ensemble_selector - INFO -   DecisionTree vs Logistic: 相关性=0.733, 多样性=0.267
2025-07-16 23:19:33 - enhanced_ensemble_selector - INFO -   DecisionTree vs SVM: 相关性=0.722, 多样性=0.278
2025-07-16 23:19:33 - enhanced_ensemble_selector - INFO -   DecisionTree vs KNN: 相关性=0.726, 多样性=0.274
2025-07-16 23:19:33 - enhanced_ensemble_selector - INFO -   DecisionTree vs NaiveBayes: 相关性=0.708, 多样性=0.292
2025-07-16 23:19:33 - enhanced_ensemble_selector - INFO -   DecisionTree vs NeuralNet: 相关性=0.733, 多样性=0.267
2025-07-16 23:19:33 - enhanced_ensemble_selector - INFO -   RandomForest vs XGBoost: 相关性=0.906, 多样性=0.094
2025-07-16 23:19:33 - enhanced_ensemble_selector - INFO -   RandomForest vs LightGBM: 相关性=0.918, 多样性=0.082
2025-07-16 23:19:33 - enhanced_ensemble_selector - INFO -   RandomForest vs CatBoost: 相关性=0.977, 多样性=0.023
2025-07-16 23:19:33 - enhanced_ensemble_selector - INFO -   RandomForest vs Logistic: 相关性=0.903, 多样性=0.097
2025-07-16 23:19:33 - enhanced_ensemble_selector - INFO -   RandomForest vs SVM: 相关性=0.885, 多样性=0.115
2025-07-16 23:19:33 - enhanced_ensemble_selector - INFO -   RandomForest vs KNN: 相关性=0.887, 多样性=0.113
2025-07-16 23:19:33 - enhanced_ensemble_selector - INFO -   RandomForest vs NaiveBayes: 相关性=0.810, 多样性=0.190
2025-07-16 23:19:33 - enhanced_ensemble_selector - INFO -   RandomForest vs NeuralNet: 相关性=0.865, 多样性=0.135
2025-07-16 23:19:33 - enhanced_ensemble_selector - INFO -   XGBoost vs LightGBM: 相关性=0.956, 多样性=0.044
2025-07-16 23:19:33 - enhanced_ensemble_selector - INFO -   XGBoost vs CatBoost: 相关性=0.911, 多样性=0.089
2025-07-16 23:19:33 - enhanced_ensemble_selector - INFO -   XGBoost vs Logistic: 相关性=0.848, 多样性=0.152
2025-07-16 23:19:33 - enhanced_ensemble_selector - INFO -   XGBoost vs SVM: 相关性=0.758, 多样性=0.242
2025-07-16 23:19:33 - enhanced_ensemble_selector - INFO -   XGBoost vs KNN: 相关性=0.775, 多样性=0.225
2025-07-16 23:19:33 - enhanced_ensemble_selector - INFO -   XGBoost vs NaiveBayes: 相关性=0.702, 多样性=0.298
2025-07-16 23:19:33 - enhanced_ensemble_selector - INFO -   XGBoost vs NeuralNet: 相关性=0.786, 多样性=0.214
2025-07-16 23:19:33 - enhanced_ensemble_selector - INFO -   LightGBM vs CatBoost: 相关性=0.939, 多样性=0.061
2025-07-16 23:19:33 - enhanced_ensemble_selector - INFO -   LightGBM vs Logistic: 相关性=0.834, 多样性=0.166
2025-07-16 23:19:33 - enhanced_ensemble_selector - INFO -   LightGBM vs SVM: 相关性=0.769, 多样性=0.231
2025-07-16 23:19:33 - enhanced_ensemble_selector - INFO -   LightGBM vs KNN: 相关性=0.778, 多样性=0.222
2025-07-16 23:19:33 - enhanced_ensemble_selector - INFO -   LightGBM vs NaiveBayes: 相关性=0.670, 多样性=0.330
2025-07-16 23:19:33 - enhanced_ensemble_selector - INFO -   LightGBM vs NeuralNet: 相关性=0.796, 多样性=0.204
2025-07-16 23:19:33 - enhanced_ensemble_selector - INFO -   CatBoost vs Logistic: 相关性=0.910, 多样性=0.090
2025-07-16 23:19:33 - enhanced_ensemble_selector - INFO -   CatBoost vs SVM: 相关性=0.896, 多样性=0.104
2025-07-16 23:19:33 - enhanced_ensemble_selector - INFO -   CatBoost vs KNN: 相关性=0.898, 多样性=0.102
2025-07-16 23:19:33 - enhanced_ensemble_selector - INFO -   CatBoost vs NaiveBayes: 相关性=0.826, 多样性=0.174
2025-07-16 23:19:33 - enhanced_ensemble_selector - INFO -   CatBoost vs NeuralNet: 相关性=0.904, 多样性=0.096
2025-07-16 23:19:33 - enhanced_ensemble_selector - INFO -   Logistic vs SVM: 相关性=0.901, 多样性=0.099
2025-07-16 23:19:33 - enhanced_ensemble_selector - INFO -   Logistic vs KNN: 相关性=0.890, 多样性=0.110
2025-07-16 23:19:33 - enhanced_ensemble_selector - INFO -   Logistic vs NaiveBayes: 相关性=0.832, 多样性=0.168
2025-07-16 23:19:33 - enhanced_ensemble_selector - INFO -   Logistic vs NeuralNet: 相关性=0.945, 多样性=0.055
2025-07-16 23:19:33 - enhanced_ensemble_selector - INFO -   SVM vs KNN: 相关性=0.942, 多样性=0.058
2025-07-16 23:19:33 - enhanced_ensemble_selector - INFO -   SVM vs NaiveBayes: 相关性=0.870, 多样性=0.130
2025-07-16 23:19:33 - enhanced_ensemble_selector - INFO -   SVM vs NeuralNet: 相关性=0.908, 多样性=0.092
2025-07-16 23:19:33 - enhanced_ensemble_selector - INFO -   KNN vs NaiveBayes: 相关性=0.959, 多样性=0.041
2025-07-16 23:19:33 - enhanced_ensemble_selector - INFO -   KNN vs NeuralNet: 相关性=0.866, 多样性=0.134
2025-07-16 23:19:33 - enhanced_ensemble_selector - INFO -   NaiveBayes vs NeuralNet: 相关性=0.796, 多样性=0.204
2025-07-16 23:19:33 - enhanced_ensemble_selector - INFO - 特征重要性多样性: 0.413
2025-07-16 23:19:33 - enhanced_ensemble_selector - INFO - 算法类型多样性: 1.000
2025-07-16 23:19:33 - enhanced_ensemble_selector - INFO - 算法类型分布: {'tree': 5, 'linear': 2, 'instance': 1, 'probabilistic': 1, 'neural': 1}
2025-07-16 23:19:33 - enhanced_ensemble_selector - INFO - 开始量化多样性评估...
2025-07-16 23:19:34 - enhanced_ensemble_selector - INFO - 量化多样性评估完成:
2025-07-16 23:19:34 - enhanced_ensemble_selector - INFO -   Q统计量平均多样性: 0.351
2025-07-16 23:19:34 - enhanced_ensemble_selector - INFO -   多样性变异系数: 0.424
2025-07-16 23:19:34 - enhanced_ensemble_selector - INFO -   熵多样性: 0.299
2025-07-16 23:19:34 - enhanced_ensemble_selector - INFO - 从 10 个合格模型中选择 3 个进行集成
2025-07-16 23:19:34 - enhanced_ensemble_selector - INFO - 最优组合: ['DecisionTree', 'NaiveBayes', 'NeuralNet']
2025-07-16 23:19:34 - enhanced_ensemble_selector - INFO - 综合得分: 0.7090
2025-07-16 23:20:13 - enhanced_ensemble_selector - INFO - 开始评估 10 个基模型...
2025-07-16 23:20:13 - enhanced_ensemble_selector - INFO -   评估模型: DecisionTree
2025-07-16 23:20:13 - enhanced_ensemble_selector - INFO -     DecisionTree - 性能得分: 0.8461
2025-07-16 23:20:13 - enhanced_ensemble_selector - INFO -   评估模型: RandomForest
2025-07-16 23:20:13 - enhanced_ensemble_selector - INFO -     RandomForest - 性能得分: 0.8439
2025-07-16 23:20:13 - enhanced_ensemble_selector - INFO -   评估模型: XGBoost
2025-07-16 23:20:13 - enhanced_ensemble_selector - INFO -     XGBoost - 性能得分: 0.7959
2025-07-16 23:20:13 - enhanced_ensemble_selector - INFO -   评估模型: LightGBM
2025-07-16 23:20:13 - enhanced_ensemble_selector - INFO -     LightGBM - 性能得分: 0.7759
2025-07-16 23:20:13 - enhanced_ensemble_selector - INFO -   评估模型: CatBoost
2025-07-16 23:20:14 - enhanced_ensemble_selector - INFO -     CatBoost - 性能得分: 0.8828
2025-07-16 23:20:14 - enhanced_ensemble_selector - INFO -   评估模型: Logistic
2025-07-16 23:20:14 - enhanced_ensemble_selector - INFO -     Logistic - 性能得分: 0.8449
2025-07-16 23:20:14 - enhanced_ensemble_selector - INFO -   评估模型: SVM
2025-07-16 23:20:14 - enhanced_ensemble_selector - INFO -     SVM - 性能得分: 0.8892
2025-07-16 23:20:14 - enhanced_ensemble_selector - INFO -   评估模型: KNN
2025-07-16 23:20:14 - enhanced_ensemble_selector - INFO -     KNN - 性能得分: 0.8978
2025-07-16 23:20:14 - enhanced_ensemble_selector - INFO -   评估模型: NaiveBayes
2025-07-16 23:20:14 - enhanced_ensemble_selector - INFO -     NaiveBayes - 性能得分: 0.8831
2025-07-16 23:20:14 - enhanced_ensemble_selector - INFO -   评估模型: NeuralNet
2025-07-16 23:20:15 - enhanced_ensemble_selector - INFO -     NeuralNet - 性能得分: 0.8664
2025-07-16 23:20:15 - enhanced_ensemble_selector - INFO - 使用改进的多样性分析完成
2025-07-16 23:20:15 - enhanced_ensemble_selector - INFO - 数据复杂度: 0.232
2025-07-16 23:20:15 - enhanced_ensemble_selector - INFO - 调整后相关性阈值: 0.600
2025-07-16 23:20:15 - enhanced_ensemble_selector - INFO - 综合多样性得分: 0.402
2025-07-16 23:20:15 - enhanced_ensemble_selector - INFO -   DecisionTree vs RandomForest: 相关性=0.866, 多样性=0.134
2025-07-16 23:20:15 - enhanced_ensemble_selector - INFO -   DecisionTree vs XGBoost: 相关性=0.800, 多样性=0.200
2025-07-16 23:20:15 - enhanced_ensemble_selector - INFO -   DecisionTree vs LightGBM: 相关性=0.789, 多样性=0.211
2025-07-16 23:20:15 - enhanced_ensemble_selector - INFO -   DecisionTree vs CatBoost: 相关性=0.848, 多样性=0.152
2025-07-16 23:20:15 - enhanced_ensemble_selector - INFO -   DecisionTree vs Logistic: 相关性=0.733, 多样性=0.267
2025-07-16 23:20:15 - enhanced_ensemble_selector - INFO -   DecisionTree vs SVM: 相关性=0.722, 多样性=0.278
2025-07-16 23:20:15 - enhanced_ensemble_selector - INFO -   DecisionTree vs KNN: 相关性=0.726, 多样性=0.274
2025-07-16 23:20:15 - enhanced_ensemble_selector - INFO -   DecisionTree vs NaiveBayes: 相关性=0.708, 多样性=0.292
2025-07-16 23:20:15 - enhanced_ensemble_selector - INFO -   DecisionTree vs NeuralNet: 相关性=0.733, 多样性=0.267
2025-07-16 23:20:15 - enhanced_ensemble_selector - INFO -   RandomForest vs XGBoost: 相关性=0.906, 多样性=0.094
2025-07-16 23:20:15 - enhanced_ensemble_selector - INFO -   RandomForest vs LightGBM: 相关性=0.918, 多样性=0.082
2025-07-16 23:20:15 - enhanced_ensemble_selector - INFO -   RandomForest vs CatBoost: 相关性=0.977, 多样性=0.023
2025-07-16 23:20:15 - enhanced_ensemble_selector - INFO -   RandomForest vs Logistic: 相关性=0.903, 多样性=0.097
2025-07-16 23:20:15 - enhanced_ensemble_selector - INFO -   RandomForest vs SVM: 相关性=0.885, 多样性=0.115
2025-07-16 23:20:15 - enhanced_ensemble_selector - INFO -   RandomForest vs KNN: 相关性=0.887, 多样性=0.113
2025-07-16 23:20:15 - enhanced_ensemble_selector - INFO -   RandomForest vs NaiveBayes: 相关性=0.810, 多样性=0.190
2025-07-16 23:20:15 - enhanced_ensemble_selector - INFO -   RandomForest vs NeuralNet: 相关性=0.865, 多样性=0.135
2025-07-16 23:20:15 - enhanced_ensemble_selector - INFO -   XGBoost vs LightGBM: 相关性=0.956, 多样性=0.044
2025-07-16 23:20:15 - enhanced_ensemble_selector - INFO -   XGBoost vs CatBoost: 相关性=0.911, 多样性=0.089
2025-07-16 23:20:15 - enhanced_ensemble_selector - INFO -   XGBoost vs Logistic: 相关性=0.848, 多样性=0.152
2025-07-16 23:20:15 - enhanced_ensemble_selector - INFO -   XGBoost vs SVM: 相关性=0.758, 多样性=0.242
2025-07-16 23:20:15 - enhanced_ensemble_selector - INFO -   XGBoost vs KNN: 相关性=0.775, 多样性=0.225
2025-07-16 23:20:15 - enhanced_ensemble_selector - INFO -   XGBoost vs NaiveBayes: 相关性=0.702, 多样性=0.298
2025-07-16 23:20:15 - enhanced_ensemble_selector - INFO -   XGBoost vs NeuralNet: 相关性=0.786, 多样性=0.214
2025-07-16 23:20:15 - enhanced_ensemble_selector - INFO -   LightGBM vs CatBoost: 相关性=0.939, 多样性=0.061
2025-07-16 23:20:15 - enhanced_ensemble_selector - INFO -   LightGBM vs Logistic: 相关性=0.834, 多样性=0.166
2025-07-16 23:20:15 - enhanced_ensemble_selector - INFO -   LightGBM vs SVM: 相关性=0.769, 多样性=0.231
2025-07-16 23:20:15 - enhanced_ensemble_selector - INFO -   LightGBM vs KNN: 相关性=0.778, 多样性=0.222
2025-07-16 23:20:15 - enhanced_ensemble_selector - INFO -   LightGBM vs NaiveBayes: 相关性=0.670, 多样性=0.330
2025-07-16 23:20:15 - enhanced_ensemble_selector - INFO -   LightGBM vs NeuralNet: 相关性=0.796, 多样性=0.204
2025-07-16 23:20:15 - enhanced_ensemble_selector - INFO -   CatBoost vs Logistic: 相关性=0.910, 多样性=0.090
2025-07-16 23:20:15 - enhanced_ensemble_selector - INFO -   CatBoost vs SVM: 相关性=0.896, 多样性=0.104
2025-07-16 23:20:15 - enhanced_ensemble_selector - INFO -   CatBoost vs KNN: 相关性=0.898, 多样性=0.102
2025-07-16 23:20:15 - enhanced_ensemble_selector - INFO -   CatBoost vs NaiveBayes: 相关性=0.826, 多样性=0.174
2025-07-16 23:20:15 - enhanced_ensemble_selector - INFO -   CatBoost vs NeuralNet: 相关性=0.904, 多样性=0.096
2025-07-16 23:20:15 - enhanced_ensemble_selector - INFO -   Logistic vs SVM: 相关性=0.901, 多样性=0.099
2025-07-16 23:20:15 - enhanced_ensemble_selector - INFO -   Logistic vs KNN: 相关性=0.890, 多样性=0.110
2025-07-16 23:20:15 - enhanced_ensemble_selector - INFO -   Logistic vs NaiveBayes: 相关性=0.832, 多样性=0.168
2025-07-16 23:20:15 - enhanced_ensemble_selector - INFO -   Logistic vs NeuralNet: 相关性=0.945, 多样性=0.055
2025-07-16 23:20:15 - enhanced_ensemble_selector - INFO -   SVM vs KNN: 相关性=0.942, 多样性=0.058
2025-07-16 23:20:15 - enhanced_ensemble_selector - INFO -   SVM vs NaiveBayes: 相关性=0.870, 多样性=0.130
2025-07-16 23:20:15 - enhanced_ensemble_selector - INFO -   SVM vs NeuralNet: 相关性=0.908, 多样性=0.092
2025-07-16 23:20:15 - enhanced_ensemble_selector - INFO -   KNN vs NaiveBayes: 相关性=0.959, 多样性=0.041
2025-07-16 23:20:15 - enhanced_ensemble_selector - INFO -   KNN vs NeuralNet: 相关性=0.866, 多样性=0.134
2025-07-16 23:20:15 - enhanced_ensemble_selector - INFO -   NaiveBayes vs NeuralNet: 相关性=0.796, 多样性=0.204
2025-07-16 23:20:15 - enhanced_ensemble_selector - INFO - 特征重要性多样性: 0.413
2025-07-16 23:20:15 - enhanced_ensemble_selector - INFO - 算法类型多样性: 1.000
2025-07-16 23:20:15 - enhanced_ensemble_selector - INFO - 算法类型分布: {'tree': 5, 'linear': 2, 'instance': 1, 'probabilistic': 1, 'neural': 1}
2025-07-16 23:20:15 - enhanced_ensemble_selector - INFO - 开始量化多样性评估...
2025-07-16 23:20:15 - enhanced_ensemble_selector - INFO - 量化多样性评估完成:
2025-07-16 23:20:15 - enhanced_ensemble_selector - INFO -   Q统计量平均多样性: 0.351
2025-07-16 23:20:15 - enhanced_ensemble_selector - INFO -   多样性变异系数: 0.424
2025-07-16 23:20:15 - enhanced_ensemble_selector - INFO -   熵多样性: 0.299
2025-07-16 23:20:15 - enhanced_ensemble_selector - INFO - 从 10 个合格模型中选择 4 个进行集成
2025-07-16 23:20:15 - enhanced_ensemble_selector - INFO - 最优组合: ['DecisionTree', 'SVM', 'NaiveBayes', 'NeuralNet']
2025-07-16 23:20:15 - enhanced_ensemble_selector - INFO - 综合得分: 0.6988
2025-07-16 23:20:22 - enhanced_ensemble_selector - INFO - 开始评估 10 个基模型...
2025-07-16 23:20:22 - enhanced_ensemble_selector - INFO -   评估模型: DecisionTree
2025-07-16 23:20:22 - enhanced_ensemble_selector - INFO -     DecisionTree - 性能得分: 0.8461
2025-07-16 23:20:22 - enhanced_ensemble_selector - INFO -   评估模型: RandomForest
2025-07-16 23:20:22 - enhanced_ensemble_selector - INFO -     RandomForest - 性能得分: 0.8439
2025-07-16 23:20:22 - enhanced_ensemble_selector - INFO -   评估模型: XGBoost
2025-07-16 23:20:23 - enhanced_ensemble_selector - INFO -     XGBoost - 性能得分: 0.7959
2025-07-16 23:20:23 - enhanced_ensemble_selector - INFO -   评估模型: LightGBM
2025-07-16 23:20:23 - enhanced_ensemble_selector - INFO -     LightGBM - 性能得分: 0.7759
2025-07-16 23:20:23 - enhanced_ensemble_selector - INFO -   评估模型: CatBoost
2025-07-16 23:20:24 - enhanced_ensemble_selector - INFO -     CatBoost - 性能得分: 0.8828
2025-07-16 23:20:24 - enhanced_ensemble_selector - INFO -   评估模型: Logistic
2025-07-16 23:20:24 - enhanced_ensemble_selector - INFO -     Logistic - 性能得分: 0.8449
2025-07-16 23:20:24 - enhanced_ensemble_selector - INFO -   评估模型: SVM
2025-07-16 23:20:24 - enhanced_ensemble_selector - INFO -     SVM - 性能得分: 0.8892
2025-07-16 23:20:24 - enhanced_ensemble_selector - INFO -   评估模型: KNN
2025-07-16 23:20:24 - enhanced_ensemble_selector - INFO -     KNN - 性能得分: 0.8978
2025-07-16 23:20:24 - enhanced_ensemble_selector - INFO -   评估模型: NaiveBayes
2025-07-16 23:20:24 - enhanced_ensemble_selector - INFO -     NaiveBayes - 性能得分: 0.8831
2025-07-16 23:20:24 - enhanced_ensemble_selector - INFO -   评估模型: NeuralNet
2025-07-16 23:20:24 - enhanced_ensemble_selector - INFO -     NeuralNet - 性能得分: 0.8664
2025-07-16 23:20:24 - enhanced_ensemble_selector - INFO - 使用改进的多样性分析完成
2025-07-16 23:20:24 - enhanced_ensemble_selector - INFO - 数据复杂度: 0.232
2025-07-16 23:20:24 - enhanced_ensemble_selector - INFO - 调整后相关性阈值: 0.600
2025-07-16 23:20:24 - enhanced_ensemble_selector - INFO - 综合多样性得分: 0.402
2025-07-16 23:20:24 - enhanced_ensemble_selector - INFO -   DecisionTree vs RandomForest: 相关性=0.866, 多样性=0.134
2025-07-16 23:20:24 - enhanced_ensemble_selector - INFO -   DecisionTree vs XGBoost: 相关性=0.800, 多样性=0.200
2025-07-16 23:20:24 - enhanced_ensemble_selector - INFO -   DecisionTree vs LightGBM: 相关性=0.789, 多样性=0.211
2025-07-16 23:20:24 - enhanced_ensemble_selector - INFO -   DecisionTree vs CatBoost: 相关性=0.848, 多样性=0.152
2025-07-16 23:20:24 - enhanced_ensemble_selector - INFO -   DecisionTree vs Logistic: 相关性=0.733, 多样性=0.267
2025-07-16 23:20:24 - enhanced_ensemble_selector - INFO -   DecisionTree vs SVM: 相关性=0.722, 多样性=0.278
2025-07-16 23:20:24 - enhanced_ensemble_selector - INFO -   DecisionTree vs KNN: 相关性=0.726, 多样性=0.274
2025-07-16 23:20:24 - enhanced_ensemble_selector - INFO -   DecisionTree vs NaiveBayes: 相关性=0.708, 多样性=0.292
2025-07-16 23:20:24 - enhanced_ensemble_selector - INFO -   DecisionTree vs NeuralNet: 相关性=0.733, 多样性=0.267
2025-07-16 23:20:24 - enhanced_ensemble_selector - INFO -   RandomForest vs XGBoost: 相关性=0.906, 多样性=0.094
2025-07-16 23:20:24 - enhanced_ensemble_selector - INFO -   RandomForest vs LightGBM: 相关性=0.918, 多样性=0.082
2025-07-16 23:20:24 - enhanced_ensemble_selector - INFO -   RandomForest vs CatBoost: 相关性=0.977, 多样性=0.023
2025-07-16 23:20:24 - enhanced_ensemble_selector - INFO -   RandomForest vs Logistic: 相关性=0.903, 多样性=0.097
2025-07-16 23:20:24 - enhanced_ensemble_selector - INFO -   RandomForest vs SVM: 相关性=0.885, 多样性=0.115
2025-07-16 23:20:24 - enhanced_ensemble_selector - INFO -   RandomForest vs KNN: 相关性=0.887, 多样性=0.113
2025-07-16 23:20:24 - enhanced_ensemble_selector - INFO -   RandomForest vs NaiveBayes: 相关性=0.810, 多样性=0.190
2025-07-16 23:20:24 - enhanced_ensemble_selector - INFO -   RandomForest vs NeuralNet: 相关性=0.865, 多样性=0.135
2025-07-16 23:20:24 - enhanced_ensemble_selector - INFO -   XGBoost vs LightGBM: 相关性=0.956, 多样性=0.044
2025-07-16 23:20:24 - enhanced_ensemble_selector - INFO -   XGBoost vs CatBoost: 相关性=0.911, 多样性=0.089
2025-07-16 23:20:24 - enhanced_ensemble_selector - INFO -   XGBoost vs Logistic: 相关性=0.848, 多样性=0.152
2025-07-16 23:20:24 - enhanced_ensemble_selector - INFO -   XGBoost vs SVM: 相关性=0.758, 多样性=0.242
2025-07-16 23:20:24 - enhanced_ensemble_selector - INFO -   XGBoost vs KNN: 相关性=0.775, 多样性=0.225
2025-07-16 23:20:24 - enhanced_ensemble_selector - INFO -   XGBoost vs NaiveBayes: 相关性=0.702, 多样性=0.298
2025-07-16 23:20:24 - enhanced_ensemble_selector - INFO -   XGBoost vs NeuralNet: 相关性=0.786, 多样性=0.214
2025-07-16 23:20:24 - enhanced_ensemble_selector - INFO -   LightGBM vs CatBoost: 相关性=0.939, 多样性=0.061
2025-07-16 23:20:24 - enhanced_ensemble_selector - INFO -   LightGBM vs Logistic: 相关性=0.834, 多样性=0.166
2025-07-16 23:20:24 - enhanced_ensemble_selector - INFO -   LightGBM vs SVM: 相关性=0.769, 多样性=0.231
2025-07-16 23:20:24 - enhanced_ensemble_selector - INFO -   LightGBM vs KNN: 相关性=0.778, 多样性=0.222
2025-07-16 23:20:24 - enhanced_ensemble_selector - INFO -   LightGBM vs NaiveBayes: 相关性=0.670, 多样性=0.330
2025-07-16 23:20:24 - enhanced_ensemble_selector - INFO -   LightGBM vs NeuralNet: 相关性=0.796, 多样性=0.204
2025-07-16 23:20:24 - enhanced_ensemble_selector - INFO -   CatBoost vs Logistic: 相关性=0.910, 多样性=0.090
2025-07-16 23:20:24 - enhanced_ensemble_selector - INFO -   CatBoost vs SVM: 相关性=0.896, 多样性=0.104
2025-07-16 23:20:24 - enhanced_ensemble_selector - INFO -   CatBoost vs KNN: 相关性=0.898, 多样性=0.102
2025-07-16 23:20:24 - enhanced_ensemble_selector - INFO -   CatBoost vs NaiveBayes: 相关性=0.826, 多样性=0.174
2025-07-16 23:20:24 - enhanced_ensemble_selector - INFO -   CatBoost vs NeuralNet: 相关性=0.904, 多样性=0.096
2025-07-16 23:20:24 - enhanced_ensemble_selector - INFO -   Logistic vs SVM: 相关性=0.901, 多样性=0.099
2025-07-16 23:20:24 - enhanced_ensemble_selector - INFO -   Logistic vs KNN: 相关性=0.890, 多样性=0.110
2025-07-16 23:20:24 - enhanced_ensemble_selector - INFO -   Logistic vs NaiveBayes: 相关性=0.832, 多样性=0.168
2025-07-16 23:20:24 - enhanced_ensemble_selector - INFO -   Logistic vs NeuralNet: 相关性=0.945, 多样性=0.055
2025-07-16 23:20:24 - enhanced_ensemble_selector - INFO -   SVM vs KNN: 相关性=0.942, 多样性=0.058
2025-07-16 23:20:24 - enhanced_ensemble_selector - INFO -   SVM vs NaiveBayes: 相关性=0.870, 多样性=0.130
2025-07-16 23:20:24 - enhanced_ensemble_selector - INFO -   SVM vs NeuralNet: 相关性=0.908, 多样性=0.092
2025-07-16 23:20:24 - enhanced_ensemble_selector - INFO -   KNN vs NaiveBayes: 相关性=0.959, 多样性=0.041
2025-07-16 23:20:24 - enhanced_ensemble_selector - INFO -   KNN vs NeuralNet: 相关性=0.866, 多样性=0.134
2025-07-16 23:20:24 - enhanced_ensemble_selector - INFO -   NaiveBayes vs NeuralNet: 相关性=0.796, 多样性=0.204
2025-07-16 23:20:24 - enhanced_ensemble_selector - INFO - 特征重要性多样性: 0.413
2025-07-16 23:20:24 - enhanced_ensemble_selector - INFO - 算法类型多样性: 1.000
2025-07-16 23:20:24 - enhanced_ensemble_selector - INFO - 算法类型分布: {'tree': 5, 'linear': 2, 'instance': 1, 'probabilistic': 1, 'neural': 1}
2025-07-16 23:20:24 - enhanced_ensemble_selector - INFO - 开始量化多样性评估...
2025-07-16 23:20:24 - enhanced_ensemble_selector - INFO - 量化多样性评估完成:
2025-07-16 23:20:24 - enhanced_ensemble_selector - INFO -   Q统计量平均多样性: 0.351
2025-07-16 23:20:24 - enhanced_ensemble_selector - INFO -   多样性变异系数: 0.424
2025-07-16 23:20:24 - enhanced_ensemble_selector - INFO -   熵多样性: 0.299
2025-07-16 23:20:24 - enhanced_ensemble_selector - INFO - 从 10 个合格模型中选择 5 个进行集成
2025-07-16 23:20:24 - enhanced_ensemble_selector - INFO - 最优组合: ['DecisionTree', 'SVM', 'KNN', 'NaiveBayes', 'NeuralNet']
2025-07-16 23:20:24 - enhanced_ensemble_selector - INFO - 综合得分: 0.6914
2025-07-16 23:20:40 - enhanced_ensemble_selector - INFO - 开始评估 10 个基模型...
2025-07-16 23:20:40 - enhanced_ensemble_selector - INFO -   评估模型: DecisionTree
2025-07-16 23:20:40 - enhanced_ensemble_selector - INFO -     DecisionTree - 性能得分: 0.8461
2025-07-16 23:20:40 - enhanced_ensemble_selector - INFO -   评估模型: RandomForest
2025-07-16 23:20:41 - enhanced_ensemble_selector - INFO -     RandomForest - 性能得分: 0.8439
2025-07-16 23:20:41 - enhanced_ensemble_selector - INFO -   评估模型: XGBoost
2025-07-16 23:20:41 - enhanced_ensemble_selector - INFO -     XGBoost - 性能得分: 0.7959
2025-07-16 23:20:41 - enhanced_ensemble_selector - INFO -   评估模型: LightGBM
2025-07-16 23:20:41 - enhanced_ensemble_selector - INFO -     LightGBM - 性能得分: 0.7759
2025-07-16 23:20:41 - enhanced_ensemble_selector - INFO -   评估模型: CatBoost
2025-07-16 23:20:42 - enhanced_ensemble_selector - INFO -     CatBoost - 性能得分: 0.8828
2025-07-16 23:20:42 - enhanced_ensemble_selector - INFO -   评估模型: Logistic
2025-07-16 23:20:42 - enhanced_ensemble_selector - INFO -     Logistic - 性能得分: 0.8449
2025-07-16 23:20:42 - enhanced_ensemble_selector - INFO -   评估模型: SVM
2025-07-16 23:20:42 - enhanced_ensemble_selector - INFO -     SVM - 性能得分: 0.8892
2025-07-16 23:20:42 - enhanced_ensemble_selector - INFO -   评估模型: KNN
2025-07-16 23:20:42 - enhanced_ensemble_selector - INFO -     KNN - 性能得分: 0.8978
2025-07-16 23:20:42 - enhanced_ensemble_selector - INFO -   评估模型: NaiveBayes
2025-07-16 23:20:42 - enhanced_ensemble_selector - INFO -     NaiveBayes - 性能得分: 0.8831
2025-07-16 23:20:42 - enhanced_ensemble_selector - INFO -   评估模型: NeuralNet
2025-07-16 23:20:42 - enhanced_ensemble_selector - INFO -     NeuralNet - 性能得分: 0.8664
2025-07-16 23:20:42 - enhanced_ensemble_selector - INFO - 使用改进的多样性分析完成
2025-07-16 23:20:42 - enhanced_ensemble_selector - INFO - 数据复杂度: 0.232
2025-07-16 23:20:42 - enhanced_ensemble_selector - INFO - 调整后相关性阈值: 0.600
2025-07-16 23:20:42 - enhanced_ensemble_selector - INFO - 综合多样性得分: 0.402
2025-07-16 23:20:42 - enhanced_ensemble_selector - INFO -   DecisionTree vs RandomForest: 相关性=0.866, 多样性=0.134
2025-07-16 23:20:42 - enhanced_ensemble_selector - INFO -   DecisionTree vs XGBoost: 相关性=0.800, 多样性=0.200
2025-07-16 23:20:42 - enhanced_ensemble_selector - INFO -   DecisionTree vs LightGBM: 相关性=0.789, 多样性=0.211
2025-07-16 23:20:42 - enhanced_ensemble_selector - INFO -   DecisionTree vs CatBoost: 相关性=0.848, 多样性=0.152
2025-07-16 23:20:42 - enhanced_ensemble_selector - INFO -   DecisionTree vs Logistic: 相关性=0.733, 多样性=0.267
2025-07-16 23:20:42 - enhanced_ensemble_selector - INFO -   DecisionTree vs SVM: 相关性=0.722, 多样性=0.278
2025-07-16 23:20:42 - enhanced_ensemble_selector - INFO -   DecisionTree vs KNN: 相关性=0.726, 多样性=0.274
2025-07-16 23:20:42 - enhanced_ensemble_selector - INFO -   DecisionTree vs NaiveBayes: 相关性=0.708, 多样性=0.292
2025-07-16 23:20:42 - enhanced_ensemble_selector - INFO -   DecisionTree vs NeuralNet: 相关性=0.733, 多样性=0.267
2025-07-16 23:20:42 - enhanced_ensemble_selector - INFO -   RandomForest vs XGBoost: 相关性=0.906, 多样性=0.094
2025-07-16 23:20:42 - enhanced_ensemble_selector - INFO -   RandomForest vs LightGBM: 相关性=0.918, 多样性=0.082
2025-07-16 23:20:42 - enhanced_ensemble_selector - INFO -   RandomForest vs CatBoost: 相关性=0.977, 多样性=0.023
2025-07-16 23:20:42 - enhanced_ensemble_selector - INFO -   RandomForest vs Logistic: 相关性=0.903, 多样性=0.097
2025-07-16 23:20:42 - enhanced_ensemble_selector - INFO -   RandomForest vs SVM: 相关性=0.885, 多样性=0.115
2025-07-16 23:20:42 - enhanced_ensemble_selector - INFO -   RandomForest vs KNN: 相关性=0.887, 多样性=0.113
2025-07-16 23:20:42 - enhanced_ensemble_selector - INFO -   RandomForest vs NaiveBayes: 相关性=0.810, 多样性=0.190
2025-07-16 23:20:42 - enhanced_ensemble_selector - INFO -   RandomForest vs NeuralNet: 相关性=0.865, 多样性=0.135
2025-07-16 23:20:42 - enhanced_ensemble_selector - INFO -   XGBoost vs LightGBM: 相关性=0.956, 多样性=0.044
2025-07-16 23:20:42 - enhanced_ensemble_selector - INFO -   XGBoost vs CatBoost: 相关性=0.911, 多样性=0.089
2025-07-16 23:20:42 - enhanced_ensemble_selector - INFO -   XGBoost vs Logistic: 相关性=0.848, 多样性=0.152
2025-07-16 23:20:42 - enhanced_ensemble_selector - INFO -   XGBoost vs SVM: 相关性=0.758, 多样性=0.242
2025-07-16 23:20:42 - enhanced_ensemble_selector - INFO -   XGBoost vs KNN: 相关性=0.775, 多样性=0.225
2025-07-16 23:20:42 - enhanced_ensemble_selector - INFO -   XGBoost vs NaiveBayes: 相关性=0.702, 多样性=0.298
2025-07-16 23:20:42 - enhanced_ensemble_selector - INFO -   XGBoost vs NeuralNet: 相关性=0.786, 多样性=0.214
2025-07-16 23:20:42 - enhanced_ensemble_selector - INFO -   LightGBM vs CatBoost: 相关性=0.939, 多样性=0.061
2025-07-16 23:20:42 - enhanced_ensemble_selector - INFO -   LightGBM vs Logistic: 相关性=0.834, 多样性=0.166
2025-07-16 23:20:42 - enhanced_ensemble_selector - INFO -   LightGBM vs SVM: 相关性=0.769, 多样性=0.231
2025-07-16 23:20:42 - enhanced_ensemble_selector - INFO -   LightGBM vs KNN: 相关性=0.778, 多样性=0.222
2025-07-16 23:20:42 - enhanced_ensemble_selector - INFO -   LightGBM vs NaiveBayes: 相关性=0.670, 多样性=0.330
2025-07-16 23:20:42 - enhanced_ensemble_selector - INFO -   LightGBM vs NeuralNet: 相关性=0.796, 多样性=0.204
2025-07-16 23:20:42 - enhanced_ensemble_selector - INFO -   CatBoost vs Logistic: 相关性=0.910, 多样性=0.090
2025-07-16 23:20:42 - enhanced_ensemble_selector - INFO -   CatBoost vs SVM: 相关性=0.896, 多样性=0.104
2025-07-16 23:20:42 - enhanced_ensemble_selector - INFO -   CatBoost vs KNN: 相关性=0.898, 多样性=0.102
2025-07-16 23:20:42 - enhanced_ensemble_selector - INFO -   CatBoost vs NaiveBayes: 相关性=0.826, 多样性=0.174
2025-07-16 23:20:42 - enhanced_ensemble_selector - INFO -   CatBoost vs NeuralNet: 相关性=0.904, 多样性=0.096
2025-07-16 23:20:42 - enhanced_ensemble_selector - INFO -   Logistic vs SVM: 相关性=0.901, 多样性=0.099
2025-07-16 23:20:42 - enhanced_ensemble_selector - INFO -   Logistic vs KNN: 相关性=0.890, 多样性=0.110
2025-07-16 23:20:42 - enhanced_ensemble_selector - INFO -   Logistic vs NaiveBayes: 相关性=0.832, 多样性=0.168
2025-07-16 23:20:42 - enhanced_ensemble_selector - INFO -   Logistic vs NeuralNet: 相关性=0.945, 多样性=0.055
2025-07-16 23:20:42 - enhanced_ensemble_selector - INFO -   SVM vs KNN: 相关性=0.942, 多样性=0.058
2025-07-16 23:20:42 - enhanced_ensemble_selector - INFO -   SVM vs NaiveBayes: 相关性=0.870, 多样性=0.130
2025-07-16 23:20:42 - enhanced_ensemble_selector - INFO -   SVM vs NeuralNet: 相关性=0.908, 多样性=0.092
2025-07-16 23:20:42 - enhanced_ensemble_selector - INFO -   KNN vs NaiveBayes: 相关性=0.959, 多样性=0.041
2025-07-16 23:20:42 - enhanced_ensemble_selector - INFO -   KNN vs NeuralNet: 相关性=0.866, 多样性=0.134
2025-07-16 23:20:42 - enhanced_ensemble_selector - INFO -   NaiveBayes vs NeuralNet: 相关性=0.796, 多样性=0.204
2025-07-16 23:20:42 - enhanced_ensemble_selector - INFO - 特征重要性多样性: 0.413
2025-07-16 23:20:42 - enhanced_ensemble_selector - INFO - 算法类型多样性: 1.000
2025-07-16 23:20:42 - enhanced_ensemble_selector - INFO - 算法类型分布: {'tree': 5, 'linear': 2, 'instance': 1, 'probabilistic': 1, 'neural': 1}
2025-07-16 23:20:42 - enhanced_ensemble_selector - INFO - 开始量化多样性评估...
2025-07-16 23:20:42 - enhanced_ensemble_selector - INFO - 量化多样性评估完成:
2025-07-16 23:20:42 - enhanced_ensemble_selector - INFO -   Q统计量平均多样性: 0.351
2025-07-16 23:20:42 - enhanced_ensemble_selector - INFO -   多样性变异系数: 0.424
2025-07-16 23:20:42 - enhanced_ensemble_selector - INFO -   熵多样性: 0.299
2025-07-16 23:20:42 - enhanced_ensemble_selector - INFO - 从 10 个合格模型中选择 2 个进行集成
2025-07-16 23:20:42 - enhanced_ensemble_selector - INFO - 最优组合: ['DecisionTree', 'NaiveBayes']
2025-07-16 23:20:42 - enhanced_ensemble_selector - INFO - 综合得分: 0.7200
2025-07-16 23:20:50 - enhanced_ensemble_selector - INFO - 开始评估 10 个基模型...
2025-07-16 23:20:50 - enhanced_ensemble_selector - INFO -   评估模型: DecisionTree
2025-07-16 23:20:50 - enhanced_ensemble_selector - INFO -     DecisionTree - 性能得分: 0.8461
2025-07-16 23:20:50 - enhanced_ensemble_selector - INFO -   评估模型: RandomForest
2025-07-16 23:20:50 - enhanced_ensemble_selector - INFO -     RandomForest - 性能得分: 0.8439
2025-07-16 23:20:50 - enhanced_ensemble_selector - INFO -   评估模型: XGBoost
2025-07-16 23:20:50 - enhanced_ensemble_selector - INFO -     XGBoost - 性能得分: 0.7959
2025-07-16 23:20:50 - enhanced_ensemble_selector - INFO -   评估模型: LightGBM
2025-07-16 23:20:50 - enhanced_ensemble_selector - INFO -     LightGBM - 性能得分: 0.7759
2025-07-16 23:20:50 - enhanced_ensemble_selector - INFO -   评估模型: CatBoost
2025-07-16 23:20:51 - enhanced_ensemble_selector - INFO -     CatBoost - 性能得分: 0.8828
2025-07-16 23:20:51 - enhanced_ensemble_selector - INFO -   评估模型: Logistic
2025-07-16 23:20:51 - enhanced_ensemble_selector - INFO -     Logistic - 性能得分: 0.8449
2025-07-16 23:20:51 - enhanced_ensemble_selector - INFO -   评估模型: SVM
2025-07-16 23:20:51 - enhanced_ensemble_selector - INFO -     SVM - 性能得分: 0.8892
2025-07-16 23:20:51 - enhanced_ensemble_selector - INFO -   评估模型: KNN
2025-07-16 23:20:52 - enhanced_ensemble_selector - INFO -     KNN - 性能得分: 0.8978
2025-07-16 23:20:52 - enhanced_ensemble_selector - INFO -   评估模型: NaiveBayes
2025-07-16 23:20:52 - enhanced_ensemble_selector - INFO -     NaiveBayes - 性能得分: 0.8831
2025-07-16 23:20:52 - enhanced_ensemble_selector - INFO -   评估模型: NeuralNet
2025-07-16 23:20:52 - enhanced_ensemble_selector - INFO -     NeuralNet - 性能得分: 0.8664
2025-07-16 23:20:52 - enhanced_ensemble_selector - INFO - 使用改进的多样性分析完成
2025-07-16 23:20:52 - enhanced_ensemble_selector - INFO - 数据复杂度: 0.232
2025-07-16 23:20:52 - enhanced_ensemble_selector - INFO - 调整后相关性阈值: 0.600
2025-07-16 23:20:52 - enhanced_ensemble_selector - INFO - 综合多样性得分: 0.402
2025-07-16 23:20:52 - enhanced_ensemble_selector - INFO -   DecisionTree vs RandomForest: 相关性=0.866, 多样性=0.134
2025-07-16 23:20:52 - enhanced_ensemble_selector - INFO -   DecisionTree vs XGBoost: 相关性=0.800, 多样性=0.200
2025-07-16 23:20:52 - enhanced_ensemble_selector - INFO -   DecisionTree vs LightGBM: 相关性=0.789, 多样性=0.211
2025-07-16 23:20:52 - enhanced_ensemble_selector - INFO -   DecisionTree vs CatBoost: 相关性=0.848, 多样性=0.152
2025-07-16 23:20:52 - enhanced_ensemble_selector - INFO -   DecisionTree vs Logistic: 相关性=0.733, 多样性=0.267
2025-07-16 23:20:52 - enhanced_ensemble_selector - INFO -   DecisionTree vs SVM: 相关性=0.722, 多样性=0.278
2025-07-16 23:20:52 - enhanced_ensemble_selector - INFO -   DecisionTree vs KNN: 相关性=0.726, 多样性=0.274
2025-07-16 23:20:52 - enhanced_ensemble_selector - INFO -   DecisionTree vs NaiveBayes: 相关性=0.708, 多样性=0.292
2025-07-16 23:20:52 - enhanced_ensemble_selector - INFO -   DecisionTree vs NeuralNet: 相关性=0.733, 多样性=0.267
2025-07-16 23:20:52 - enhanced_ensemble_selector - INFO -   RandomForest vs XGBoost: 相关性=0.906, 多样性=0.094
2025-07-16 23:20:52 - enhanced_ensemble_selector - INFO -   RandomForest vs LightGBM: 相关性=0.918, 多样性=0.082
2025-07-16 23:20:52 - enhanced_ensemble_selector - INFO -   RandomForest vs CatBoost: 相关性=0.977, 多样性=0.023
2025-07-16 23:20:52 - enhanced_ensemble_selector - INFO -   RandomForest vs Logistic: 相关性=0.903, 多样性=0.097
2025-07-16 23:20:52 - enhanced_ensemble_selector - INFO -   RandomForest vs SVM: 相关性=0.885, 多样性=0.115
2025-07-16 23:20:52 - enhanced_ensemble_selector - INFO -   RandomForest vs KNN: 相关性=0.887, 多样性=0.113
2025-07-16 23:20:52 - enhanced_ensemble_selector - INFO -   RandomForest vs NaiveBayes: 相关性=0.810, 多样性=0.190
2025-07-16 23:20:52 - enhanced_ensemble_selector - INFO -   RandomForest vs NeuralNet: 相关性=0.865, 多样性=0.135
2025-07-16 23:20:52 - enhanced_ensemble_selector - INFO -   XGBoost vs LightGBM: 相关性=0.956, 多样性=0.044
2025-07-16 23:20:52 - enhanced_ensemble_selector - INFO -   XGBoost vs CatBoost: 相关性=0.911, 多样性=0.089
2025-07-16 23:20:52 - enhanced_ensemble_selector - INFO -   XGBoost vs Logistic: 相关性=0.848, 多样性=0.152
2025-07-16 23:20:52 - enhanced_ensemble_selector - INFO -   XGBoost vs SVM: 相关性=0.758, 多样性=0.242
2025-07-16 23:20:52 - enhanced_ensemble_selector - INFO -   XGBoost vs KNN: 相关性=0.775, 多样性=0.225
2025-07-16 23:20:52 - enhanced_ensemble_selector - INFO -   XGBoost vs NaiveBayes: 相关性=0.702, 多样性=0.298
2025-07-16 23:20:52 - enhanced_ensemble_selector - INFO -   XGBoost vs NeuralNet: 相关性=0.786, 多样性=0.214
2025-07-16 23:20:52 - enhanced_ensemble_selector - INFO -   LightGBM vs CatBoost: 相关性=0.939, 多样性=0.061
2025-07-16 23:20:52 - enhanced_ensemble_selector - INFO -   LightGBM vs Logistic: 相关性=0.834, 多样性=0.166
2025-07-16 23:20:52 - enhanced_ensemble_selector - INFO -   LightGBM vs SVM: 相关性=0.769, 多样性=0.231
2025-07-16 23:20:52 - enhanced_ensemble_selector - INFO -   LightGBM vs KNN: 相关性=0.778, 多样性=0.222
2025-07-16 23:20:52 - enhanced_ensemble_selector - INFO -   LightGBM vs NaiveBayes: 相关性=0.670, 多样性=0.330
2025-07-16 23:20:52 - enhanced_ensemble_selector - INFO -   LightGBM vs NeuralNet: 相关性=0.796, 多样性=0.204
2025-07-16 23:20:52 - enhanced_ensemble_selector - INFO -   CatBoost vs Logistic: 相关性=0.910, 多样性=0.090
2025-07-16 23:20:52 - enhanced_ensemble_selector - INFO -   CatBoost vs SVM: 相关性=0.896, 多样性=0.104
2025-07-16 23:20:52 - enhanced_ensemble_selector - INFO -   CatBoost vs KNN: 相关性=0.898, 多样性=0.102
2025-07-16 23:20:52 - enhanced_ensemble_selector - INFO -   CatBoost vs NaiveBayes: 相关性=0.826, 多样性=0.174
2025-07-16 23:20:52 - enhanced_ensemble_selector - INFO -   CatBoost vs NeuralNet: 相关性=0.904, 多样性=0.096
2025-07-16 23:20:52 - enhanced_ensemble_selector - INFO -   Logistic vs SVM: 相关性=0.901, 多样性=0.099
2025-07-16 23:20:52 - enhanced_ensemble_selector - INFO -   Logistic vs KNN: 相关性=0.890, 多样性=0.110
2025-07-16 23:20:52 - enhanced_ensemble_selector - INFO -   Logistic vs NaiveBayes: 相关性=0.832, 多样性=0.168
2025-07-16 23:20:52 - enhanced_ensemble_selector - INFO -   Logistic vs NeuralNet: 相关性=0.945, 多样性=0.055
2025-07-16 23:20:52 - enhanced_ensemble_selector - INFO -   SVM vs KNN: 相关性=0.942, 多样性=0.058
2025-07-16 23:20:52 - enhanced_ensemble_selector - INFO -   SVM vs NaiveBayes: 相关性=0.870, 多样性=0.130
2025-07-16 23:20:52 - enhanced_ensemble_selector - INFO -   SVM vs NeuralNet: 相关性=0.908, 多样性=0.092
2025-07-16 23:20:52 - enhanced_ensemble_selector - INFO -   KNN vs NaiveBayes: 相关性=0.959, 多样性=0.041
2025-07-16 23:20:52 - enhanced_ensemble_selector - INFO -   KNN vs NeuralNet: 相关性=0.866, 多样性=0.134
2025-07-16 23:20:52 - enhanced_ensemble_selector - INFO -   NaiveBayes vs NeuralNet: 相关性=0.796, 多样性=0.204
2025-07-16 23:20:52 - enhanced_ensemble_selector - INFO - 特征重要性多样性: 0.413
2025-07-16 23:20:52 - enhanced_ensemble_selector - INFO - 算法类型多样性: 1.000
2025-07-16 23:20:52 - enhanced_ensemble_selector - INFO - 算法类型分布: {'tree': 5, 'linear': 2, 'instance': 1, 'probabilistic': 1, 'neural': 1}
2025-07-16 23:20:52 - enhanced_ensemble_selector - INFO - 开始量化多样性评估...
2025-07-16 23:20:52 - enhanced_ensemble_selector - INFO - 量化多样性评估完成:
2025-07-16 23:20:52 - enhanced_ensemble_selector - INFO -   Q统计量平均多样性: 0.351
2025-07-16 23:20:52 - enhanced_ensemble_selector - INFO -   多样性变异系数: 0.424
2025-07-16 23:20:52 - enhanced_ensemble_selector - INFO -   熵多样性: 0.299
2025-07-16 23:20:52 - enhanced_ensemble_selector - INFO - 从 10 个合格模型中选择 3 个进行集成
2025-07-16 23:20:52 - enhanced_ensemble_selector - INFO - 最优组合: ['DecisionTree', 'NaiveBayes', 'NeuralNet']
2025-07-16 23:20:52 - enhanced_ensemble_selector - INFO - 综合得分: 0.7090
2025-07-16 23:21:27 - enhanced_ensemble_selector - INFO - 开始评估 10 个基模型...
2025-07-16 23:21:27 - enhanced_ensemble_selector - INFO -   评估模型: DecisionTree
2025-07-16 23:21:28 - enhanced_ensemble_selector - INFO -     DecisionTree - 性能得分: 0.8461
2025-07-16 23:21:28 - enhanced_ensemble_selector - INFO -   评估模型: RandomForest
2025-07-16 23:21:28 - enhanced_ensemble_selector - INFO -     RandomForest - 性能得分: 0.8439
2025-07-16 23:21:28 - enhanced_ensemble_selector - INFO -   评估模型: XGBoost
2025-07-16 23:21:28 - enhanced_ensemble_selector - INFO -     XGBoost - 性能得分: 0.7959
2025-07-16 23:21:28 - enhanced_ensemble_selector - INFO -   评估模型: LightGBM
2025-07-16 23:21:28 - enhanced_ensemble_selector - INFO -     LightGBM - 性能得分: 0.7759
2025-07-16 23:21:28 - enhanced_ensemble_selector - INFO -   评估模型: CatBoost
2025-07-16 23:21:29 - enhanced_ensemble_selector - INFO -     CatBoost - 性能得分: 0.8828
2025-07-16 23:21:29 - enhanced_ensemble_selector - INFO -   评估模型: Logistic
2025-07-16 23:21:29 - enhanced_ensemble_selector - INFO -     Logistic - 性能得分: 0.8449
2025-07-16 23:21:29 - enhanced_ensemble_selector - INFO -   评估模型: SVM
2025-07-16 23:21:29 - enhanced_ensemble_selector - INFO -     SVM - 性能得分: 0.8892
2025-07-16 23:21:29 - enhanced_ensemble_selector - INFO -   评估模型: KNN
2025-07-16 23:21:29 - enhanced_ensemble_selector - INFO -     KNN - 性能得分: 0.8978
2025-07-16 23:21:29 - enhanced_ensemble_selector - INFO -   评估模型: NaiveBayes
2025-07-16 23:21:29 - enhanced_ensemble_selector - INFO -     NaiveBayes - 性能得分: 0.8831
2025-07-16 23:21:29 - enhanced_ensemble_selector - INFO -   评估模型: NeuralNet
2025-07-16 23:21:29 - enhanced_ensemble_selector - INFO -     NeuralNet - 性能得分: 0.8664
2025-07-16 23:21:29 - enhanced_ensemble_selector - INFO - 使用改进的多样性分析完成
2025-07-16 23:21:29 - enhanced_ensemble_selector - INFO - 数据复杂度: 0.232
2025-07-16 23:21:29 - enhanced_ensemble_selector - INFO - 调整后相关性阈值: 0.600
2025-07-16 23:21:29 - enhanced_ensemble_selector - INFO - 综合多样性得分: 0.402
2025-07-16 23:21:29 - enhanced_ensemble_selector - INFO -   DecisionTree vs RandomForest: 相关性=0.866, 多样性=0.134
2025-07-16 23:21:29 - enhanced_ensemble_selector - INFO -   DecisionTree vs XGBoost: 相关性=0.800, 多样性=0.200
2025-07-16 23:21:29 - enhanced_ensemble_selector - INFO -   DecisionTree vs LightGBM: 相关性=0.789, 多样性=0.211
2025-07-16 23:21:29 - enhanced_ensemble_selector - INFO -   DecisionTree vs CatBoost: 相关性=0.848, 多样性=0.152
2025-07-16 23:21:29 - enhanced_ensemble_selector - INFO -   DecisionTree vs Logistic: 相关性=0.733, 多样性=0.267
2025-07-16 23:21:29 - enhanced_ensemble_selector - INFO -   DecisionTree vs SVM: 相关性=0.722, 多样性=0.278
2025-07-16 23:21:29 - enhanced_ensemble_selector - INFO -   DecisionTree vs KNN: 相关性=0.726, 多样性=0.274
2025-07-16 23:21:29 - enhanced_ensemble_selector - INFO -   DecisionTree vs NaiveBayes: 相关性=0.708, 多样性=0.292
2025-07-16 23:21:29 - enhanced_ensemble_selector - INFO -   DecisionTree vs NeuralNet: 相关性=0.733, 多样性=0.267
2025-07-16 23:21:29 - enhanced_ensemble_selector - INFO -   RandomForest vs XGBoost: 相关性=0.906, 多样性=0.094
2025-07-16 23:21:29 - enhanced_ensemble_selector - INFO -   RandomForest vs LightGBM: 相关性=0.918, 多样性=0.082
2025-07-16 23:21:29 - enhanced_ensemble_selector - INFO -   RandomForest vs CatBoost: 相关性=0.977, 多样性=0.023
2025-07-16 23:21:29 - enhanced_ensemble_selector - INFO -   RandomForest vs Logistic: 相关性=0.903, 多样性=0.097
2025-07-16 23:21:29 - enhanced_ensemble_selector - INFO -   RandomForest vs SVM: 相关性=0.885, 多样性=0.115
2025-07-16 23:21:29 - enhanced_ensemble_selector - INFO -   RandomForest vs KNN: 相关性=0.887, 多样性=0.113
2025-07-16 23:21:29 - enhanced_ensemble_selector - INFO -   RandomForest vs NaiveBayes: 相关性=0.810, 多样性=0.190
2025-07-16 23:21:29 - enhanced_ensemble_selector - INFO -   RandomForest vs NeuralNet: 相关性=0.865, 多样性=0.135
2025-07-16 23:21:29 - enhanced_ensemble_selector - INFO -   XGBoost vs LightGBM: 相关性=0.956, 多样性=0.044
2025-07-16 23:21:29 - enhanced_ensemble_selector - INFO -   XGBoost vs CatBoost: 相关性=0.911, 多样性=0.089
2025-07-16 23:21:29 - enhanced_ensemble_selector - INFO -   XGBoost vs Logistic: 相关性=0.848, 多样性=0.152
2025-07-16 23:21:29 - enhanced_ensemble_selector - INFO -   XGBoost vs SVM: 相关性=0.758, 多样性=0.242
2025-07-16 23:21:29 - enhanced_ensemble_selector - INFO -   XGBoost vs KNN: 相关性=0.775, 多样性=0.225
2025-07-16 23:21:29 - enhanced_ensemble_selector - INFO -   XGBoost vs NaiveBayes: 相关性=0.702, 多样性=0.298
2025-07-16 23:21:29 - enhanced_ensemble_selector - INFO -   XGBoost vs NeuralNet: 相关性=0.786, 多样性=0.214
2025-07-16 23:21:29 - enhanced_ensemble_selector - INFO -   LightGBM vs CatBoost: 相关性=0.939, 多样性=0.061
2025-07-16 23:21:29 - enhanced_ensemble_selector - INFO -   LightGBM vs Logistic: 相关性=0.834, 多样性=0.166
2025-07-16 23:21:29 - enhanced_ensemble_selector - INFO -   LightGBM vs SVM: 相关性=0.769, 多样性=0.231
2025-07-16 23:21:29 - enhanced_ensemble_selector - INFO -   LightGBM vs KNN: 相关性=0.778, 多样性=0.222
2025-07-16 23:21:29 - enhanced_ensemble_selector - INFO -   LightGBM vs NaiveBayes: 相关性=0.670, 多样性=0.330
2025-07-16 23:21:29 - enhanced_ensemble_selector - INFO -   LightGBM vs NeuralNet: 相关性=0.796, 多样性=0.204
2025-07-16 23:21:29 - enhanced_ensemble_selector - INFO -   CatBoost vs Logistic: 相关性=0.910, 多样性=0.090
2025-07-16 23:21:29 - enhanced_ensemble_selector - INFO -   CatBoost vs SVM: 相关性=0.896, 多样性=0.104
2025-07-16 23:21:29 - enhanced_ensemble_selector - INFO -   CatBoost vs KNN: 相关性=0.898, 多样性=0.102
2025-07-16 23:21:29 - enhanced_ensemble_selector - INFO -   CatBoost vs NaiveBayes: 相关性=0.826, 多样性=0.174
2025-07-16 23:21:29 - enhanced_ensemble_selector - INFO -   CatBoost vs NeuralNet: 相关性=0.904, 多样性=0.096
2025-07-16 23:21:29 - enhanced_ensemble_selector - INFO -   Logistic vs SVM: 相关性=0.901, 多样性=0.099
2025-07-16 23:21:29 - enhanced_ensemble_selector - INFO -   Logistic vs KNN: 相关性=0.890, 多样性=0.110
2025-07-16 23:21:29 - enhanced_ensemble_selector - INFO -   Logistic vs NaiveBayes: 相关性=0.832, 多样性=0.168
2025-07-16 23:21:29 - enhanced_ensemble_selector - INFO -   Logistic vs NeuralNet: 相关性=0.945, 多样性=0.055
2025-07-16 23:21:29 - enhanced_ensemble_selector - INFO -   SVM vs KNN: 相关性=0.942, 多样性=0.058
2025-07-16 23:21:29 - enhanced_ensemble_selector - INFO -   SVM vs NaiveBayes: 相关性=0.870, 多样性=0.130
2025-07-16 23:21:29 - enhanced_ensemble_selector - INFO -   SVM vs NeuralNet: 相关性=0.908, 多样性=0.092
2025-07-16 23:21:29 - enhanced_ensemble_selector - INFO -   KNN vs NaiveBayes: 相关性=0.959, 多样性=0.041
2025-07-16 23:21:29 - enhanced_ensemble_selector - INFO -   KNN vs NeuralNet: 相关性=0.866, 多样性=0.134
2025-07-16 23:21:29 - enhanced_ensemble_selector - INFO -   NaiveBayes vs NeuralNet: 相关性=0.796, 多样性=0.204
2025-07-16 23:21:29 - enhanced_ensemble_selector - INFO - 特征重要性多样性: 0.413
2025-07-16 23:21:29 - enhanced_ensemble_selector - INFO - 算法类型多样性: 1.000
2025-07-16 23:21:29 - enhanced_ensemble_selector - INFO - 算法类型分布: {'tree': 5, 'linear': 2, 'instance': 1, 'probabilistic': 1, 'neural': 1}
2025-07-16 23:21:29 - enhanced_ensemble_selector - INFO - 开始量化多样性评估...
2025-07-16 23:21:30 - enhanced_ensemble_selector - INFO - 量化多样性评估完成:
2025-07-16 23:21:30 - enhanced_ensemble_selector - INFO -   Q统计量平均多样性: 0.351
2025-07-16 23:21:30 - enhanced_ensemble_selector - INFO -   多样性变异系数: 0.424
2025-07-16 23:21:30 - enhanced_ensemble_selector - INFO -   熵多样性: 0.299
2025-07-16 23:21:30 - enhanced_ensemble_selector - INFO - 从 10 个合格模型中选择 3 个进行集成
2025-07-16 23:21:30 - enhanced_ensemble_selector - INFO - 使用量化多样性评估进行模型选择...
2025-07-16 23:21:30 - enhanced_ensemble_selector - INFO - 量化评估选择完成:
2025-07-16 23:21:30 - enhanced_ensemble_selector - INFO -   最优组合: ['DecisionTree', 'LightGBM', 'NeuralNet']
2025-07-16 23:21:30 - enhanced_ensemble_selector - INFO -   性能得分: 0.8295
2025-07-16 23:21:30 - enhanced_ensemble_selector - INFO -   多样性得分: 0.5795
2025-07-16 23:21:30 - enhanced_ensemble_selector - INFO -   综合得分: 0.7045
2025-07-16 23:21:30 - enhanced_ensemble_selector - INFO -   多样性等级: 良好的多样性
2025-07-16 23:26:32 - enhanced_ensemble_selector - INFO - 开始评估 5 个基模型...
2025-07-16 23:26:32 - enhanced_ensemble_selector - INFO -   评估模型: DecisionTree
2025-07-16 23:26:32 - enhanced_ensemble_selector - INFO -     DecisionTree - 性能得分: 0.7880
2025-07-16 23:26:32 - enhanced_ensemble_selector - INFO -   评估模型: RandomForest
2025-07-16 23:26:33 - enhanced_ensemble_selector - INFO -     RandomForest - 性能得分: 0.8757
2025-07-16 23:26:33 - enhanced_ensemble_selector - INFO -   评估模型: Logistic
2025-07-16 23:26:33 - enhanced_ensemble_selector - INFO -     Logistic - 性能得分: 0.8155
2025-07-16 23:26:33 - enhanced_ensemble_selector - INFO -   评估模型: SVM
2025-07-16 23:26:33 - enhanced_ensemble_selector - INFO -     SVM - 性能得分: 0.8821
2025-07-16 23:26:33 - enhanced_ensemble_selector - INFO -   评估模型: XGBoost
2025-07-16 23:26:33 - enhanced_ensemble_selector - INFO -     XGBoost - 性能得分: 0.8683
2025-07-16 23:26:33 - enhanced_ensemble_selector - INFO - 使用改进的多样性分析完成
2025-07-16 23:26:33 - enhanced_ensemble_selector - INFO - 数据复杂度: 0.126
2025-07-16 23:26:33 - enhanced_ensemble_selector - INFO - 调整后相关性阈值: 0.600
2025-07-16 23:26:33 - enhanced_ensemble_selector - INFO - 综合多样性得分: 0.213
2025-07-16 23:26:33 - enhanced_ensemble_selector - INFO -   DecisionTree vs RandomForest: 相关性=0.768, 多样性=0.232
2025-07-16 23:26:33 - enhanced_ensemble_selector - INFO -   DecisionTree vs Logistic: 相关性=0.711, 多样性=0.289
2025-07-16 23:26:33 - enhanced_ensemble_selector - INFO -   DecisionTree vs SVM: 相关性=0.695, 多样性=0.305
2025-07-16 23:26:33 - enhanced_ensemble_selector - INFO -   DecisionTree vs XGBoost: 相关性=0.741, 多样性=0.259
2025-07-16 23:26:33 - enhanced_ensemble_selector - INFO -   RandomForest vs Logistic: 相关性=0.892, 多样性=0.108
2025-07-16 23:26:33 - enhanced_ensemble_selector - INFO -   RandomForest vs SVM: 相关性=0.909, 多样性=0.091
2025-07-16 23:26:33 - enhanced_ensemble_selector - INFO -   RandomForest vs XGBoost: 相关性=0.925, 多样性=0.075
2025-07-16 23:26:33 - enhanced_ensemble_selector - INFO -   Logistic vs SVM: 相关性=0.857, 多样性=0.143
2025-07-16 23:26:33 - enhanced_ensemble_selector - INFO -   Logistic vs XGBoost: 相关性=0.864, 多样性=0.136
2025-07-16 23:26:33 - enhanced_ensemble_selector - INFO -   SVM vs XGBoost: 相关性=0.937, 多样性=0.063
2025-07-16 23:26:33 - enhanced_ensemble_selector - INFO - 特征重要性多样性: 0.225
2025-07-16 23:26:33 - enhanced_ensemble_selector - INFO - 算法类型多样性: 0.300
2025-07-16 23:26:33 - enhanced_ensemble_selector - INFO - 算法类型分布: {'tree': 3, 'linear': 2}
2025-07-16 23:26:33 - enhanced_ensemble_selector - INFO - 开始量化多样性评估...
2025-07-16 23:26:33 - enhanced_ensemble_selector - INFO - 量化多样性评估完成:
2025-07-16 23:26:33 - enhanced_ensemble_selector - INFO -   Q统计量平均多样性: 0.313
2025-07-16 23:26:33 - enhanced_ensemble_selector - INFO -   多样性变异系数: 0.205
2025-07-16 23:26:33 - enhanced_ensemble_selector - INFO -   熵多样性: 0.241
2025-07-16 23:26:33 - enhanced_ensemble_selector - INFO - 从 5 个合格模型中选择 3 个进行集成
2025-07-16 23:26:33 - enhanced_ensemble_selector - INFO - 使用量化多样性评估进行模型选择...
2025-07-16 23:26:33 - enhanced_ensemble_selector - INFO - 量化评估选择完成:
2025-07-16 23:26:33 - enhanced_ensemble_selector - INFO -   最优组合: ['DecisionTree', 'RandomForest', 'SVM']
2025-07-16 23:26:33 - enhanced_ensemble_selector - INFO -   性能得分: 0.8486
2025-07-16 23:26:33 - enhanced_ensemble_selector - INFO -   多样性得分: 0.3491
2025-07-16 23:26:33 - enhanced_ensemble_selector - INFO -   综合得分: 0.5988
2025-07-16 23:26:33 - enhanced_ensemble_selector - INFO -   多样性等级: 中等的多样性
2025-07-16 23:26:33 - enhanced_ensemble_selector - INFO - 开始评估 4 个基模型...
2025-07-16 23:26:33 - enhanced_ensemble_selector - INFO -   评估模型: DecisionTree
2025-07-16 23:26:33 - enhanced_ensemble_selector - INFO -     DecisionTree - 性能得分: 0.7279
2025-07-16 23:26:33 - enhanced_ensemble_selector - INFO -   评估模型: RandomForest
2025-07-16 23:26:33 - enhanced_ensemble_selector - INFO -     RandomForest - 性能得分: 0.8630
2025-07-16 23:26:33 - enhanced_ensemble_selector - INFO -   评估模型: Logistic
2025-07-16 23:26:33 - enhanced_ensemble_selector - INFO -     Logistic - 性能得分: 0.7726
2025-07-16 23:26:33 - enhanced_ensemble_selector - INFO -   评估模型: SVM
2025-07-16 23:26:33 - enhanced_ensemble_selector - INFO -     SVM - 性能得分: 0.8912
2025-07-16 23:26:33 - enhanced_ensemble_selector - INFO - 使用改进的多样性分析完成
2025-07-16 23:26:33 - enhanced_ensemble_selector - INFO - 数据复杂度: 0.133
2025-07-16 23:26:33 - enhanced_ensemble_selector - INFO - 调整后相关性阈值: 0.600
2025-07-16 23:26:33 - enhanced_ensemble_selector - INFO - 综合多样性得分: 0.435
2025-07-16 23:26:33 - enhanced_ensemble_selector - INFO -   DecisionTree vs RandomForest: 相关性=0.744, 多样性=0.256
2025-07-16 23:26:33 - enhanced_ensemble_selector - INFO -   DecisionTree vs Logistic: 相关性=0.520, 多样性=0.480
2025-07-16 23:26:33 - enhanced_ensemble_selector - INFO -   DecisionTree vs SVM: 相关性=0.670, 多样性=0.330
2025-07-16 23:26:33 - enhanced_ensemble_selector - INFO -   RandomForest vs Logistic: 相关性=0.761, 多样性=0.239
2025-07-16 23:26:33 - enhanced_ensemble_selector - INFO -   RandomForest vs SVM: 相关性=0.915, 多样性=0.085
2025-07-16 23:26:33 - enhanced_ensemble_selector - INFO -   Logistic vs SVM: 相关性=0.723, 多样性=0.277
2025-07-16 23:26:33 - enhanced_ensemble_selector - INFO - 特征重要性多样性: 0.653
2025-07-16 23:26:33 - enhanced_ensemble_selector - INFO - 算法类型多样性: 0.500
2025-07-16 23:26:33 - enhanced_ensemble_selector - INFO - 算法类型分布: {'tree': 2, 'linear': 2}
2025-07-16 23:26:33 - enhanced_ensemble_selector - INFO - 开始量化多样性评估...
2025-07-16 23:26:33 - enhanced_ensemble_selector - INFO - 量化多样性评估完成:
2025-07-16 23:26:33 - enhanced_ensemble_selector - INFO -   Q统计量平均多样性: 0.410
2025-07-16 23:26:33 - enhanced_ensemble_selector - INFO -   多样性变异系数: 0.277
2025-07-16 23:26:33 - enhanced_ensemble_selector - INFO -   熵多样性: 0.340
2025-07-16 23:26:33 - enhanced_ensemble_selector - INFO - 从 4 个合格模型中选择 3 个进行集成
2025-07-16 23:26:33 - enhanced_ensemble_selector - INFO - 最优组合: ['RandomForest', 'Logistic', 'SVM']
2025-07-16 23:26:33 - enhanced_ensemble_selector - INFO - 综合得分: 0.8423
2025-07-16 23:26:33 - enhanced_ensemble_selector - INFO - 从 4 个合格模型中选择 3 个进行集成
2025-07-16 23:26:33 - enhanced_ensemble_selector - INFO - 最优组合: ['DecisionTree', 'Logistic', 'SVM']
2025-07-16 23:26:33 - enhanced_ensemble_selector - INFO - 综合得分: 0.4645
2025-07-16 23:26:33 - enhanced_ensemble_selector - INFO - 从 4 个合格模型中选择 3 个进行集成
2025-07-16 23:26:33 - enhanced_ensemble_selector - INFO - 最优组合: ['DecisionTree', 'Logistic', 'SVM']
2025-07-16 23:26:33 - enhanced_ensemble_selector - INFO - 综合得分: 0.6206
2025-07-16 23:26:33 - enhanced_ensemble_selector - INFO - 从 4 个合格模型中选择 3 个进行集成
2025-07-16 23:26:33 - enhanced_ensemble_selector - INFO - 使用量化多样性评估进行模型选择...
2025-07-16 23:26:33 - enhanced_ensemble_selector - INFO - 量化评估选择完成:
2025-07-16 23:26:33 - enhanced_ensemble_selector - INFO -   最优组合: ['DecisionTree', 'RandomForest', 'Logistic']
2025-07-16 23:26:33 - enhanced_ensemble_selector - INFO -   性能得分: 0.7878
2025-07-16 23:26:33 - enhanced_ensemble_selector - INFO -   多样性得分: 0.4790
2025-07-16 23:26:33 - enhanced_ensemble_selector - INFO -   综合得分: 0.6334
2025-07-16 23:26:33 - enhanced_ensemble_selector - INFO -   多样性等级: 中等的多样性
2025-07-16 23:28:49 - enhanced_ensemble_selector - INFO - 开始评估 10 个基模型...
2025-07-16 23:28:49 - enhanced_ensemble_selector - INFO -   评估模型: DecisionTree
2025-07-16 23:28:49 - enhanced_ensemble_selector - INFO -     DecisionTree - 性能得分: 0.8233
2025-07-16 23:28:49 - enhanced_ensemble_selector - INFO -   评估模型: RandomForest
2025-07-16 23:28:49 - enhanced_ensemble_selector - INFO -     RandomForest - 性能得分: 0.9097
2025-07-16 23:28:49 - enhanced_ensemble_selector - INFO -   评估模型: XGBoost
2025-07-16 23:28:49 - enhanced_ensemble_selector - INFO -     XGBoost - 性能得分: 0.8978
2025-07-16 23:28:49 - enhanced_ensemble_selector - INFO -   评估模型: LightGBM
2025-07-16 23:28:51 - enhanced_ensemble_selector - INFO -     LightGBM - 性能得分: 0.8927
2025-07-16 23:28:51 - enhanced_ensemble_selector - INFO -   评估模型: CatBoost
2025-07-16 23:28:52 - enhanced_ensemble_selector - INFO -     CatBoost - 性能得分: 0.9148
2025-07-16 23:28:52 - enhanced_ensemble_selector - INFO -   评估模型: Logistic
2025-07-16 23:28:52 - enhanced_ensemble_selector - INFO -     Logistic - 性能得分: 0.8694
2025-07-16 23:28:52 - enhanced_ensemble_selector - INFO -   评估模型: SVM
2025-07-16 23:28:52 - enhanced_ensemble_selector - INFO -     SVM - 性能得分: 0.8297
2025-07-16 23:28:52 - enhanced_ensemble_selector - INFO -   评估模型: KNN
2025-07-16 23:28:52 - enhanced_ensemble_selector - INFO -     KNN - 性能得分: 0.8892
2025-07-16 23:28:52 - enhanced_ensemble_selector - INFO -   评估模型: NaiveBayes
2025-07-16 23:28:52 - enhanced_ensemble_selector - INFO -     NaiveBayes - 性能得分: 0.8809
2025-07-16 23:28:52 - enhanced_ensemble_selector - INFO -   评估模型: NeuralNet
2025-07-16 23:28:52 - enhanced_ensemble_selector - INFO -     NeuralNet - 性能得分: 0.8959
2025-07-16 23:28:52 - enhanced_ensemble_selector - INFO - 使用改进的多样性分析完成
2025-07-16 23:28:52 - enhanced_ensemble_selector - INFO - 数据复杂度: 0.246
2025-07-16 23:28:52 - enhanced_ensemble_selector - INFO - 调整后相关性阈值: 0.600
2025-07-16 23:28:52 - enhanced_ensemble_selector - INFO - 综合多样性得分: 0.374
2025-07-16 23:28:52 - enhanced_ensemble_selector - INFO -   DecisionTree vs RandomForest: 相关性=0.806, 多样性=0.194
2025-07-16 23:28:52 - enhanced_ensemble_selector - INFO -   DecisionTree vs XGBoost: 相关性=0.843, 多样性=0.157
2025-07-16 23:28:52 - enhanced_ensemble_selector - INFO -   DecisionTree vs LightGBM: 相关性=0.784, 多样性=0.216
2025-07-16 23:28:52 - enhanced_ensemble_selector - INFO -   DecisionTree vs CatBoost: 相关性=0.803, 多样性=0.197
2025-07-16 23:28:52 - enhanced_ensemble_selector - INFO -   DecisionTree vs Logistic: 相关性=0.673, 多样性=0.327
2025-07-16 23:28:52 - enhanced_ensemble_selector - INFO -   DecisionTree vs SVM: 相关性=0.699, 多样性=0.301
2025-07-16 23:28:52 - enhanced_ensemble_selector - INFO -   DecisionTree vs KNN: 相关性=0.696, 多样性=0.304
2025-07-16 23:28:52 - enhanced_ensemble_selector - INFO -   DecisionTree vs NaiveBayes: 相关性=0.659, 多样性=0.341
2025-07-16 23:28:52 - enhanced_ensemble_selector - INFO -   DecisionTree vs NeuralNet: 相关性=0.643, 多样性=0.357
2025-07-16 23:28:52 - enhanced_ensemble_selector - INFO -   RandomForest vs XGBoost: 相关性=0.955, 多样性=0.045
2025-07-16 23:28:52 - enhanced_ensemble_selector - INFO -   RandomForest vs LightGBM: 相关性=0.950, 多样性=0.050
2025-07-16 23:28:52 - enhanced_ensemble_selector - INFO -   RandomForest vs CatBoost: 相关性=0.986, 多样性=0.014
2025-07-16 23:28:52 - enhanced_ensemble_selector - INFO -   RandomForest vs Logistic: 相关性=0.887, 多样性=0.113
2025-07-16 23:28:52 - enhanced_ensemble_selector - INFO -   RandomForest vs SVM: 相关性=0.936, 多样性=0.064
2025-07-16 23:28:52 - enhanced_ensemble_selector - INFO -   RandomForest vs KNN: 相关性=0.912, 多样性=0.088
2025-07-16 23:28:52 - enhanced_ensemble_selector - INFO -   RandomForest vs NaiveBayes: 相关性=0.861, 多样性=0.139
2025-07-16 23:28:52 - enhanced_ensemble_selector - INFO -   RandomForest vs NeuralNet: 相关性=0.886, 多样性=0.114
2025-07-16 23:28:52 - enhanced_ensemble_selector - INFO -   XGBoost vs LightGBM: 相关性=0.971, 多样性=0.029
2025-07-16 23:28:52 - enhanced_ensemble_selector - INFO -   XGBoost vs CatBoost: 相关性=0.981, 多样性=0.019
2025-07-16 23:28:52 - enhanced_ensemble_selector - INFO -   XGBoost vs Logistic: 相关性=0.814, 多样性=0.186
2025-07-16 23:28:52 - enhanced_ensemble_selector - INFO -   XGBoost vs SVM: 相关性=0.888, 多样性=0.112
2025-07-16 23:28:52 - enhanced_ensemble_selector - INFO -   XGBoost vs KNN: 相关性=0.857, 多样性=0.143
2025-07-16 23:28:52 - enhanced_ensemble_selector - INFO -   XGBoost vs NaiveBayes: 相关性=0.822, 多样性=0.178
2025-07-16 23:28:52 - enhanced_ensemble_selector - INFO -   XGBoost vs NeuralNet: 相关性=0.838, 多样性=0.162
2025-07-16 23:28:52 - enhanced_ensemble_selector - INFO -   LightGBM vs CatBoost: 相关性=0.981, 多样性=0.019
2025-07-16 23:28:52 - enhanced_ensemble_selector - INFO -   LightGBM vs Logistic: 相关性=0.819, 多样性=0.181
2025-07-16 23:28:52 - enhanced_ensemble_selector - INFO -   LightGBM vs SVM: 相关性=0.895, 多样性=0.105
2025-07-16 23:28:52 - enhanced_ensemble_selector - INFO -   LightGBM vs KNN: 相关性=0.876, 多样性=0.124
2025-07-16 23:28:52 - enhanced_ensemble_selector - INFO -   LightGBM vs NaiveBayes: 相关性=0.797, 多样性=0.203
2025-07-16 23:28:52 - enhanced_ensemble_selector - INFO -   LightGBM vs NeuralNet: 相关性=0.882, 多样性=0.118
2025-07-16 23:28:52 - enhanced_ensemble_selector - INFO -   CatBoost vs Logistic: 相关性=0.865, 多样性=0.135
2025-07-16 23:28:52 - enhanced_ensemble_selector - INFO -   CatBoost vs SVM: 相关性=0.924, 多样性=0.076
2025-07-16 23:28:52 - enhanced_ensemble_selector - INFO -   CatBoost vs KNN: 相关性=0.907, 多样性=0.093
2025-07-16 23:28:52 - enhanced_ensemble_selector - INFO -   CatBoost vs NaiveBayes: 相关性=0.843, 多样性=0.157
2025-07-16 23:28:52 - enhanced_ensemble_selector - INFO -   CatBoost vs NeuralNet: 相关性=0.892, 多样性=0.108
2025-07-16 23:28:52 - enhanced_ensemble_selector - INFO -   Logistic vs SVM: 相关性=0.899, 多样性=0.101
2025-07-16 23:28:52 - enhanced_ensemble_selector - INFO -   Logistic vs KNN: 相关性=0.963, 多样性=0.037
2025-07-16 23:28:52 - enhanced_ensemble_selector - INFO -   Logistic vs NaiveBayes: 相关性=0.954, 多样性=0.046
2025-07-16 23:28:52 - enhanced_ensemble_selector - INFO -   Logistic vs NeuralNet: 相关性=0.936, 多样性=0.064
2025-07-16 23:28:52 - enhanced_ensemble_selector - INFO -   SVM vs KNN: 相关性=0.924, 多样性=0.076
2025-07-16 23:28:52 - enhanced_ensemble_selector - INFO -   SVM vs NaiveBayes: 相关性=0.874, 多样性=0.126
2025-07-16 23:28:52 - enhanced_ensemble_selector - INFO -   SVM vs NeuralNet: 相关性=0.894, 多样性=0.106
2025-07-16 23:28:52 - enhanced_ensemble_selector - INFO -   KNN vs NaiveBayes: 相关性=0.910, 多样性=0.090
2025-07-16 23:28:52 - enhanced_ensemble_selector - INFO -   KNN vs NeuralNet: 相关性=0.939, 多样性=0.061
2025-07-16 23:28:52 - enhanced_ensemble_selector - INFO -   NaiveBayes vs NeuralNet: 相关性=0.897, 多样性=0.103
2025-07-16 23:28:52 - enhanced_ensemble_selector - INFO - 特征重要性多样性: 0.359
2025-07-16 23:28:52 - enhanced_ensemble_selector - INFO - 算法类型多样性: 1.000
2025-07-16 23:28:52 - enhanced_ensemble_selector - INFO - 算法类型分布: {'tree': 5, 'linear': 2, 'instance': 1, 'probabilistic': 1, 'neural': 1}
2025-07-16 23:28:52 - enhanced_ensemble_selector - INFO - 开始量化多样性评估...
2025-07-16 23:28:53 - enhanced_ensemble_selector - INFO - 量化多样性评估完成:
2025-07-16 23:28:53 - enhanced_ensemble_selector - INFO -   Q统计量平均多样性: 0.297
2025-07-16 23:28:53 - enhanced_ensemble_selector - INFO -   多样性变异系数: 0.301
2025-07-16 23:28:53 - enhanced_ensemble_selector - INFO -   熵多样性: 0.215
2025-07-16 23:28:53 - enhanced_ensemble_selector - INFO - 从 10 个合格模型中选择 3 个进行集成
2025-07-16 23:28:53 - enhanced_ensemble_selector - INFO - 使用量化多样性评估进行模型选择...
2025-07-16 23:28:54 - enhanced_ensemble_selector - INFO - 量化评估选择完成:
2025-07-16 23:28:54 - enhanced_ensemble_selector - INFO -   最优组合: ['DecisionTree', 'KNN', 'NeuralNet']
2025-07-16 23:28:54 - enhanced_ensemble_selector - INFO -   性能得分: 0.8694
2025-07-16 23:28:54 - enhanced_ensemble_selector - INFO -   多样性得分: 0.4692
2025-07-16 23:28:54 - enhanced_ensemble_selector - INFO -   综合得分: 0.6693
2025-07-16 23:28:54 - enhanced_ensemble_selector - INFO -   多样性等级: 中等的多样性
2025-07-16 23:29:20 - enhanced_ensemble_selector - INFO - 开始评估 10 个基模型...
2025-07-16 23:29:20 - enhanced_ensemble_selector - INFO -   评估模型: DecisionTree
2025-07-16 23:29:20 - enhanced_ensemble_selector - INFO -     DecisionTree - 性能得分: 0.8461
2025-07-16 23:29:20 - enhanced_ensemble_selector - INFO -   评估模型: RandomForest
2025-07-16 23:29:20 - enhanced_ensemble_selector - INFO -     RandomForest - 性能得分: 0.8439
2025-07-16 23:29:20 - enhanced_ensemble_selector - INFO -   评估模型: XGBoost
2025-07-16 23:29:21 - enhanced_ensemble_selector - INFO -     XGBoost - 性能得分: 0.7959
2025-07-16 23:29:21 - enhanced_ensemble_selector - INFO -   评估模型: LightGBM
2025-07-16 23:29:22 - enhanced_ensemble_selector - INFO -     LightGBM - 性能得分: 0.7759
2025-07-16 23:29:22 - enhanced_ensemble_selector - INFO -   评估模型: CatBoost
2025-07-16 23:29:23 - enhanced_ensemble_selector - INFO -     CatBoost - 性能得分: 0.8828
2025-07-16 23:29:23 - enhanced_ensemble_selector - INFO -   评估模型: Logistic
2025-07-16 23:29:23 - enhanced_ensemble_selector - INFO -     Logistic - 性能得分: 0.8449
2025-07-16 23:29:23 - enhanced_ensemble_selector - INFO -   评估模型: SVM
2025-07-16 23:29:23 - enhanced_ensemble_selector - INFO -     SVM - 性能得分: 0.8892
2025-07-16 23:29:23 - enhanced_ensemble_selector - INFO -   评估模型: KNN
2025-07-16 23:29:23 - enhanced_ensemble_selector - INFO -     KNN - 性能得分: 0.8978
2025-07-16 23:29:23 - enhanced_ensemble_selector - INFO -   评估模型: NaiveBayes
2025-07-16 23:29:23 - enhanced_ensemble_selector - INFO -     NaiveBayes - 性能得分: 0.8831
2025-07-16 23:29:23 - enhanced_ensemble_selector - INFO -   评估模型: NeuralNet
2025-07-16 23:29:24 - enhanced_ensemble_selector - INFO -     NeuralNet - 性能得分: 0.8664
2025-07-16 23:29:24 - enhanced_ensemble_selector - INFO - 使用改进的多样性分析完成
2025-07-16 23:29:24 - enhanced_ensemble_selector - INFO - 数据复杂度: 0.232
2025-07-16 23:29:24 - enhanced_ensemble_selector - INFO - 调整后相关性阈值: 0.600
2025-07-16 23:29:24 - enhanced_ensemble_selector - INFO - 综合多样性得分: 0.402
2025-07-16 23:29:24 - enhanced_ensemble_selector - INFO -   DecisionTree vs RandomForest: 相关性=0.866, 多样性=0.134
2025-07-16 23:29:24 - enhanced_ensemble_selector - INFO -   DecisionTree vs XGBoost: 相关性=0.800, 多样性=0.200
2025-07-16 23:29:24 - enhanced_ensemble_selector - INFO -   DecisionTree vs LightGBM: 相关性=0.789, 多样性=0.211
2025-07-16 23:29:24 - enhanced_ensemble_selector - INFO -   DecisionTree vs CatBoost: 相关性=0.848, 多样性=0.152
2025-07-16 23:29:24 - enhanced_ensemble_selector - INFO -   DecisionTree vs Logistic: 相关性=0.733, 多样性=0.267
2025-07-16 23:29:24 - enhanced_ensemble_selector - INFO -   DecisionTree vs SVM: 相关性=0.722, 多样性=0.278
2025-07-16 23:29:24 - enhanced_ensemble_selector - INFO -   DecisionTree vs KNN: 相关性=0.726, 多样性=0.274
2025-07-16 23:29:24 - enhanced_ensemble_selector - INFO -   DecisionTree vs NaiveBayes: 相关性=0.708, 多样性=0.292
2025-07-16 23:29:24 - enhanced_ensemble_selector - INFO -   DecisionTree vs NeuralNet: 相关性=0.733, 多样性=0.267
2025-07-16 23:29:24 - enhanced_ensemble_selector - INFO -   RandomForest vs XGBoost: 相关性=0.906, 多样性=0.094
2025-07-16 23:29:24 - enhanced_ensemble_selector - INFO -   RandomForest vs LightGBM: 相关性=0.918, 多样性=0.082
2025-07-16 23:29:24 - enhanced_ensemble_selector - INFO -   RandomForest vs CatBoost: 相关性=0.977, 多样性=0.023
2025-07-16 23:29:24 - enhanced_ensemble_selector - INFO -   RandomForest vs Logistic: 相关性=0.903, 多样性=0.097
2025-07-16 23:29:24 - enhanced_ensemble_selector - INFO -   RandomForest vs SVM: 相关性=0.885, 多样性=0.115
2025-07-16 23:29:24 - enhanced_ensemble_selector - INFO -   RandomForest vs KNN: 相关性=0.887, 多样性=0.113
2025-07-16 23:29:24 - enhanced_ensemble_selector - INFO -   RandomForest vs NaiveBayes: 相关性=0.810, 多样性=0.190
2025-07-16 23:29:24 - enhanced_ensemble_selector - INFO -   RandomForest vs NeuralNet: 相关性=0.865, 多样性=0.135
2025-07-16 23:29:24 - enhanced_ensemble_selector - INFO -   XGBoost vs LightGBM: 相关性=0.956, 多样性=0.044
2025-07-16 23:29:24 - enhanced_ensemble_selector - INFO -   XGBoost vs CatBoost: 相关性=0.911, 多样性=0.089
2025-07-16 23:29:24 - enhanced_ensemble_selector - INFO -   XGBoost vs Logistic: 相关性=0.848, 多样性=0.152
2025-07-16 23:29:24 - enhanced_ensemble_selector - INFO -   XGBoost vs SVM: 相关性=0.758, 多样性=0.242
2025-07-16 23:29:24 - enhanced_ensemble_selector - INFO -   XGBoost vs KNN: 相关性=0.775, 多样性=0.225
2025-07-16 23:29:24 - enhanced_ensemble_selector - INFO -   XGBoost vs NaiveBayes: 相关性=0.702, 多样性=0.298
2025-07-16 23:29:24 - enhanced_ensemble_selector - INFO -   XGBoost vs NeuralNet: 相关性=0.786, 多样性=0.214
2025-07-16 23:29:24 - enhanced_ensemble_selector - INFO -   LightGBM vs CatBoost: 相关性=0.939, 多样性=0.061
2025-07-16 23:29:24 - enhanced_ensemble_selector - INFO -   LightGBM vs Logistic: 相关性=0.834, 多样性=0.166
2025-07-16 23:29:24 - enhanced_ensemble_selector - INFO -   LightGBM vs SVM: 相关性=0.769, 多样性=0.231
2025-07-16 23:29:24 - enhanced_ensemble_selector - INFO -   LightGBM vs KNN: 相关性=0.778, 多样性=0.222
2025-07-16 23:29:24 - enhanced_ensemble_selector - INFO -   LightGBM vs NaiveBayes: 相关性=0.670, 多样性=0.330
2025-07-16 23:29:24 - enhanced_ensemble_selector - INFO -   LightGBM vs NeuralNet: 相关性=0.796, 多样性=0.204
2025-07-16 23:29:24 - enhanced_ensemble_selector - INFO -   CatBoost vs Logistic: 相关性=0.910, 多样性=0.090
2025-07-16 23:29:24 - enhanced_ensemble_selector - INFO -   CatBoost vs SVM: 相关性=0.896, 多样性=0.104
2025-07-16 23:29:24 - enhanced_ensemble_selector - INFO -   CatBoost vs KNN: 相关性=0.898, 多样性=0.102
2025-07-16 23:29:24 - enhanced_ensemble_selector - INFO -   CatBoost vs NaiveBayes: 相关性=0.826, 多样性=0.174
2025-07-16 23:29:24 - enhanced_ensemble_selector - INFO -   CatBoost vs NeuralNet: 相关性=0.904, 多样性=0.096
2025-07-16 23:29:24 - enhanced_ensemble_selector - INFO -   Logistic vs SVM: 相关性=0.901, 多样性=0.099
2025-07-16 23:29:24 - enhanced_ensemble_selector - INFO -   Logistic vs KNN: 相关性=0.890, 多样性=0.110
2025-07-16 23:29:24 - enhanced_ensemble_selector - INFO -   Logistic vs NaiveBayes: 相关性=0.832, 多样性=0.168
2025-07-16 23:29:24 - enhanced_ensemble_selector - INFO -   Logistic vs NeuralNet: 相关性=0.945, 多样性=0.055
2025-07-16 23:29:24 - enhanced_ensemble_selector - INFO -   SVM vs KNN: 相关性=0.942, 多样性=0.058
2025-07-16 23:29:24 - enhanced_ensemble_selector - INFO -   SVM vs NaiveBayes: 相关性=0.870, 多样性=0.130
2025-07-16 23:29:24 - enhanced_ensemble_selector - INFO -   SVM vs NeuralNet: 相关性=0.908, 多样性=0.092
2025-07-16 23:29:24 - enhanced_ensemble_selector - INFO -   KNN vs NaiveBayes: 相关性=0.959, 多样性=0.041
2025-07-16 23:29:24 - enhanced_ensemble_selector - INFO -   KNN vs NeuralNet: 相关性=0.866, 多样性=0.134
2025-07-16 23:29:24 - enhanced_ensemble_selector - INFO -   NaiveBayes vs NeuralNet: 相关性=0.796, 多样性=0.204
2025-07-16 23:29:24 - enhanced_ensemble_selector - INFO - 特征重要性多样性: 0.413
2025-07-16 23:29:24 - enhanced_ensemble_selector - INFO - 算法类型多样性: 1.000
2025-07-16 23:29:24 - enhanced_ensemble_selector - INFO - 算法类型分布: {'tree': 5, 'linear': 2, 'instance': 1, 'probabilistic': 1, 'neural': 1}
2025-07-16 23:29:24 - enhanced_ensemble_selector - INFO - 开始量化多样性评估...
2025-07-16 23:29:24 - enhanced_ensemble_selector - INFO - 量化多样性评估完成:
2025-07-16 23:29:24 - enhanced_ensemble_selector - INFO -   Q统计量平均多样性: 0.351
2025-07-16 23:29:24 - enhanced_ensemble_selector - INFO -   多样性变异系数: 0.424
2025-07-16 23:29:24 - enhanced_ensemble_selector - INFO -   熵多样性: 0.299
2025-07-16 23:29:24 - enhanced_ensemble_selector - INFO - 从 10 个合格模型中选择 3 个进行集成
2025-07-16 23:29:24 - enhanced_ensemble_selector - INFO - 最优组合: ['DecisionTree', 'NaiveBayes', 'NeuralNet']
2025-07-16 23:29:24 - enhanced_ensemble_selector - INFO - 综合得分: 0.7090
2025-07-16 23:29:30 - enhanced_ensemble_selector - INFO - 开始评估 10 个基模型...
2025-07-16 23:29:30 - enhanced_ensemble_selector - INFO -   评估模型: DecisionTree
2025-07-16 23:29:30 - enhanced_ensemble_selector - INFO -     DecisionTree - 性能得分: 0.8461
2025-07-16 23:29:30 - enhanced_ensemble_selector - INFO -   评估模型: RandomForest
2025-07-16 23:29:31 - enhanced_ensemble_selector - INFO -     RandomForest - 性能得分: 0.8439
2025-07-16 23:29:31 - enhanced_ensemble_selector - INFO -   评估模型: XGBoost
2025-07-16 23:29:31 - enhanced_ensemble_selector - INFO -     XGBoost - 性能得分: 0.7959
2025-07-16 23:29:31 - enhanced_ensemble_selector - INFO -   评估模型: LightGBM
2025-07-16 23:29:31 - enhanced_ensemble_selector - INFO -     LightGBM - 性能得分: 0.7759
2025-07-16 23:29:31 - enhanced_ensemble_selector - INFO -   评估模型: CatBoost
2025-07-16 23:29:32 - enhanced_ensemble_selector - INFO -     CatBoost - 性能得分: 0.8828
2025-07-16 23:29:32 - enhanced_ensemble_selector - INFO -   评估模型: Logistic
2025-07-16 23:29:32 - enhanced_ensemble_selector - INFO -     Logistic - 性能得分: 0.8449
2025-07-16 23:29:32 - enhanced_ensemble_selector - INFO -   评估模型: SVM
2025-07-16 23:29:32 - enhanced_ensemble_selector - INFO -     SVM - 性能得分: 0.8892
2025-07-16 23:29:32 - enhanced_ensemble_selector - INFO -   评估模型: KNN
2025-07-16 23:29:32 - enhanced_ensemble_selector - INFO -     KNN - 性能得分: 0.8978
2025-07-16 23:29:32 - enhanced_ensemble_selector - INFO -   评估模型: NaiveBayes
2025-07-16 23:29:32 - enhanced_ensemble_selector - INFO -     NaiveBayes - 性能得分: 0.8831
2025-07-16 23:29:32 - enhanced_ensemble_selector - INFO -   评估模型: NeuralNet
2025-07-16 23:29:32 - enhanced_ensemble_selector - INFO -     NeuralNet - 性能得分: 0.8664
2025-07-16 23:29:32 - enhanced_ensemble_selector - INFO - 使用改进的多样性分析完成
2025-07-16 23:29:32 - enhanced_ensemble_selector - INFO - 数据复杂度: 0.232
2025-07-16 23:29:32 - enhanced_ensemble_selector - INFO - 调整后相关性阈值: 0.600
2025-07-16 23:29:32 - enhanced_ensemble_selector - INFO - 综合多样性得分: 0.402
2025-07-16 23:29:32 - enhanced_ensemble_selector - INFO -   DecisionTree vs RandomForest: 相关性=0.866, 多样性=0.134
2025-07-16 23:29:32 - enhanced_ensemble_selector - INFO -   DecisionTree vs XGBoost: 相关性=0.800, 多样性=0.200
2025-07-16 23:29:32 - enhanced_ensemble_selector - INFO -   DecisionTree vs LightGBM: 相关性=0.789, 多样性=0.211
2025-07-16 23:29:32 - enhanced_ensemble_selector - INFO -   DecisionTree vs CatBoost: 相关性=0.848, 多样性=0.152
2025-07-16 23:29:32 - enhanced_ensemble_selector - INFO -   DecisionTree vs Logistic: 相关性=0.733, 多样性=0.267
2025-07-16 23:29:32 - enhanced_ensemble_selector - INFO -   DecisionTree vs SVM: 相关性=0.722, 多样性=0.278
2025-07-16 23:29:32 - enhanced_ensemble_selector - INFO -   DecisionTree vs KNN: 相关性=0.726, 多样性=0.274
2025-07-16 23:29:32 - enhanced_ensemble_selector - INFO -   DecisionTree vs NaiveBayes: 相关性=0.708, 多样性=0.292
2025-07-16 23:29:32 - enhanced_ensemble_selector - INFO -   DecisionTree vs NeuralNet: 相关性=0.733, 多样性=0.267
2025-07-16 23:29:32 - enhanced_ensemble_selector - INFO -   RandomForest vs XGBoost: 相关性=0.906, 多样性=0.094
2025-07-16 23:29:32 - enhanced_ensemble_selector - INFO -   RandomForest vs LightGBM: 相关性=0.918, 多样性=0.082
2025-07-16 23:29:32 - enhanced_ensemble_selector - INFO -   RandomForest vs CatBoost: 相关性=0.977, 多样性=0.023
2025-07-16 23:29:32 - enhanced_ensemble_selector - INFO -   RandomForest vs Logistic: 相关性=0.903, 多样性=0.097
2025-07-16 23:29:32 - enhanced_ensemble_selector - INFO -   RandomForest vs SVM: 相关性=0.885, 多样性=0.115
2025-07-16 23:29:32 - enhanced_ensemble_selector - INFO -   RandomForest vs KNN: 相关性=0.887, 多样性=0.113
2025-07-16 23:29:32 - enhanced_ensemble_selector - INFO -   RandomForest vs NaiveBayes: 相关性=0.810, 多样性=0.190
2025-07-16 23:29:32 - enhanced_ensemble_selector - INFO -   RandomForest vs NeuralNet: 相关性=0.865, 多样性=0.135
2025-07-16 23:29:32 - enhanced_ensemble_selector - INFO -   XGBoost vs LightGBM: 相关性=0.956, 多样性=0.044
2025-07-16 23:29:32 - enhanced_ensemble_selector - INFO -   XGBoost vs CatBoost: 相关性=0.911, 多样性=0.089
2025-07-16 23:29:32 - enhanced_ensemble_selector - INFO -   XGBoost vs Logistic: 相关性=0.848, 多样性=0.152
2025-07-16 23:29:32 - enhanced_ensemble_selector - INFO -   XGBoost vs SVM: 相关性=0.758, 多样性=0.242
2025-07-16 23:29:32 - enhanced_ensemble_selector - INFO -   XGBoost vs KNN: 相关性=0.775, 多样性=0.225
2025-07-16 23:29:32 - enhanced_ensemble_selector - INFO -   XGBoost vs NaiveBayes: 相关性=0.702, 多样性=0.298
2025-07-16 23:29:32 - enhanced_ensemble_selector - INFO -   XGBoost vs NeuralNet: 相关性=0.786, 多样性=0.214
2025-07-16 23:29:32 - enhanced_ensemble_selector - INFO -   LightGBM vs CatBoost: 相关性=0.939, 多样性=0.061
2025-07-16 23:29:32 - enhanced_ensemble_selector - INFO -   LightGBM vs Logistic: 相关性=0.834, 多样性=0.166
2025-07-16 23:29:32 - enhanced_ensemble_selector - INFO -   LightGBM vs SVM: 相关性=0.769, 多样性=0.231
2025-07-16 23:29:32 - enhanced_ensemble_selector - INFO -   LightGBM vs KNN: 相关性=0.778, 多样性=0.222
2025-07-16 23:29:32 - enhanced_ensemble_selector - INFO -   LightGBM vs NaiveBayes: 相关性=0.670, 多样性=0.330
2025-07-16 23:29:32 - enhanced_ensemble_selector - INFO -   LightGBM vs NeuralNet: 相关性=0.796, 多样性=0.204
2025-07-16 23:29:32 - enhanced_ensemble_selector - INFO -   CatBoost vs Logistic: 相关性=0.910, 多样性=0.090
2025-07-16 23:29:32 - enhanced_ensemble_selector - INFO -   CatBoost vs SVM: 相关性=0.896, 多样性=0.104
2025-07-16 23:29:32 - enhanced_ensemble_selector - INFO -   CatBoost vs KNN: 相关性=0.898, 多样性=0.102
2025-07-16 23:29:32 - enhanced_ensemble_selector - INFO -   CatBoost vs NaiveBayes: 相关性=0.826, 多样性=0.174
2025-07-16 23:29:32 - enhanced_ensemble_selector - INFO -   CatBoost vs NeuralNet: 相关性=0.904, 多样性=0.096
2025-07-16 23:29:32 - enhanced_ensemble_selector - INFO -   Logistic vs SVM: 相关性=0.901, 多样性=0.099
2025-07-16 23:29:32 - enhanced_ensemble_selector - INFO -   Logistic vs KNN: 相关性=0.890, 多样性=0.110
2025-07-16 23:29:32 - enhanced_ensemble_selector - INFO -   Logistic vs NaiveBayes: 相关性=0.832, 多样性=0.168
2025-07-16 23:29:32 - enhanced_ensemble_selector - INFO -   Logistic vs NeuralNet: 相关性=0.945, 多样性=0.055
2025-07-16 23:29:32 - enhanced_ensemble_selector - INFO -   SVM vs KNN: 相关性=0.942, 多样性=0.058
2025-07-16 23:29:32 - enhanced_ensemble_selector - INFO -   SVM vs NaiveBayes: 相关性=0.870, 多样性=0.130
2025-07-16 23:29:32 - enhanced_ensemble_selector - INFO -   SVM vs NeuralNet: 相关性=0.908, 多样性=0.092
2025-07-16 23:29:32 - enhanced_ensemble_selector - INFO -   KNN vs NaiveBayes: 相关性=0.959, 多样性=0.041
2025-07-16 23:29:32 - enhanced_ensemble_selector - INFO -   KNN vs NeuralNet: 相关性=0.866, 多样性=0.134
2025-07-16 23:29:32 - enhanced_ensemble_selector - INFO -   NaiveBayes vs NeuralNet: 相关性=0.796, 多样性=0.204
2025-07-16 23:29:32 - enhanced_ensemble_selector - INFO - 特征重要性多样性: 0.413
2025-07-16 23:29:32 - enhanced_ensemble_selector - INFO - 算法类型多样性: 1.000
2025-07-16 23:29:32 - enhanced_ensemble_selector - INFO - 算法类型分布: {'tree': 5, 'linear': 2, 'instance': 1, 'probabilistic': 1, 'neural': 1}
2025-07-16 23:29:32 - enhanced_ensemble_selector - INFO - 开始量化多样性评估...
2025-07-16 23:29:32 - enhanced_ensemble_selector - INFO - 量化多样性评估完成:
2025-07-16 23:29:32 - enhanced_ensemble_selector - INFO -   Q统计量平均多样性: 0.351
2025-07-16 23:29:32 - enhanced_ensemble_selector - INFO -   多样性变异系数: 0.424
2025-07-16 23:29:32 - enhanced_ensemble_selector - INFO -   熵多样性: 0.299
2025-07-16 23:29:32 - enhanced_ensemble_selector - INFO - 从 10 个合格模型中选择 3 个进行集成
2025-07-16 23:29:32 - enhanced_ensemble_selector - INFO - 使用量化多样性评估进行模型选择...
2025-07-16 23:29:33 - enhanced_ensemble_selector - INFO - 量化评估选择完成:
2025-07-16 23:29:33 - enhanced_ensemble_selector - INFO -   最优组合: ['DecisionTree', 'LightGBM', 'NeuralNet']
2025-07-16 23:29:33 - enhanced_ensemble_selector - INFO -   性能得分: 0.8295
2025-07-16 23:29:33 - enhanced_ensemble_selector - INFO -   多样性得分: 0.5795
2025-07-16 23:29:33 - enhanced_ensemble_selector - INFO -   综合得分: 0.7045
2025-07-16 23:29:33 - enhanced_ensemble_selector - INFO -   多样性等级: 良好的多样性
2025-07-16 23:32:26 - enhanced_ensemble_selector - INFO - 开始评估 5 个基模型...
2025-07-16 23:32:26 - enhanced_ensemble_selector - INFO -   评估模型: DecisionTree
2025-07-16 23:32:26 - enhanced_ensemble_selector - INFO -     DecisionTree - 性能得分: 0.7880
2025-07-16 23:32:26 - enhanced_ensemble_selector - INFO -   评估模型: RandomForest
2025-07-16 23:32:26 - enhanced_ensemble_selector - INFO -     RandomForest - 性能得分: 0.8757
2025-07-16 23:32:26 - enhanced_ensemble_selector - INFO -   评估模型: Logistic
2025-07-16 23:32:26 - enhanced_ensemble_selector - INFO -     Logistic - 性能得分: 0.8155
2025-07-16 23:32:26 - enhanced_ensemble_selector - INFO -   评估模型: SVM
2025-07-16 23:32:26 - enhanced_ensemble_selector - INFO -     SVM - 性能得分: 0.8821
2025-07-16 23:32:26 - enhanced_ensemble_selector - INFO -   评估模型: XGBoost
2025-07-16 23:32:26 - enhanced_ensemble_selector - INFO -     XGBoost - 性能得分: 0.8683
2025-07-16 23:32:26 - enhanced_ensemble_selector - INFO - 使用改进的多样性分析完成
2025-07-16 23:32:26 - enhanced_ensemble_selector - INFO - 数据复杂度: 0.126
2025-07-16 23:32:26 - enhanced_ensemble_selector - INFO - 调整后相关性阈值: 0.600
2025-07-16 23:32:26 - enhanced_ensemble_selector - INFO - 综合多样性得分: 0.213
2025-07-16 23:32:26 - enhanced_ensemble_selector - INFO -   DecisionTree vs RandomForest: 相关性=0.768, 多样性=0.232
2025-07-16 23:32:26 - enhanced_ensemble_selector - INFO -   DecisionTree vs Logistic: 相关性=0.711, 多样性=0.289
2025-07-16 23:32:26 - enhanced_ensemble_selector - INFO -   DecisionTree vs SVM: 相关性=0.695, 多样性=0.305
2025-07-16 23:32:26 - enhanced_ensemble_selector - INFO -   DecisionTree vs XGBoost: 相关性=0.741, 多样性=0.259
2025-07-16 23:32:26 - enhanced_ensemble_selector - INFO -   RandomForest vs Logistic: 相关性=0.892, 多样性=0.108
2025-07-16 23:32:26 - enhanced_ensemble_selector - INFO -   RandomForest vs SVM: 相关性=0.909, 多样性=0.091
2025-07-16 23:32:26 - enhanced_ensemble_selector - INFO -   RandomForest vs XGBoost: 相关性=0.925, 多样性=0.075
2025-07-16 23:32:26 - enhanced_ensemble_selector - INFO -   Logistic vs SVM: 相关性=0.857, 多样性=0.143
2025-07-16 23:32:26 - enhanced_ensemble_selector - INFO -   Logistic vs XGBoost: 相关性=0.864, 多样性=0.136
2025-07-16 23:32:26 - enhanced_ensemble_selector - INFO -   SVM vs XGBoost: 相关性=0.937, 多样性=0.063
2025-07-16 23:32:26 - enhanced_ensemble_selector - INFO - 特征重要性多样性: 0.225
2025-07-16 23:32:26 - enhanced_ensemble_selector - INFO - 算法类型多样性: 0.300
2025-07-16 23:32:26 - enhanced_ensemble_selector - INFO - 算法类型分布: {'tree': 3, 'linear': 2}
2025-07-16 23:32:26 - enhanced_ensemble_selector - INFO - 开始量化多样性评估...
2025-07-16 23:32:26 - enhanced_ensemble_selector - INFO - 量化多样性评估完成:
2025-07-16 23:32:26 - enhanced_ensemble_selector - INFO -   Q统计量平均多样性: 0.313
2025-07-16 23:32:26 - enhanced_ensemble_selector - INFO -   多样性变异系数: 0.205
2025-07-16 23:32:26 - enhanced_ensemble_selector - INFO -   熵多样性: 0.241
2025-07-16 23:32:26 - enhanced_ensemble_selector - INFO - 从 5 个合格模型中选择 3 个进行集成
2025-07-16 23:32:26 - enhanced_ensemble_selector - INFO - 使用量化多样性评估进行模型选择...
2025-07-16 23:32:26 - enhanced_ensemble_selector - INFO - 量化评估选择完成:
2025-07-16 23:32:26 - enhanced_ensemble_selector - INFO -   最优组合: ['DecisionTree', 'RandomForest', 'SVM']
2025-07-16 23:32:26 - enhanced_ensemble_selector - INFO -   性能得分: 0.8486
2025-07-16 23:32:26 - enhanced_ensemble_selector - INFO -   多样性得分: 0.3491
2025-07-16 23:32:26 - enhanced_ensemble_selector - INFO -   综合得分: 0.5988
2025-07-16 23:32:26 - enhanced_ensemble_selector - INFO -   多样性等级: 中等的多样性
2025-07-16 23:32:26 - enhanced_ensemble_selector - INFO - 开始评估 4 个基模型...
2025-07-16 23:32:26 - enhanced_ensemble_selector - INFO -   评估模型: DecisionTree
2025-07-16 23:32:26 - enhanced_ensemble_selector - INFO -     DecisionTree - 性能得分: 0.7279
2025-07-16 23:32:26 - enhanced_ensemble_selector - INFO -   评估模型: RandomForest
2025-07-16 23:32:26 - enhanced_ensemble_selector - INFO -     RandomForest - 性能得分: 0.8630
2025-07-16 23:32:26 - enhanced_ensemble_selector - INFO -   评估模型: Logistic
2025-07-16 23:32:26 - enhanced_ensemble_selector - INFO -     Logistic - 性能得分: 0.7726
2025-07-16 23:32:26 - enhanced_ensemble_selector - INFO -   评估模型: SVM
2025-07-16 23:32:26 - enhanced_ensemble_selector - INFO -     SVM - 性能得分: 0.8912
2025-07-16 23:32:27 - enhanced_ensemble_selector - INFO - 使用改进的多样性分析完成
2025-07-16 23:32:27 - enhanced_ensemble_selector - INFO - 数据复杂度: 0.133
2025-07-16 23:32:27 - enhanced_ensemble_selector - INFO - 调整后相关性阈值: 0.600
2025-07-16 23:32:27 - enhanced_ensemble_selector - INFO - 综合多样性得分: 0.435
2025-07-16 23:32:27 - enhanced_ensemble_selector - INFO -   DecisionTree vs RandomForest: 相关性=0.744, 多样性=0.256
2025-07-16 23:32:27 - enhanced_ensemble_selector - INFO -   DecisionTree vs Logistic: 相关性=0.520, 多样性=0.480
2025-07-16 23:32:27 - enhanced_ensemble_selector - INFO -   DecisionTree vs SVM: 相关性=0.670, 多样性=0.330
2025-07-16 23:32:27 - enhanced_ensemble_selector - INFO -   RandomForest vs Logistic: 相关性=0.761, 多样性=0.239
2025-07-16 23:32:27 - enhanced_ensemble_selector - INFO -   RandomForest vs SVM: 相关性=0.915, 多样性=0.085
2025-07-16 23:32:27 - enhanced_ensemble_selector - INFO -   Logistic vs SVM: 相关性=0.723, 多样性=0.277
2025-07-16 23:32:27 - enhanced_ensemble_selector - INFO - 特征重要性多样性: 0.653
2025-07-16 23:32:27 - enhanced_ensemble_selector - INFO - 算法类型多样性: 0.500
2025-07-16 23:32:27 - enhanced_ensemble_selector - INFO - 算法类型分布: {'tree': 2, 'linear': 2}
2025-07-16 23:32:27 - enhanced_ensemble_selector - INFO - 开始量化多样性评估...
2025-07-16 23:32:27 - enhanced_ensemble_selector - INFO - 量化多样性评估完成:
2025-07-16 23:32:27 - enhanced_ensemble_selector - INFO -   Q统计量平均多样性: 0.410
2025-07-16 23:32:27 - enhanced_ensemble_selector - INFO -   多样性变异系数: 0.277
2025-07-16 23:32:27 - enhanced_ensemble_selector - INFO -   熵多样性: 0.340
2025-07-16 23:32:27 - enhanced_ensemble_selector - INFO - 从 4 个合格模型中选择 3 个进行集成
2025-07-16 23:32:27 - enhanced_ensemble_selector - INFO - 最优组合: ['RandomForest', 'Logistic', 'SVM']
2025-07-16 23:32:27 - enhanced_ensemble_selector - INFO - 综合得分: 0.8423
2025-07-16 23:32:27 - enhanced_ensemble_selector - INFO - 从 4 个合格模型中选择 3 个进行集成
2025-07-16 23:32:27 - enhanced_ensemble_selector - INFO - 最优组合: ['DecisionTree', 'Logistic', 'SVM']
2025-07-16 23:32:27 - enhanced_ensemble_selector - INFO - 综合得分: 0.4645
2025-07-16 23:32:27 - enhanced_ensemble_selector - INFO - 从 4 个合格模型中选择 3 个进行集成
2025-07-16 23:32:27 - enhanced_ensemble_selector - INFO - 最优组合: ['DecisionTree', 'Logistic', 'SVM']
2025-07-16 23:32:27 - enhanced_ensemble_selector - INFO - 综合得分: 0.6206
2025-07-16 23:32:27 - enhanced_ensemble_selector - INFO - 从 4 个合格模型中选择 3 个进行集成
2025-07-16 23:32:27 - enhanced_ensemble_selector - INFO - 使用量化多样性评估进行模型选择...
2025-07-16 23:32:27 - enhanced_ensemble_selector - INFO - 量化评估选择完成:
2025-07-16 23:32:27 - enhanced_ensemble_selector - INFO -   最优组合: ['DecisionTree', 'RandomForest', 'Logistic']
2025-07-16 23:32:27 - enhanced_ensemble_selector - INFO -   性能得分: 0.7878
2025-07-16 23:32:27 - enhanced_ensemble_selector - INFO -   多样性得分: 0.4790
2025-07-16 23:32:27 - enhanced_ensemble_selector - INFO -   综合得分: 0.6334
2025-07-16 23:32:27 - enhanced_ensemble_selector - INFO -   多样性等级: 中等的多样性
2025-07-16 23:39:46 - enhanced_ensemble_selector - INFO - 开始评估 10 个基模型...
2025-07-16 23:39:46 - enhanced_ensemble_selector - INFO -   评估模型: DecisionTree
2025-07-16 23:39:46 - enhanced_ensemble_selector - INFO -     DecisionTree - 性能得分: 0.7752
2025-07-16 23:39:46 - enhanced_ensemble_selector - INFO -   评估模型: RandomForest
2025-07-16 23:39:46 - enhanced_ensemble_selector - INFO -     RandomForest - 性能得分: 0.7470
2025-07-16 23:39:46 - enhanced_ensemble_selector - INFO -   评估模型: XGBoost
2025-07-16 23:39:46 - enhanced_ensemble_selector - INFO -     XGBoost - 性能得分: 0.8060
2025-07-16 23:39:46 - enhanced_ensemble_selector - INFO -   评估模型: LightGBM
2025-07-16 23:39:48 - enhanced_ensemble_selector - INFO -     LightGBM - 性能得分: 0.7925
2025-07-16 23:39:48 - enhanced_ensemble_selector - INFO -   评估模型: CatBoost
2025-07-16 23:39:49 - enhanced_ensemble_selector - INFO -     CatBoost - 性能得分: 0.7574
2025-07-16 23:39:49 - enhanced_ensemble_selector - INFO -   评估模型: Logistic
2025-07-16 23:39:49 - enhanced_ensemble_selector - INFO -     Logistic - 性能得分: 0.7853
2025-07-16 23:39:49 - enhanced_ensemble_selector - INFO -   评估模型: SVM
2025-07-16 23:39:49 - enhanced_ensemble_selector - INFO -     SVM - 性能得分: 0.6824
2025-07-16 23:39:49 - enhanced_ensemble_selector - INFO -   评估模型: KNN
2025-07-16 23:39:49 - enhanced_ensemble_selector - INFO -     KNN - 性能得分: 0.6575
2025-07-16 23:39:49 - enhanced_ensemble_selector - INFO -   评估模型: NaiveBayes
2025-07-16 23:39:49 - enhanced_ensemble_selector - INFO -     NaiveBayes - 性能得分: 0.6875
2025-07-16 23:39:49 - enhanced_ensemble_selector - INFO -   评估模型: NeuralNet
2025-07-16 23:39:49 - enhanced_ensemble_selector - INFO -     NeuralNet - 性能得分: 0.8065
2025-07-16 23:39:49 - enhanced_ensemble_selector - INFO - 使用改进的多样性分析完成
2025-07-16 23:39:49 - enhanced_ensemble_selector - INFO - 数据复杂度: 0.264
2025-07-16 23:39:49 - enhanced_ensemble_selector - INFO - 调整后相关性阈值: 0.600
2025-07-16 23:39:49 - enhanced_ensemble_selector - INFO - 综合多样性得分: 0.517
2025-07-16 23:39:49 - enhanced_ensemble_selector - INFO -   DecisionTree vs RandomForest: 相关性=0.685, 多样性=0.315
2025-07-16 23:39:49 - enhanced_ensemble_selector - INFO -   DecisionTree vs XGBoost: 相关性=0.763, 多样性=0.237
2025-07-16 23:39:49 - enhanced_ensemble_selector - INFO -   DecisionTree vs LightGBM: 相关性=0.756, 多样性=0.244
2025-07-16 23:39:49 - enhanced_ensemble_selector - INFO -   DecisionTree vs CatBoost: 相关性=0.754, 多样性=0.246
2025-07-16 23:39:49 - enhanced_ensemble_selector - INFO -   DecisionTree vs Logistic: 相关性=0.547, 多样性=0.453
2025-07-16 23:39:49 - enhanced_ensemble_selector - INFO -   DecisionTree vs SVM: 相关性=0.605, 多样性=0.395
2025-07-16 23:39:49 - enhanced_ensemble_selector - INFO -   DecisionTree vs KNN: 相关性=0.478, 多样性=0.522
2025-07-16 23:39:49 - enhanced_ensemble_selector - INFO -   DecisionTree vs NaiveBayes: 相关性=0.332, 多样性=0.668
2025-07-16 23:39:49 - enhanced_ensemble_selector - INFO -   DecisionTree vs NeuralNet: 相关性=0.685, 多样性=0.315
2025-07-16 23:39:49 - enhanced_ensemble_selector - INFO -   RandomForest vs XGBoost: 相关性=0.936, 多样性=0.064
2025-07-16 23:39:49 - enhanced_ensemble_selector - INFO -   RandomForest vs LightGBM: 相关性=0.919, 多样性=0.081
2025-07-16 23:39:49 - enhanced_ensemble_selector - INFO -   RandomForest vs CatBoost: 相关性=0.952, 多样性=0.048
2025-07-16 23:39:49 - enhanced_ensemble_selector - INFO -   RandomForest vs Logistic: 相关性=0.731, 多样性=0.269
2025-07-16 23:39:49 - enhanced_ensemble_selector - INFO -   RandomForest vs SVM: 相关性=0.783, 多样性=0.217
2025-07-16 23:39:49 - enhanced_ensemble_selector - INFO -   RandomForest vs KNN: 相关性=0.688, 多样性=0.312
2025-07-16 23:39:49 - enhanced_ensemble_selector - INFO -   RandomForest vs NaiveBayes: 相关性=0.536, 多样性=0.464
2025-07-16 23:39:49 - enhanced_ensemble_selector - INFO -   RandomForest vs NeuralNet: 相关性=0.795, 多样性=0.205
2025-07-16 23:39:49 - enhanced_ensemble_selector - INFO -   XGBoost vs LightGBM: 相关性=0.970, 多样性=0.030
2025-07-16 23:39:49 - enhanced_ensemble_selector - INFO -   XGBoost vs CatBoost: 相关性=0.948, 多样性=0.052
2025-07-16 23:39:49 - enhanced_ensemble_selector - INFO -   XGBoost vs Logistic: 相关性=0.697, 多样性=0.303
2025-07-16 23:39:49 - enhanced_ensemble_selector - INFO -   XGBoost vs SVM: 相关性=0.705, 多样性=0.295
2025-07-16 23:39:49 - enhanced_ensemble_selector - INFO -   XGBoost vs KNN: 相关性=0.638, 多样性=0.362
2025-07-16 23:39:49 - enhanced_ensemble_selector - INFO -   XGBoost vs NaiveBayes: 相关性=0.522, 多样性=0.478
2025-07-16 23:39:49 - enhanced_ensemble_selector - INFO -   XGBoost vs NeuralNet: 相关性=0.825, 多样性=0.175
2025-07-16 23:39:49 - enhanced_ensemble_selector - INFO -   LightGBM vs CatBoost: 相关性=0.946, 多样性=0.054
2025-07-16 23:39:49 - enhanced_ensemble_selector - INFO -   LightGBM vs Logistic: 相关性=0.712, 多样性=0.288
2025-07-16 23:39:49 - enhanced_ensemble_selector - INFO -   LightGBM vs SVM: 相关性=0.704, 多样性=0.296
2025-07-16 23:39:49 - enhanced_ensemble_selector - INFO -   LightGBM vs KNN: 相关性=0.690, 多样性=0.310
2025-07-16 23:39:49 - enhanced_ensemble_selector - INFO -   LightGBM vs NaiveBayes: 相关性=0.569, 多样性=0.431
2025-07-16 23:39:49 - enhanced_ensemble_selector - INFO -   LightGBM vs NeuralNet: 相关性=0.857, 多样性=0.143
2025-07-16 23:39:49 - enhanced_ensemble_selector - INFO -   CatBoost vs Logistic: 相关性=0.787, 多样性=0.213
2025-07-16 23:39:49 - enhanced_ensemble_selector - INFO -   CatBoost vs SVM: 相关性=0.769, 多样性=0.231
2025-07-16 23:39:49 - enhanced_ensemble_selector - INFO -   CatBoost vs KNN: 相关性=0.719, 多样性=0.281
2025-07-16 23:39:49 - enhanced_ensemble_selector - INFO -   CatBoost vs NaiveBayes: 相关性=0.548, 多样性=0.452
2025-07-16 23:39:49 - enhanced_ensemble_selector - INFO -   CatBoost vs NeuralNet: 相关性=0.870, 多样性=0.130
2025-07-16 23:39:49 - enhanced_ensemble_selector - INFO -   Logistic vs SVM: 相关性=0.746, 多样性=0.254
2025-07-16 23:39:49 - enhanced_ensemble_selector - INFO -   Logistic vs KNN: 相关性=0.605, 多样性=0.395
2025-07-16 23:39:49 - enhanced_ensemble_selector - INFO -   Logistic vs NaiveBayes: 相关性=0.688, 多样性=0.312
2025-07-16 23:39:49 - enhanced_ensemble_selector - INFO -   Logistic vs NeuralNet: 相关性=0.706, 多样性=0.294
2025-07-16 23:39:49 - enhanced_ensemble_selector - INFO -   SVM vs KNN: 相关性=0.704, 多样性=0.296
2025-07-16 23:39:49 - enhanced_ensemble_selector - INFO -   SVM vs NaiveBayes: 相关性=0.534, 多样性=0.466
2025-07-16 23:39:49 - enhanced_ensemble_selector - INFO -   SVM vs NeuralNet: 相关性=0.702, 多样性=0.298
2025-07-16 23:39:49 - enhanced_ensemble_selector - INFO -   KNN vs NaiveBayes: 相关性=0.642, 多样性=0.358
2025-07-16 23:39:49 - enhanced_ensemble_selector - INFO -   KNN vs NeuralNet: 相关性=0.739, 多样性=0.261
2025-07-16 23:39:49 - enhanced_ensemble_selector - INFO -   NaiveBayes vs NeuralNet: 相关性=0.617, 多样性=0.383
2025-07-16 23:39:49 - enhanced_ensemble_selector - INFO - 特征重要性多样性: 0.577
2025-07-16 23:39:49 - enhanced_ensemble_selector - INFO - 算法类型多样性: 1.000
2025-07-16 23:39:49 - enhanced_ensemble_selector - INFO - 算法类型分布: {'tree': 5, 'linear': 2, 'instance': 1, 'probabilistic': 1, 'neural': 1}
2025-07-16 23:39:49 - enhanced_ensemble_selector - INFO - 开始量化多样性评估...
2025-07-16 23:39:50 - enhanced_ensemble_selector - INFO - 量化多样性评估完成:
2025-07-16 23:39:50 - enhanced_ensemble_selector - INFO -   Q统计量平均多样性: 0.432
2025-07-16 23:39:50 - enhanced_ensemble_selector - INFO -   多样性变异系数: 0.367
2025-07-16 23:39:50 - enhanced_ensemble_selector - INFO -   熵多样性: 0.489
2025-07-16 23:39:50 - enhanced_ensemble_selector - INFO - 从 10 个合格模型中选择 3 个进行集成
2025-07-16 23:39:50 - enhanced_ensemble_selector - INFO - 使用量化多样性评估进行模型选择...
2025-07-16 23:39:51 - enhanced_ensemble_selector - INFO - 量化评估选择完成:
2025-07-16 23:39:51 - enhanced_ensemble_selector - INFO -   最优组合: ['DecisionTree', 'Logistic', 'NaiveBayes']
2025-07-16 23:39:51 - enhanced_ensemble_selector - INFO -   性能得分: 0.7493
2025-07-16 23:39:51 - enhanced_ensemble_selector - INFO -   多样性得分: 0.6808
2025-07-16 23:39:51 - enhanced_ensemble_selector - INFO -   综合得分: 0.7151
2025-07-16 23:39:51 - enhanced_ensemble_selector - INFO -   多样性等级: 良好的多样性
2025-07-16 23:46:08 - enhanced_ensemble_selector - INFO - 开始评估 10 个基模型...
2025-07-16 23:46:08 - enhanced_ensemble_selector - INFO -   评估模型: DecisionTree
2025-07-16 23:46:08 - enhanced_ensemble_selector - INFO -     DecisionTree - 性能得分: 0.8461
2025-07-16 23:46:08 - enhanced_ensemble_selector - INFO -   评估模型: RandomForest
2025-07-16 23:46:09 - enhanced_ensemble_selector - INFO -     RandomForest - 性能得分: 0.8439
2025-07-16 23:46:09 - enhanced_ensemble_selector - INFO -   评估模型: XGBoost
2025-07-16 23:46:09 - enhanced_ensemble_selector - INFO -     XGBoost - 性能得分: 0.7959
2025-07-16 23:46:09 - enhanced_ensemble_selector - INFO -   评估模型: LightGBM
2025-07-16 23:46:09 - enhanced_ensemble_selector - INFO -     LightGBM - 性能得分: 0.7759
2025-07-16 23:46:09 - enhanced_ensemble_selector - INFO -   评估模型: CatBoost
2025-07-16 23:46:10 - enhanced_ensemble_selector - INFO -     CatBoost - 性能得分: 0.8828
2025-07-16 23:46:10 - enhanced_ensemble_selector - INFO -   评估模型: Logistic
2025-07-16 23:46:10 - enhanced_ensemble_selector - INFO -     Logistic - 性能得分: 0.8449
2025-07-16 23:46:10 - enhanced_ensemble_selector - INFO -   评估模型: SVM
2025-07-16 23:46:10 - enhanced_ensemble_selector - INFO -     SVM - 性能得分: 0.8892
2025-07-16 23:46:10 - enhanced_ensemble_selector - INFO -   评估模型: KNN
2025-07-16 23:46:10 - enhanced_ensemble_selector - INFO -     KNN - 性能得分: 0.8978
2025-07-16 23:46:10 - enhanced_ensemble_selector - INFO -   评估模型: NaiveBayes
2025-07-16 23:46:10 - enhanced_ensemble_selector - INFO -     NaiveBayes - 性能得分: 0.8831
2025-07-16 23:46:10 - enhanced_ensemble_selector - INFO -   评估模型: NeuralNet
2025-07-16 23:46:10 - enhanced_ensemble_selector - INFO -     NeuralNet - 性能得分: 0.8664
2025-07-16 23:46:10 - enhanced_ensemble_selector - INFO - 使用改进的多样性分析完成
2025-07-16 23:46:10 - enhanced_ensemble_selector - INFO - 数据复杂度: 0.232
2025-07-16 23:46:10 - enhanced_ensemble_selector - INFO - 调整后相关性阈值: 0.600
2025-07-16 23:46:10 - enhanced_ensemble_selector - INFO - 综合多样性得分: 0.402
2025-07-16 23:46:10 - enhanced_ensemble_selector - INFO -   DecisionTree vs RandomForest: 相关性=0.866, 多样性=0.134
2025-07-16 23:46:10 - enhanced_ensemble_selector - INFO -   DecisionTree vs XGBoost: 相关性=0.800, 多样性=0.200
2025-07-16 23:46:10 - enhanced_ensemble_selector - INFO -   DecisionTree vs LightGBM: 相关性=0.789, 多样性=0.211
2025-07-16 23:46:10 - enhanced_ensemble_selector - INFO -   DecisionTree vs CatBoost: 相关性=0.848, 多样性=0.152
2025-07-16 23:46:10 - enhanced_ensemble_selector - INFO -   DecisionTree vs Logistic: 相关性=0.733, 多样性=0.267
2025-07-16 23:46:10 - enhanced_ensemble_selector - INFO -   DecisionTree vs SVM: 相关性=0.722, 多样性=0.278
2025-07-16 23:46:10 - enhanced_ensemble_selector - INFO -   DecisionTree vs KNN: 相关性=0.726, 多样性=0.274
2025-07-16 23:46:10 - enhanced_ensemble_selector - INFO -   DecisionTree vs NaiveBayes: 相关性=0.708, 多样性=0.292
2025-07-16 23:46:10 - enhanced_ensemble_selector - INFO -   DecisionTree vs NeuralNet: 相关性=0.733, 多样性=0.267
2025-07-16 23:46:10 - enhanced_ensemble_selector - INFO -   RandomForest vs XGBoost: 相关性=0.906, 多样性=0.094
2025-07-16 23:46:10 - enhanced_ensemble_selector - INFO -   RandomForest vs LightGBM: 相关性=0.918, 多样性=0.082
2025-07-16 23:46:10 - enhanced_ensemble_selector - INFO -   RandomForest vs CatBoost: 相关性=0.977, 多样性=0.023
2025-07-16 23:46:10 - enhanced_ensemble_selector - INFO -   RandomForest vs Logistic: 相关性=0.903, 多样性=0.097
2025-07-16 23:46:10 - enhanced_ensemble_selector - INFO -   RandomForest vs SVM: 相关性=0.885, 多样性=0.115
2025-07-16 23:46:10 - enhanced_ensemble_selector - INFO -   RandomForest vs KNN: 相关性=0.887, 多样性=0.113
2025-07-16 23:46:10 - enhanced_ensemble_selector - INFO -   RandomForest vs NaiveBayes: 相关性=0.810, 多样性=0.190
2025-07-16 23:46:10 - enhanced_ensemble_selector - INFO -   RandomForest vs NeuralNet: 相关性=0.865, 多样性=0.135
2025-07-16 23:46:10 - enhanced_ensemble_selector - INFO -   XGBoost vs LightGBM: 相关性=0.956, 多样性=0.044
2025-07-16 23:46:10 - enhanced_ensemble_selector - INFO -   XGBoost vs CatBoost: 相关性=0.911, 多样性=0.089
2025-07-16 23:46:10 - enhanced_ensemble_selector - INFO -   XGBoost vs Logistic: 相关性=0.848, 多样性=0.152
2025-07-16 23:46:10 - enhanced_ensemble_selector - INFO -   XGBoost vs SVM: 相关性=0.758, 多样性=0.242
2025-07-16 23:46:10 - enhanced_ensemble_selector - INFO -   XGBoost vs KNN: 相关性=0.775, 多样性=0.225
2025-07-16 23:46:10 - enhanced_ensemble_selector - INFO -   XGBoost vs NaiveBayes: 相关性=0.702, 多样性=0.298
2025-07-16 23:46:10 - enhanced_ensemble_selector - INFO -   XGBoost vs NeuralNet: 相关性=0.786, 多样性=0.214
2025-07-16 23:46:10 - enhanced_ensemble_selector - INFO -   LightGBM vs CatBoost: 相关性=0.939, 多样性=0.061
2025-07-16 23:46:10 - enhanced_ensemble_selector - INFO -   LightGBM vs Logistic: 相关性=0.834, 多样性=0.166
2025-07-16 23:46:10 - enhanced_ensemble_selector - INFO -   LightGBM vs SVM: 相关性=0.769, 多样性=0.231
2025-07-16 23:46:10 - enhanced_ensemble_selector - INFO -   LightGBM vs KNN: 相关性=0.778, 多样性=0.222
2025-07-16 23:46:10 - enhanced_ensemble_selector - INFO -   LightGBM vs NaiveBayes: 相关性=0.670, 多样性=0.330
2025-07-16 23:46:10 - enhanced_ensemble_selector - INFO -   LightGBM vs NeuralNet: 相关性=0.796, 多样性=0.204
2025-07-16 23:46:10 - enhanced_ensemble_selector - INFO -   CatBoost vs Logistic: 相关性=0.910, 多样性=0.090
2025-07-16 23:46:10 - enhanced_ensemble_selector - INFO -   CatBoost vs SVM: 相关性=0.896, 多样性=0.104
2025-07-16 23:46:10 - enhanced_ensemble_selector - INFO -   CatBoost vs KNN: 相关性=0.898, 多样性=0.102
2025-07-16 23:46:10 - enhanced_ensemble_selector - INFO -   CatBoost vs NaiveBayes: 相关性=0.826, 多样性=0.174
2025-07-16 23:46:10 - enhanced_ensemble_selector - INFO -   CatBoost vs NeuralNet: 相关性=0.904, 多样性=0.096
2025-07-16 23:46:10 - enhanced_ensemble_selector - INFO -   Logistic vs SVM: 相关性=0.901, 多样性=0.099
2025-07-16 23:46:10 - enhanced_ensemble_selector - INFO -   Logistic vs KNN: 相关性=0.890, 多样性=0.110
2025-07-16 23:46:10 - enhanced_ensemble_selector - INFO -   Logistic vs NaiveBayes: 相关性=0.832, 多样性=0.168
2025-07-16 23:46:10 - enhanced_ensemble_selector - INFO -   Logistic vs NeuralNet: 相关性=0.945, 多样性=0.055
2025-07-16 23:46:10 - enhanced_ensemble_selector - INFO -   SVM vs KNN: 相关性=0.942, 多样性=0.058
2025-07-16 23:46:10 - enhanced_ensemble_selector - INFO -   SVM vs NaiveBayes: 相关性=0.870, 多样性=0.130
2025-07-16 23:46:10 - enhanced_ensemble_selector - INFO -   SVM vs NeuralNet: 相关性=0.908, 多样性=0.092
2025-07-16 23:46:10 - enhanced_ensemble_selector - INFO -   KNN vs NaiveBayes: 相关性=0.959, 多样性=0.041
2025-07-16 23:46:10 - enhanced_ensemble_selector - INFO -   KNN vs NeuralNet: 相关性=0.866, 多样性=0.134
2025-07-16 23:46:10 - enhanced_ensemble_selector - INFO -   NaiveBayes vs NeuralNet: 相关性=0.796, 多样性=0.204
2025-07-16 23:46:10 - enhanced_ensemble_selector - INFO - 特征重要性多样性: 0.413
2025-07-16 23:46:10 - enhanced_ensemble_selector - INFO - 算法类型多样性: 1.000
2025-07-16 23:46:10 - enhanced_ensemble_selector - INFO - 算法类型分布: {'tree': 5, 'linear': 2, 'instance': 1, 'probabilistic': 1, 'neural': 1}
2025-07-16 23:46:10 - enhanced_ensemble_selector - INFO - 开始量化多样性评估...
2025-07-16 23:46:10 - enhanced_ensemble_selector - INFO - 量化多样性评估完成:
2025-07-16 23:46:10 - enhanced_ensemble_selector - INFO -   Q统计量平均多样性: 0.351
2025-07-16 23:46:10 - enhanced_ensemble_selector - INFO -   多样性变异系数: 0.424
2025-07-16 23:46:10 - enhanced_ensemble_selector - INFO -   熵多样性: 0.299
2025-07-16 23:46:10 - enhanced_ensemble_selector - INFO - 从 10 个合格模型中选择 3 个进行集成
2025-07-16 23:46:10 - enhanced_ensemble_selector - INFO - 使用量化多样性评估进行模型选择...
2025-07-16 23:46:11 - enhanced_ensemble_selector - INFO - 量化评估选择完成:
2025-07-16 23:46:11 - enhanced_ensemble_selector - INFO -   最优组合: ['DecisionTree', 'LightGBM', 'NeuralNet']
2025-07-16 23:46:11 - enhanced_ensemble_selector - INFO -   性能得分: 0.8295
2025-07-16 23:46:11 - enhanced_ensemble_selector - INFO -   多样性得分: 0.5795
2025-07-16 23:46:11 - enhanced_ensemble_selector - INFO -   综合得分: 0.7045
2025-07-16 23:46:11 - enhanced_ensemble_selector - INFO -   多样性等级: 良好的多样性
2025-07-16 23:47:44 - enhanced_ensemble_selector - INFO - 开始评估 10 个基模型...
2025-07-16 23:47:44 - enhanced_ensemble_selector - INFO -   评估模型: DecisionTree
2025-07-16 23:47:44 - enhanced_ensemble_selector - INFO -     DecisionTree - 性能得分: 0.8233
2025-07-16 23:47:44 - enhanced_ensemble_selector - INFO -   评估模型: RandomForest
2025-07-16 23:47:44 - enhanced_ensemble_selector - INFO -     RandomForest - 性能得分: 0.9097
2025-07-16 23:47:44 - enhanced_ensemble_selector - INFO -   评估模型: XGBoost
2025-07-16 23:47:44 - enhanced_ensemble_selector - INFO -     XGBoost - 性能得分: 0.8978
2025-07-16 23:47:44 - enhanced_ensemble_selector - INFO -   评估模型: LightGBM
2025-07-16 23:47:44 - enhanced_ensemble_selector - INFO -     LightGBM - 性能得分: 0.8927
2025-07-16 23:47:44 - enhanced_ensemble_selector - INFO -   评估模型: CatBoost
2025-07-16 23:47:45 - enhanced_ensemble_selector - INFO -     CatBoost - 性能得分: 0.9148
2025-07-16 23:47:45 - enhanced_ensemble_selector - INFO -   评估模型: Logistic
2025-07-16 23:47:45 - enhanced_ensemble_selector - INFO -     Logistic - 性能得分: 0.8694
2025-07-16 23:47:45 - enhanced_ensemble_selector - INFO -   评估模型: SVM
2025-07-16 23:47:45 - enhanced_ensemble_selector - INFO -     SVM - 性能得分: 0.8297
2025-07-16 23:47:45 - enhanced_ensemble_selector - INFO -   评估模型: KNN
2025-07-16 23:47:45 - enhanced_ensemble_selector - INFO -     KNN - 性能得分: 0.8892
2025-07-16 23:47:45 - enhanced_ensemble_selector - INFO -   评估模型: NaiveBayes
2025-07-16 23:47:45 - enhanced_ensemble_selector - INFO -     NaiveBayes - 性能得分: 0.8809
2025-07-16 23:47:45 - enhanced_ensemble_selector - INFO -   评估模型: NeuralNet
2025-07-16 23:47:45 - enhanced_ensemble_selector - INFO -     NeuralNet - 性能得分: 0.8959
2025-07-16 23:47:45 - enhanced_ensemble_selector - INFO - 使用改进的多样性分析完成
2025-07-16 23:47:45 - enhanced_ensemble_selector - INFO - 数据复杂度: 0.246
2025-07-16 23:47:45 - enhanced_ensemble_selector - INFO - 调整后相关性阈值: 0.600
2025-07-16 23:47:45 - enhanced_ensemble_selector - INFO - 综合多样性得分: 0.374
2025-07-16 23:47:45 - enhanced_ensemble_selector - INFO -   DecisionTree vs RandomForest: 相关性=0.806, 多样性=0.194
2025-07-16 23:47:45 - enhanced_ensemble_selector - INFO -   DecisionTree vs XGBoost: 相关性=0.843, 多样性=0.157
2025-07-16 23:47:45 - enhanced_ensemble_selector - INFO -   DecisionTree vs LightGBM: 相关性=0.784, 多样性=0.216
2025-07-16 23:47:45 - enhanced_ensemble_selector - INFO -   DecisionTree vs CatBoost: 相关性=0.803, 多样性=0.197
2025-07-16 23:47:45 - enhanced_ensemble_selector - INFO -   DecisionTree vs Logistic: 相关性=0.673, 多样性=0.327
2025-07-16 23:47:45 - enhanced_ensemble_selector - INFO -   DecisionTree vs SVM: 相关性=0.699, 多样性=0.301
2025-07-16 23:47:45 - enhanced_ensemble_selector - INFO -   DecisionTree vs KNN: 相关性=0.696, 多样性=0.304
2025-07-16 23:47:45 - enhanced_ensemble_selector - INFO -   DecisionTree vs NaiveBayes: 相关性=0.659, 多样性=0.341
2025-07-16 23:47:45 - enhanced_ensemble_selector - INFO -   DecisionTree vs NeuralNet: 相关性=0.643, 多样性=0.357
2025-07-16 23:47:45 - enhanced_ensemble_selector - INFO -   RandomForest vs XGBoost: 相关性=0.955, 多样性=0.045
2025-07-16 23:47:45 - enhanced_ensemble_selector - INFO -   RandomForest vs LightGBM: 相关性=0.950, 多样性=0.050
2025-07-16 23:47:45 - enhanced_ensemble_selector - INFO -   RandomForest vs CatBoost: 相关性=0.986, 多样性=0.014
2025-07-16 23:47:45 - enhanced_ensemble_selector - INFO -   RandomForest vs Logistic: 相关性=0.887, 多样性=0.113
2025-07-16 23:47:45 - enhanced_ensemble_selector - INFO -   RandomForest vs SVM: 相关性=0.936, 多样性=0.064
2025-07-16 23:47:45 - enhanced_ensemble_selector - INFO -   RandomForest vs KNN: 相关性=0.912, 多样性=0.088
2025-07-16 23:47:45 - enhanced_ensemble_selector - INFO -   RandomForest vs NaiveBayes: 相关性=0.861, 多样性=0.139
2025-07-16 23:47:45 - enhanced_ensemble_selector - INFO -   RandomForest vs NeuralNet: 相关性=0.886, 多样性=0.114
2025-07-16 23:47:45 - enhanced_ensemble_selector - INFO -   XGBoost vs LightGBM: 相关性=0.971, 多样性=0.029
2025-07-16 23:47:45 - enhanced_ensemble_selector - INFO -   XGBoost vs CatBoost: 相关性=0.981, 多样性=0.019
2025-07-16 23:47:45 - enhanced_ensemble_selector - INFO -   XGBoost vs Logistic: 相关性=0.814, 多样性=0.186
2025-07-16 23:47:45 - enhanced_ensemble_selector - INFO -   XGBoost vs SVM: 相关性=0.888, 多样性=0.112
2025-07-16 23:47:45 - enhanced_ensemble_selector - INFO -   XGBoost vs KNN: 相关性=0.857, 多样性=0.143
2025-07-16 23:47:45 - enhanced_ensemble_selector - INFO -   XGBoost vs NaiveBayes: 相关性=0.822, 多样性=0.178
2025-07-16 23:47:45 - enhanced_ensemble_selector - INFO -   XGBoost vs NeuralNet: 相关性=0.838, 多样性=0.162
2025-07-16 23:47:45 - enhanced_ensemble_selector - INFO -   LightGBM vs CatBoost: 相关性=0.981, 多样性=0.019
2025-07-16 23:47:45 - enhanced_ensemble_selector - INFO -   LightGBM vs Logistic: 相关性=0.819, 多样性=0.181
2025-07-16 23:47:45 - enhanced_ensemble_selector - INFO -   LightGBM vs SVM: 相关性=0.895, 多样性=0.105
2025-07-16 23:47:45 - enhanced_ensemble_selector - INFO -   LightGBM vs KNN: 相关性=0.876, 多样性=0.124
2025-07-16 23:47:45 - enhanced_ensemble_selector - INFO -   LightGBM vs NaiveBayes: 相关性=0.797, 多样性=0.203
2025-07-16 23:47:45 - enhanced_ensemble_selector - INFO -   LightGBM vs NeuralNet: 相关性=0.882, 多样性=0.118
2025-07-16 23:47:45 - enhanced_ensemble_selector - INFO -   CatBoost vs Logistic: 相关性=0.865, 多样性=0.135
2025-07-16 23:47:45 - enhanced_ensemble_selector - INFO -   CatBoost vs SVM: 相关性=0.924, 多样性=0.076
2025-07-16 23:47:45 - enhanced_ensemble_selector - INFO -   CatBoost vs KNN: 相关性=0.907, 多样性=0.093
2025-07-16 23:47:45 - enhanced_ensemble_selector - INFO -   CatBoost vs NaiveBayes: 相关性=0.843, 多样性=0.157
2025-07-16 23:47:45 - enhanced_ensemble_selector - INFO -   CatBoost vs NeuralNet: 相关性=0.892, 多样性=0.108
2025-07-16 23:47:45 - enhanced_ensemble_selector - INFO -   Logistic vs SVM: 相关性=0.899, 多样性=0.101
2025-07-16 23:47:45 - enhanced_ensemble_selector - INFO -   Logistic vs KNN: 相关性=0.963, 多样性=0.037
2025-07-16 23:47:45 - enhanced_ensemble_selector - INFO -   Logistic vs NaiveBayes: 相关性=0.954, 多样性=0.046
2025-07-16 23:47:45 - enhanced_ensemble_selector - INFO -   Logistic vs NeuralNet: 相关性=0.936, 多样性=0.064
2025-07-16 23:47:45 - enhanced_ensemble_selector - INFO -   SVM vs KNN: 相关性=0.924, 多样性=0.076
2025-07-16 23:47:45 - enhanced_ensemble_selector - INFO -   SVM vs NaiveBayes: 相关性=0.874, 多样性=0.126
2025-07-16 23:47:45 - enhanced_ensemble_selector - INFO -   SVM vs NeuralNet: 相关性=0.894, 多样性=0.106
2025-07-16 23:47:45 - enhanced_ensemble_selector - INFO -   KNN vs NaiveBayes: 相关性=0.910, 多样性=0.090
2025-07-16 23:47:45 - enhanced_ensemble_selector - INFO -   KNN vs NeuralNet: 相关性=0.939, 多样性=0.061
2025-07-16 23:47:45 - enhanced_ensemble_selector - INFO -   NaiveBayes vs NeuralNet: 相关性=0.897, 多样性=0.103
2025-07-16 23:47:45 - enhanced_ensemble_selector - INFO - 特征重要性多样性: 0.359
2025-07-16 23:47:45 - enhanced_ensemble_selector - INFO - 算法类型多样性: 1.000
2025-07-16 23:47:45 - enhanced_ensemble_selector - INFO - 算法类型分布: {'tree': 5, 'linear': 2, 'instance': 1, 'probabilistic': 1, 'neural': 1}
2025-07-16 23:47:45 - enhanced_ensemble_selector - INFO - 开始量化多样性评估...
2025-07-16 23:47:46 - enhanced_ensemble_selector - INFO - 量化多样性评估完成:
2025-07-16 23:47:46 - enhanced_ensemble_selector - INFO -   Q统计量平均多样性: 0.297
2025-07-16 23:47:46 - enhanced_ensemble_selector - INFO -   多样性变异系数: 0.301
2025-07-16 23:47:46 - enhanced_ensemble_selector - INFO -   熵多样性: 0.215
2025-07-16 23:47:46 - enhanced_ensemble_selector - INFO - 从 10 个合格模型中选择 3 个进行集成
2025-07-16 23:47:46 - enhanced_ensemble_selector - INFO - 使用量化多样性评估进行模型选择...
2025-07-16 23:47:47 - enhanced_ensemble_selector - INFO - 量化评估选择完成:
2025-07-16 23:47:47 - enhanced_ensemble_selector - INFO -   最优组合: ['DecisionTree', 'KNN', 'NeuralNet']
2025-07-16 23:47:47 - enhanced_ensemble_selector - INFO -   性能得分: 0.8694
2025-07-16 23:47:47 - enhanced_ensemble_selector - INFO -   多样性得分: 0.4692
2025-07-16 23:47:47 - enhanced_ensemble_selector - INFO -   综合得分: 0.6693
2025-07-16 23:47:47 - enhanced_ensemble_selector - INFO -   多样性等级: 中等的多样性
2025-07-16 23:54:18 - enhanced_ensemble_selector - INFO - 开始评估 10 个基模型...
2025-07-16 23:54:18 - enhanced_ensemble_selector - INFO -   评估模型: DecisionTree
2025-07-16 23:54:18 - enhanced_ensemble_selector - INFO -     DecisionTree - 性能得分: 0.8511
2025-07-16 23:54:18 - enhanced_ensemble_selector - INFO -   评估模型: RandomForest
2025-07-16 23:54:18 - enhanced_ensemble_selector - INFO -     RandomForest - 性能得分: 0.8626
2025-07-16 23:54:18 - enhanced_ensemble_selector - INFO -   评估模型: XGBoost
2025-07-16 23:54:18 - enhanced_ensemble_selector - INFO -     XGBoost - 性能得分: 0.8137
2025-07-16 23:54:18 - enhanced_ensemble_selector - INFO -   评估模型: LightGBM
2025-07-16 23:54:20 - enhanced_ensemble_selector - INFO -     LightGBM - 性能得分: 0.8165
2025-07-16 23:54:20 - enhanced_ensemble_selector - INFO -   评估模型: CatBoost
2025-07-16 23:54:21 - enhanced_ensemble_selector - INFO -     CatBoost - 性能得分: 0.8053
2025-07-16 23:54:21 - enhanced_ensemble_selector - INFO -   评估模型: Logistic
2025-07-16 23:54:21 - enhanced_ensemble_selector - INFO -     Logistic - 性能得分: 0.8383
2025-07-16 23:54:21 - enhanced_ensemble_selector - INFO -   评估模型: SVM
2025-07-16 23:54:21 - enhanced_ensemble_selector - INFO -     SVM - 性能得分: 0.8813
2025-07-16 23:54:21 - enhanced_ensemble_selector - INFO -   评估模型: KNN
2025-07-16 23:54:21 - enhanced_ensemble_selector - INFO -     KNN - 性能得分: 0.8329
2025-07-16 23:54:21 - enhanced_ensemble_selector - INFO -   评估模型: NaiveBayes
2025-07-16 23:54:21 - enhanced_ensemble_selector - INFO -     NaiveBayes - 性能得分: 0.7733
2025-07-16 23:54:21 - enhanced_ensemble_selector - INFO -   评估模型: NeuralNet
2025-07-16 23:54:21 - enhanced_ensemble_selector - INFO -     NeuralNet - 性能得分: 0.7655
2025-07-16 23:54:21 - enhanced_ensemble_selector - INFO - 使用改进的多样性分析完成
2025-07-16 23:54:21 - enhanced_ensemble_selector - INFO - 数据复杂度: 0.251
2025-07-16 23:54:21 - enhanced_ensemble_selector - INFO - 调整后相关性阈值: 0.600
2025-07-16 23:54:21 - enhanced_ensemble_selector - INFO - 综合多样性得分: 0.440
2025-07-16 23:54:21 - enhanced_ensemble_selector - INFO -   DecisionTree vs RandomForest: 相关性=0.840, 多样性=0.160
2025-07-16 23:54:21 - enhanced_ensemble_selector - INFO -   DecisionTree vs XGBoost: 相关性=0.811, 多样性=0.189
2025-07-16 23:54:21 - enhanced_ensemble_selector - INFO -   DecisionTree vs LightGBM: 相关性=0.797, 多样性=0.203
2025-07-16 23:54:21 - enhanced_ensemble_selector - INFO -   DecisionTree vs CatBoost: 相关性=0.830, 多样性=0.170
2025-07-16 23:54:21 - enhanced_ensemble_selector - INFO -   DecisionTree vs Logistic: 相关性=0.653, 多样性=0.347
2025-07-16 23:54:21 - enhanced_ensemble_selector - INFO -   DecisionTree vs SVM: 相关性=0.746, 多样性=0.254
2025-07-16 23:54:21 - enhanced_ensemble_selector - INFO -   DecisionTree vs KNN: 相关性=0.591, 多样性=0.409
2025-07-16 23:54:21 - enhanced_ensemble_selector - INFO -   DecisionTree vs NaiveBayes: 相关性=0.473, 多样性=0.527
2025-07-16 23:54:21 - enhanced_ensemble_selector - INFO -   DecisionTree vs NeuralNet: 相关性=0.529, 多样性=0.471
2025-07-16 23:54:21 - enhanced_ensemble_selector - INFO -   RandomForest vs XGBoost: 相关性=0.909, 多样性=0.091
2025-07-16 23:54:21 - enhanced_ensemble_selector - INFO -   RandomForest vs LightGBM: 相关性=0.929, 多样性=0.071
2025-07-16 23:54:21 - enhanced_ensemble_selector - INFO -   RandomForest vs CatBoost: 相关性=0.971, 多样性=0.029
2025-07-16 23:54:21 - enhanced_ensemble_selector - INFO -   RandomForest vs Logistic: 相关性=0.872, 多样性=0.128
2025-07-16 23:54:21 - enhanced_ensemble_selector - INFO -   RandomForest vs SVM: 相关性=0.923, 多样性=0.077
2025-07-16 23:54:21 - enhanced_ensemble_selector - INFO -   RandomForest vs KNN: 相关性=0.745, 多样性=0.255
2025-07-16 23:54:21 - enhanced_ensemble_selector - INFO -   RandomForest vs NaiveBayes: 相关性=0.742, 多样性=0.258
2025-07-16 23:54:21 - enhanced_ensemble_selector - INFO -   RandomForest vs NeuralNet: 相关性=0.632, 多样性=0.368
2025-07-16 23:54:21 - enhanced_ensemble_selector - INFO -   XGBoost vs LightGBM: 相关性=0.983, 多样性=0.017
2025-07-16 23:54:21 - enhanced_ensemble_selector - INFO -   XGBoost vs CatBoost: 相关性=0.951, 多样性=0.049
2025-07-16 23:54:21 - enhanced_ensemble_selector - INFO -   XGBoost vs Logistic: 相关性=0.804, 多样性=0.196
2025-07-16 23:54:21 - enhanced_ensemble_selector - INFO -   XGBoost vs SVM: 相关性=0.825, 多样性=0.175
2025-07-16 23:54:21 - enhanced_ensemble_selector - INFO -   XGBoost vs KNN: 相关性=0.617, 多样性=0.383
2025-07-16 23:54:21 - enhanced_ensemble_selector - INFO -   XGBoost vs NaiveBayes: 相关性=0.631, 多样性=0.369
2025-07-16 23:54:21 - enhanced_ensemble_selector - INFO -   XGBoost vs NeuralNet: 相关性=0.652, 多样性=0.348
2025-07-16 23:54:21 - enhanced_ensemble_selector - INFO -   LightGBM vs CatBoost: 相关性=0.967, 多样性=0.033
2025-07-16 23:54:21 - enhanced_ensemble_selector - INFO -   LightGBM vs Logistic: 相关性=0.834, 多样性=0.166
2025-07-16 23:54:21 - enhanced_ensemble_selector - INFO -   LightGBM vs SVM: 相关性=0.868, 多样性=0.132
2025-07-16 23:54:21 - enhanced_ensemble_selector - INFO -   LightGBM vs KNN: 相关性=0.681, 多样性=0.319
2025-07-16 23:54:21 - enhanced_ensemble_selector - INFO -   LightGBM vs NaiveBayes: 相关性=0.699, 多样性=0.301
2025-07-16 23:54:21 - enhanced_ensemble_selector - INFO -   LightGBM vs NeuralNet: 相关性=0.652, 多样性=0.348
2025-07-16 23:54:21 - enhanced_ensemble_selector - INFO -   CatBoost vs Logistic: 相关性=0.875, 多样性=0.125
2025-07-16 23:54:21 - enhanced_ensemble_selector - INFO -   CatBoost vs SVM: 相关性=0.912, 多样性=0.088
2025-07-16 23:54:21 - enhanced_ensemble_selector - INFO -   CatBoost vs KNN: 相关性=0.753, 多样性=0.247
2025-07-16 23:54:21 - enhanced_ensemble_selector - INFO -   CatBoost vs NaiveBayes: 相关性=0.716, 多样性=0.284
2025-07-16 23:54:21 - enhanced_ensemble_selector - INFO -   CatBoost vs NeuralNet: 相关性=0.727, 多样性=0.273
2025-07-16 23:54:21 - enhanced_ensemble_selector - INFO -   Logistic vs SVM: 相关性=0.877, 多样性=0.123
2025-07-16 23:54:21 - enhanced_ensemble_selector - INFO -   Logistic vs KNN: 相关性=0.746, 多样性=0.254
2025-07-16 23:54:21 - enhanced_ensemble_selector - INFO -   Logistic vs NaiveBayes: 相关性=0.873, 多样性=0.127
2025-07-16 23:54:21 - enhanced_ensemble_selector - INFO -   Logistic vs NeuralNet: 相关性=0.568, 多样性=0.432
2025-07-16 23:54:21 - enhanced_ensemble_selector - INFO -   SVM vs KNN: 相关性=0.845, 多样性=0.155
2025-07-16 23:54:21 - enhanced_ensemble_selector - INFO -   SVM vs NaiveBayes: 相关性=0.834, 多样性=0.166
2025-07-16 23:54:21 - enhanced_ensemble_selector - INFO -   SVM vs NeuralNet: 相关性=0.653, 多样性=0.347
2025-07-16 23:54:21 - enhanced_ensemble_selector - INFO -   KNN vs NaiveBayes: 相关性=0.741, 多样性=0.259
2025-07-16 23:54:21 - enhanced_ensemble_selector - INFO -   KNN vs NeuralNet: 相关性=0.681, 多样性=0.319
2025-07-16 23:54:21 - enhanced_ensemble_selector - INFO -   NaiveBayes vs NeuralNet: 相关性=0.431, 多样性=0.569
2025-07-16 23:54:21 - enhanced_ensemble_selector - INFO - 特征重要性多样性: 0.406
2025-07-16 23:54:21 - enhanced_ensemble_selector - INFO - 算法类型多样性: 1.000
2025-07-16 23:54:21 - enhanced_ensemble_selector - INFO - 算法类型分布: {'tree': 5, 'linear': 2, 'instance': 1, 'probabilistic': 1, 'neural': 1}
2025-07-16 23:54:21 - enhanced_ensemble_selector - INFO - 开始量化多样性评估...
2025-07-16 23:54:22 - enhanced_ensemble_selector - INFO - 量化多样性评估完成:
2025-07-16 23:54:22 - enhanced_ensemble_selector - INFO -   Q统计量平均多样性: 0.361
2025-07-16 23:54:22 - enhanced_ensemble_selector - INFO -   多样性变异系数: 0.347
2025-07-16 23:54:22 - enhanced_ensemble_selector - INFO -   熵多样性: 0.315
2025-07-16 23:54:22 - enhanced_ensemble_selector - INFO - 从 10 个合格模型中选择 3 个进行集成
2025-07-16 23:54:22 - enhanced_ensemble_selector - INFO - 使用量化多样性评估进行模型选择...
2025-07-16 23:54:23 - enhanced_ensemble_selector - INFO - 量化评估选择完成:
2025-07-16 23:54:23 - enhanced_ensemble_selector - INFO -   最优组合: ['DecisionTree', 'KNN', 'NeuralNet']
2025-07-16 23:54:23 - enhanced_ensemble_selector - INFO -   性能得分: 0.8165
2025-07-16 23:54:23 - enhanced_ensemble_selector - INFO -   多样性得分: 0.5290
2025-07-16 23:54:23 - enhanced_ensemble_selector - INFO -   综合得分: 0.6728
2025-07-16 23:54:23 - enhanced_ensemble_selector - INFO -   多样性等级: 良好的多样性
2025-07-17 00:21:37 - enhanced_ensemble_selector - INFO - 开始评估 10 个基模型...
2025-07-17 00:21:37 - enhanced_ensemble_selector - INFO -   评估模型: DecisionTree
2025-07-17 00:21:37 - enhanced_ensemble_selector - INFO -     DecisionTree - 性能得分: 0.8511
2025-07-17 00:21:37 - enhanced_ensemble_selector - INFO -   评估模型: RandomForest
2025-07-17 00:21:37 - enhanced_ensemble_selector - INFO -     RandomForest - 性能得分: 0.8626
2025-07-17 00:21:37 - enhanced_ensemble_selector - INFO -   评估模型: XGBoost
2025-07-17 00:21:37 - enhanced_ensemble_selector - INFO -     XGBoost - 性能得分: 0.8137
2025-07-17 00:21:37 - enhanced_ensemble_selector - INFO -   评估模型: LightGBM
2025-07-17 00:21:38 - enhanced_ensemble_selector - INFO -     LightGBM - 性能得分: 0.8165
2025-07-17 00:21:38 - enhanced_ensemble_selector - INFO -   评估模型: CatBoost
2025-07-17 00:21:39 - enhanced_ensemble_selector - INFO -     CatBoost - 性能得分: 0.8053
2025-07-17 00:21:39 - enhanced_ensemble_selector - INFO -   评估模型: Logistic
2025-07-17 00:21:39 - enhanced_ensemble_selector - INFO -     Logistic - 性能得分: 0.8383
2025-07-17 00:21:39 - enhanced_ensemble_selector - INFO -   评估模型: SVM
2025-07-17 00:21:39 - enhanced_ensemble_selector - INFO -     SVM - 性能得分: 0.8813
2025-07-17 00:21:39 - enhanced_ensemble_selector - INFO -   评估模型: KNN
2025-07-17 00:21:39 - enhanced_ensemble_selector - INFO -     KNN - 性能得分: 0.8329
2025-07-17 00:21:39 - enhanced_ensemble_selector - INFO -   评估模型: NaiveBayes
2025-07-17 00:21:39 - enhanced_ensemble_selector - INFO -     NaiveBayes - 性能得分: 0.7733
2025-07-17 00:21:39 - enhanced_ensemble_selector - INFO -   评估模型: NeuralNet
2025-07-17 00:21:39 - enhanced_ensemble_selector - INFO -     NeuralNet - 性能得分: 0.7655
2025-07-17 00:21:39 - enhanced_ensemble_selector - INFO - 使用改进的多样性分析完成
2025-07-17 00:21:39 - enhanced_ensemble_selector - INFO - 数据复杂度: 0.251
2025-07-17 00:21:39 - enhanced_ensemble_selector - INFO - 调整后相关性阈值: 0.600
2025-07-17 00:21:39 - enhanced_ensemble_selector - INFO - 综合多样性得分: 0.440
2025-07-17 00:21:39 - enhanced_ensemble_selector - INFO -   DecisionTree vs RandomForest: 相关性=0.840, 多样性=0.160
2025-07-17 00:21:39 - enhanced_ensemble_selector - INFO -   DecisionTree vs XGBoost: 相关性=0.811, 多样性=0.189
2025-07-17 00:21:39 - enhanced_ensemble_selector - INFO -   DecisionTree vs LightGBM: 相关性=0.797, 多样性=0.203
2025-07-17 00:21:39 - enhanced_ensemble_selector - INFO -   DecisionTree vs CatBoost: 相关性=0.830, 多样性=0.170
2025-07-17 00:21:39 - enhanced_ensemble_selector - INFO -   DecisionTree vs Logistic: 相关性=0.653, 多样性=0.347
2025-07-17 00:21:39 - enhanced_ensemble_selector - INFO -   DecisionTree vs SVM: 相关性=0.746, 多样性=0.254
2025-07-17 00:21:39 - enhanced_ensemble_selector - INFO -   DecisionTree vs KNN: 相关性=0.591, 多样性=0.409
2025-07-17 00:21:39 - enhanced_ensemble_selector - INFO -   DecisionTree vs NaiveBayes: 相关性=0.473, 多样性=0.527
2025-07-17 00:21:39 - enhanced_ensemble_selector - INFO -   DecisionTree vs NeuralNet: 相关性=0.529, 多样性=0.471
2025-07-17 00:21:39 - enhanced_ensemble_selector - INFO -   RandomForest vs XGBoost: 相关性=0.909, 多样性=0.091
2025-07-17 00:21:39 - enhanced_ensemble_selector - INFO -   RandomForest vs LightGBM: 相关性=0.929, 多样性=0.071
2025-07-17 00:21:39 - enhanced_ensemble_selector - INFO -   RandomForest vs CatBoost: 相关性=0.971, 多样性=0.029
2025-07-17 00:21:39 - enhanced_ensemble_selector - INFO -   RandomForest vs Logistic: 相关性=0.872, 多样性=0.128
2025-07-17 00:21:39 - enhanced_ensemble_selector - INFO -   RandomForest vs SVM: 相关性=0.923, 多样性=0.077
2025-07-17 00:21:39 - enhanced_ensemble_selector - INFO -   RandomForest vs KNN: 相关性=0.745, 多样性=0.255
2025-07-17 00:21:39 - enhanced_ensemble_selector - INFO -   RandomForest vs NaiveBayes: 相关性=0.742, 多样性=0.258
2025-07-17 00:21:39 - enhanced_ensemble_selector - INFO -   RandomForest vs NeuralNet: 相关性=0.632, 多样性=0.368
2025-07-17 00:21:39 - enhanced_ensemble_selector - INFO -   XGBoost vs LightGBM: 相关性=0.983, 多样性=0.017
2025-07-17 00:21:39 - enhanced_ensemble_selector - INFO -   XGBoost vs CatBoost: 相关性=0.951, 多样性=0.049
2025-07-17 00:21:39 - enhanced_ensemble_selector - INFO -   XGBoost vs Logistic: 相关性=0.804, 多样性=0.196
2025-07-17 00:21:39 - enhanced_ensemble_selector - INFO -   XGBoost vs SVM: 相关性=0.825, 多样性=0.175
2025-07-17 00:21:39 - enhanced_ensemble_selector - INFO -   XGBoost vs KNN: 相关性=0.617, 多样性=0.383
2025-07-17 00:21:39 - enhanced_ensemble_selector - INFO -   XGBoost vs NaiveBayes: 相关性=0.631, 多样性=0.369
2025-07-17 00:21:39 - enhanced_ensemble_selector - INFO -   XGBoost vs NeuralNet: 相关性=0.652, 多样性=0.348
2025-07-17 00:21:39 - enhanced_ensemble_selector - INFO -   LightGBM vs CatBoost: 相关性=0.967, 多样性=0.033
2025-07-17 00:21:39 - enhanced_ensemble_selector - INFO -   LightGBM vs Logistic: 相关性=0.834, 多样性=0.166
2025-07-17 00:21:39 - enhanced_ensemble_selector - INFO -   LightGBM vs SVM: 相关性=0.868, 多样性=0.132
2025-07-17 00:21:39 - enhanced_ensemble_selector - INFO -   LightGBM vs KNN: 相关性=0.681, 多样性=0.319
2025-07-17 00:21:39 - enhanced_ensemble_selector - INFO -   LightGBM vs NaiveBayes: 相关性=0.699, 多样性=0.301
2025-07-17 00:21:39 - enhanced_ensemble_selector - INFO -   LightGBM vs NeuralNet: 相关性=0.652, 多样性=0.348
2025-07-17 00:21:39 - enhanced_ensemble_selector - INFO -   CatBoost vs Logistic: 相关性=0.875, 多样性=0.125
2025-07-17 00:21:39 - enhanced_ensemble_selector - INFO -   CatBoost vs SVM: 相关性=0.912, 多样性=0.088
2025-07-17 00:21:39 - enhanced_ensemble_selector - INFO -   CatBoost vs KNN: 相关性=0.753, 多样性=0.247
2025-07-17 00:21:39 - enhanced_ensemble_selector - INFO -   CatBoost vs NaiveBayes: 相关性=0.716, 多样性=0.284
2025-07-17 00:21:39 - enhanced_ensemble_selector - INFO -   CatBoost vs NeuralNet: 相关性=0.727, 多样性=0.273
2025-07-17 00:21:39 - enhanced_ensemble_selector - INFO -   Logistic vs SVM: 相关性=0.877, 多样性=0.123
2025-07-17 00:21:39 - enhanced_ensemble_selector - INFO -   Logistic vs KNN: 相关性=0.746, 多样性=0.254
2025-07-17 00:21:39 - enhanced_ensemble_selector - INFO -   Logistic vs NaiveBayes: 相关性=0.873, 多样性=0.127
2025-07-17 00:21:39 - enhanced_ensemble_selector - INFO -   Logistic vs NeuralNet: 相关性=0.568, 多样性=0.432
2025-07-17 00:21:39 - enhanced_ensemble_selector - INFO -   SVM vs KNN: 相关性=0.845, 多样性=0.155
2025-07-17 00:21:39 - enhanced_ensemble_selector - INFO -   SVM vs NaiveBayes: 相关性=0.834, 多样性=0.166
2025-07-17 00:21:39 - enhanced_ensemble_selector - INFO -   SVM vs NeuralNet: 相关性=0.653, 多样性=0.347
2025-07-17 00:21:39 - enhanced_ensemble_selector - INFO -   KNN vs NaiveBayes: 相关性=0.741, 多样性=0.259
2025-07-17 00:21:39 - enhanced_ensemble_selector - INFO -   KNN vs NeuralNet: 相关性=0.681, 多样性=0.319
2025-07-17 00:21:39 - enhanced_ensemble_selector - INFO -   NaiveBayes vs NeuralNet: 相关性=0.431, 多样性=0.569
2025-07-17 00:21:39 - enhanced_ensemble_selector - INFO - 特征重要性多样性: 0.406
2025-07-17 00:21:39 - enhanced_ensemble_selector - INFO - 算法类型多样性: 1.000
2025-07-17 00:21:39 - enhanced_ensemble_selector - INFO - 算法类型分布: {'tree': 5, 'linear': 2, 'instance': 1, 'probabilistic': 1, 'neural': 1}
2025-07-17 00:21:39 - enhanced_ensemble_selector - INFO - 开始量化多样性评估...
2025-07-17 00:21:39 - enhanced_ensemble_selector - INFO - 量化多样性评估完成:
2025-07-17 00:21:39 - enhanced_ensemble_selector - INFO -   Q统计量平均多样性: 0.361
2025-07-17 00:21:39 - enhanced_ensemble_selector - INFO -   多样性变异系数: 0.347
2025-07-17 00:21:39 - enhanced_ensemble_selector - INFO -   熵多样性: 0.315
2025-07-17 00:21:39 - enhanced_ensemble_selector - INFO - 从 10 个合格模型中选择 3 个进行集成
2025-07-17 00:21:39 - enhanced_ensemble_selector - INFO - 使用量化多样性评估进行模型选择...
2025-07-17 00:21:40 - enhanced_ensemble_selector - INFO - 量化评估选择完成:
2025-07-17 00:21:40 - enhanced_ensemble_selector - INFO -   最优组合: ['DecisionTree', 'KNN', 'NeuralNet']
2025-07-17 00:21:40 - enhanced_ensemble_selector - INFO -   性能得分: 0.8165
2025-07-17 00:21:40 - enhanced_ensemble_selector - INFO -   多样性得分: 0.5290
2025-07-17 00:21:40 - enhanced_ensemble_selector - INFO -   综合得分: 0.6728
2025-07-17 00:21:40 - enhanced_ensemble_selector - INFO -   多样性等级: 良好的多样性
2025-07-17 00:27:04 - enhanced_ensemble_selector - INFO - 开始评估 10 个基模型...
2025-07-17 00:27:04 - enhanced_ensemble_selector - INFO -   评估模型: DecisionTree
2025-07-17 00:27:04 - enhanced_ensemble_selector - INFO -     DecisionTree - 性能得分: 0.8511
2025-07-17 00:27:04 - enhanced_ensemble_selector - INFO -   评估模型: RandomForest
2025-07-17 00:27:04 - enhanced_ensemble_selector - INFO -     RandomForest - 性能得分: 0.8626
2025-07-17 00:27:04 - enhanced_ensemble_selector - INFO -   评估模型: XGBoost
2025-07-17 00:27:04 - enhanced_ensemble_selector - INFO -     XGBoost - 性能得分: 0.8137
2025-07-17 00:27:04 - enhanced_ensemble_selector - INFO -   评估模型: LightGBM
2025-07-17 00:27:06 - enhanced_ensemble_selector - INFO -     LightGBM - 性能得分: 0.8165
2025-07-17 00:27:06 - enhanced_ensemble_selector - INFO -   评估模型: CatBoost
2025-07-17 00:27:07 - enhanced_ensemble_selector - INFO -     CatBoost - 性能得分: 0.8053
2025-07-17 00:27:07 - enhanced_ensemble_selector - INFO -   评估模型: Logistic
2025-07-17 00:27:07 - enhanced_ensemble_selector - INFO -     Logistic - 性能得分: 0.8383
2025-07-17 00:27:07 - enhanced_ensemble_selector - INFO -   评估模型: SVM
2025-07-17 00:27:07 - enhanced_ensemble_selector - INFO -     SVM - 性能得分: 0.8813
2025-07-17 00:27:07 - enhanced_ensemble_selector - INFO -   评估模型: KNN
2025-07-17 00:27:07 - enhanced_ensemble_selector - INFO -     KNN - 性能得分: 0.8329
2025-07-17 00:27:07 - enhanced_ensemble_selector - INFO -   评估模型: NaiveBayes
2025-07-17 00:27:07 - enhanced_ensemble_selector - INFO -     NaiveBayes - 性能得分: 0.7733
2025-07-17 00:27:07 - enhanced_ensemble_selector - INFO -   评估模型: NeuralNet
2025-07-17 00:27:08 - enhanced_ensemble_selector - INFO -     NeuralNet - 性能得分: 0.7655
2025-07-17 00:27:08 - enhanced_ensemble_selector - INFO - 使用改进的多样性分析完成
2025-07-17 00:27:08 - enhanced_ensemble_selector - INFO - 数据复杂度: 0.251
2025-07-17 00:27:08 - enhanced_ensemble_selector - INFO - 调整后相关性阈值: 0.600
2025-07-17 00:27:08 - enhanced_ensemble_selector - INFO - 综合多样性得分: 0.440
2025-07-17 00:27:08 - enhanced_ensemble_selector - INFO -   DecisionTree vs RandomForest: 相关性=0.840, 多样性=0.160
2025-07-17 00:27:08 - enhanced_ensemble_selector - INFO -   DecisionTree vs XGBoost: 相关性=0.811, 多样性=0.189
2025-07-17 00:27:08 - enhanced_ensemble_selector - INFO -   DecisionTree vs LightGBM: 相关性=0.797, 多样性=0.203
2025-07-17 00:27:08 - enhanced_ensemble_selector - INFO -   DecisionTree vs CatBoost: 相关性=0.830, 多样性=0.170
2025-07-17 00:27:08 - enhanced_ensemble_selector - INFO -   DecisionTree vs Logistic: 相关性=0.653, 多样性=0.347
2025-07-17 00:27:08 - enhanced_ensemble_selector - INFO -   DecisionTree vs SVM: 相关性=0.746, 多样性=0.254
2025-07-17 00:27:08 - enhanced_ensemble_selector - INFO -   DecisionTree vs KNN: 相关性=0.591, 多样性=0.409
2025-07-17 00:27:08 - enhanced_ensemble_selector - INFO -   DecisionTree vs NaiveBayes: 相关性=0.473, 多样性=0.527
2025-07-17 00:27:08 - enhanced_ensemble_selector - INFO -   DecisionTree vs NeuralNet: 相关性=0.529, 多样性=0.471
2025-07-17 00:27:08 - enhanced_ensemble_selector - INFO -   RandomForest vs XGBoost: 相关性=0.909, 多样性=0.091
2025-07-17 00:27:08 - enhanced_ensemble_selector - INFO -   RandomForest vs LightGBM: 相关性=0.929, 多样性=0.071
2025-07-17 00:27:08 - enhanced_ensemble_selector - INFO -   RandomForest vs CatBoost: 相关性=0.971, 多样性=0.029
2025-07-17 00:27:08 - enhanced_ensemble_selector - INFO -   RandomForest vs Logistic: 相关性=0.872, 多样性=0.128
2025-07-17 00:27:08 - enhanced_ensemble_selector - INFO -   RandomForest vs SVM: 相关性=0.923, 多样性=0.077
2025-07-17 00:27:08 - enhanced_ensemble_selector - INFO -   RandomForest vs KNN: 相关性=0.745, 多样性=0.255
2025-07-17 00:27:08 - enhanced_ensemble_selector - INFO -   RandomForest vs NaiveBayes: 相关性=0.742, 多样性=0.258
2025-07-17 00:27:08 - enhanced_ensemble_selector - INFO -   RandomForest vs NeuralNet: 相关性=0.632, 多样性=0.368
2025-07-17 00:27:08 - enhanced_ensemble_selector - INFO -   XGBoost vs LightGBM: 相关性=0.983, 多样性=0.017
2025-07-17 00:27:08 - enhanced_ensemble_selector - INFO -   XGBoost vs CatBoost: 相关性=0.951, 多样性=0.049
2025-07-17 00:27:08 - enhanced_ensemble_selector - INFO -   XGBoost vs Logistic: 相关性=0.804, 多样性=0.196
2025-07-17 00:27:08 - enhanced_ensemble_selector - INFO -   XGBoost vs SVM: 相关性=0.825, 多样性=0.175
2025-07-17 00:27:08 - enhanced_ensemble_selector - INFO -   XGBoost vs KNN: 相关性=0.617, 多样性=0.383
2025-07-17 00:27:08 - enhanced_ensemble_selector - INFO -   XGBoost vs NaiveBayes: 相关性=0.631, 多样性=0.369
2025-07-17 00:27:08 - enhanced_ensemble_selector - INFO -   XGBoost vs NeuralNet: 相关性=0.652, 多样性=0.348
2025-07-17 00:27:08 - enhanced_ensemble_selector - INFO -   LightGBM vs CatBoost: 相关性=0.967, 多样性=0.033
2025-07-17 00:27:08 - enhanced_ensemble_selector - INFO -   LightGBM vs Logistic: 相关性=0.834, 多样性=0.166
2025-07-17 00:27:08 - enhanced_ensemble_selector - INFO -   LightGBM vs SVM: 相关性=0.868, 多样性=0.132
2025-07-17 00:27:08 - enhanced_ensemble_selector - INFO -   LightGBM vs KNN: 相关性=0.681, 多样性=0.319
2025-07-17 00:27:08 - enhanced_ensemble_selector - INFO -   LightGBM vs NaiveBayes: 相关性=0.699, 多样性=0.301
2025-07-17 00:27:08 - enhanced_ensemble_selector - INFO -   LightGBM vs NeuralNet: 相关性=0.652, 多样性=0.348
2025-07-17 00:27:08 - enhanced_ensemble_selector - INFO -   CatBoost vs Logistic: 相关性=0.875, 多样性=0.125
2025-07-17 00:27:08 - enhanced_ensemble_selector - INFO -   CatBoost vs SVM: 相关性=0.912, 多样性=0.088
2025-07-17 00:27:08 - enhanced_ensemble_selector - INFO -   CatBoost vs KNN: 相关性=0.753, 多样性=0.247
2025-07-17 00:27:08 - enhanced_ensemble_selector - INFO -   CatBoost vs NaiveBayes: 相关性=0.716, 多样性=0.284
2025-07-17 00:27:08 - enhanced_ensemble_selector - INFO -   CatBoost vs NeuralNet: 相关性=0.727, 多样性=0.273
2025-07-17 00:27:08 - enhanced_ensemble_selector - INFO -   Logistic vs SVM: 相关性=0.877, 多样性=0.123
2025-07-17 00:27:08 - enhanced_ensemble_selector - INFO -   Logistic vs KNN: 相关性=0.746, 多样性=0.254
2025-07-17 00:27:08 - enhanced_ensemble_selector - INFO -   Logistic vs NaiveBayes: 相关性=0.873, 多样性=0.127
2025-07-17 00:27:08 - enhanced_ensemble_selector - INFO -   Logistic vs NeuralNet: 相关性=0.568, 多样性=0.432
2025-07-17 00:27:08 - enhanced_ensemble_selector - INFO -   SVM vs KNN: 相关性=0.845, 多样性=0.155
2025-07-17 00:27:08 - enhanced_ensemble_selector - INFO -   SVM vs NaiveBayes: 相关性=0.834, 多样性=0.166
2025-07-17 00:27:08 - enhanced_ensemble_selector - INFO -   SVM vs NeuralNet: 相关性=0.653, 多样性=0.347
2025-07-17 00:27:08 - enhanced_ensemble_selector - INFO -   KNN vs NaiveBayes: 相关性=0.741, 多样性=0.259
2025-07-17 00:27:08 - enhanced_ensemble_selector - INFO -   KNN vs NeuralNet: 相关性=0.681, 多样性=0.319
2025-07-17 00:27:08 - enhanced_ensemble_selector - INFO -   NaiveBayes vs NeuralNet: 相关性=0.431, 多样性=0.569
2025-07-17 00:27:08 - enhanced_ensemble_selector - INFO - 特征重要性多样性: 0.406
2025-07-17 00:27:08 - enhanced_ensemble_selector - INFO - 算法类型多样性: 1.000
2025-07-17 00:27:08 - enhanced_ensemble_selector - INFO - 算法类型分布: {'tree': 5, 'linear': 2, 'instance': 1, 'probabilistic': 1, 'neural': 1}
2025-07-17 00:27:08 - enhanced_ensemble_selector - INFO - 开始量化多样性评估...
2025-07-17 00:27:08 - enhanced_ensemble_selector - INFO - 量化多样性评估完成:
2025-07-17 00:27:08 - enhanced_ensemble_selector - INFO -   Q统计量平均多样性: 0.361
2025-07-17 00:27:08 - enhanced_ensemble_selector - INFO -   多样性变异系数: 0.347
2025-07-17 00:27:08 - enhanced_ensemble_selector - INFO -   熵多样性: 0.315
2025-07-17 00:27:08 - enhanced_ensemble_selector - INFO - 从 10 个合格模型中选择 3 个进行集成
2025-07-17 00:27:08 - enhanced_ensemble_selector - INFO - 使用量化多样性评估进行模型选择...
2025-07-17 00:27:09 - enhanced_ensemble_selector - INFO - 量化评估选择完成:
2025-07-17 00:27:09 - enhanced_ensemble_selector - INFO -   最优组合: ['DecisionTree', 'KNN', 'NeuralNet']
2025-07-17 00:27:09 - enhanced_ensemble_selector - INFO -   性能得分: 0.8165
2025-07-17 00:27:09 - enhanced_ensemble_selector - INFO -   多样性得分: 0.5290
2025-07-17 00:27:09 - enhanced_ensemble_selector - INFO -   综合得分: 0.6728
2025-07-17 00:27:09 - enhanced_ensemble_selector - INFO -   多样性等级: 良好的多样性
2025-08-07 07:56:39 - enhanced_ensemble_selector - INFO - 开始评估 3 个基模型...
2025-08-07 07:56:39 - enhanced_ensemble_selector - INFO -   评估模型: Logistic
2025-08-07 07:56:39 - enhanced_ensemble_selector - ERROR - 评估模型 Logistic 失败: Input X contains NaN.
LogisticRegression does not accept missing values encoded as NaN natively. For supervised learning, you might want to consider sklearn.ensemble.HistGradientBoostingClassifier and Regressor which accept missing values encoded as NaNs natively. Alternatively, it is possible to preprocess the data, for instance by using an imputer transformer in a pipeline or drop samples with missing values. See https://scikit-learn.org/stable/modules/impute.html You can find a list of all estimators that handle NaN values at the following page: https://scikit-learn.org/stable/modules/impute.html#estimators-that-handle-nan-values
2025-08-07 07:56:39 - enhanced_ensemble_selector - INFO -   评估模型: RandomForest
2025-08-07 07:56:39 - enhanced_ensemble_selector - INFO -     RandomForest - 性能得分: 0.9339
2025-08-07 07:56:39 - enhanced_ensemble_selector - INFO -   评估模型: KNN
2025-08-07 07:56:39 - enhanced_ensemble_selector - ERROR - 评估模型 KNN 失败: Input X contains NaN.
KNeighborsClassifier does not accept missing values encoded as NaN natively. For supervised learning, you might want to consider sklearn.ensemble.HistGradientBoostingClassifier and Regressor which accept missing values encoded as NaNs natively. Alternatively, it is possible to preprocess the data, for instance by using an imputer transformer in a pipeline or drop samples with missing values. See https://scikit-learn.org/stable/modules/impute.html You can find a list of all estimators that handle NaN values at the following page: https://scikit-learn.org/stable/modules/impute.html#estimators-that-handle-nan-values
2025-08-07 07:56:39 - enhanced_ensemble_selector - INFO - 使用改进的多样性分析完成
2025-08-07 07:56:39 - enhanced_ensemble_selector - INFO - 数据复杂度: nan
2025-08-07 07:56:39 - enhanced_ensemble_selector - INFO - 调整后相关性阈值: 0.300
2025-08-07 07:56:39 - enhanced_ensemble_selector - INFO - 综合多样性得分: 0.980
2025-08-07 07:57:17 - enhanced_ensemble_selector - INFO - 开始评估 3 个基模型...
2025-08-07 07:57:17 - enhanced_ensemble_selector - INFO -   评估模型: Logistic
2025-08-07 07:57:17 - enhanced_ensemble_selector - INFO -     Logistic - 性能得分: 0.7590
2025-08-07 07:57:17 - enhanced_ensemble_selector - INFO -   评估模型: RandomForest
2025-08-07 07:57:17 - enhanced_ensemble_selector - INFO -     RandomForest - 性能得分: 0.9088
2025-08-07 07:57:17 - enhanced_ensemble_selector - INFO -   评估模型: KNN
2025-08-07 07:57:19 - enhanced_ensemble_selector - INFO -     KNN - 性能得分: 0.6615
2025-08-07 07:57:19 - enhanced_ensemble_selector - INFO - 使用改进的多样性分析完成
2025-08-07 07:57:19 - enhanced_ensemble_selector - INFO - 数据复杂度: 0.174
2025-08-07 07:57:19 - enhanced_ensemble_selector - INFO - 调整后相关性阈值: 0.600
2025-08-07 07:57:19 - enhanced_ensemble_selector - INFO - 综合多样性得分: 0.333
2025-08-07 07:57:19 - enhanced_ensemble_selector - INFO -   Logistic vs RandomForest: 相关性=0.836, 多样性=0.164
2025-08-07 07:57:19 - enhanced_ensemble_selector - INFO -   Logistic vs KNN: 相关性=0.792, 多样性=0.208
2025-08-07 07:57:19 - enhanced_ensemble_selector - INFO -   RandomForest vs KNN: 相关性=0.640, 多样性=0.360
2025-08-07 07:57:19 - enhanced_ensemble_selector - INFO - 特征重要性多样性: 0.038
2025-08-07 07:57:19 - enhanced_ensemble_selector - INFO - 算法类型多样性: 1.000
2025-08-07 07:57:19 - enhanced_ensemble_selector - INFO - 算法类型分布: {'linear': 1, 'tree': 1, 'instance': 1}
2025-08-07 07:57:19 - enhanced_ensemble_selector - INFO - 开始量化多样性评估...
2025-08-07 07:57:19 - enhanced_ensemble_selector - INFO - 量化多样性评估完成:
2025-08-07 07:57:19 - enhanced_ensemble_selector - INFO -   Q统计量平均多样性: 0.400
2025-08-07 07:57:19 - enhanced_ensemble_selector - INFO -   多样性变异系数: 0.173
2025-08-07 07:57:19 - enhanced_ensemble_selector - INFO -   熵多样性: 0.336
2025-08-07 07:57:19 - enhanced_ensemble_selector - INFO - 从 3 个合格模型中选择 2 个进行集成
2025-08-07 07:57:19 - enhanced_ensemble_selector - INFO - 最优组合: ['RandomForest', 'KNN']
2025-08-07 07:57:19 - enhanced_ensemble_selector - INFO - 综合得分: 0.7005
2025-08-07 07:57:49 - enhanced_ensemble_selector - INFO - 开始评估 3 个基模型...
2025-08-07 07:57:49 - enhanced_ensemble_selector - INFO -   评估模型: Logistic
2025-08-07 07:57:49 - enhanced_ensemble_selector - INFO -     Logistic - 性能得分: 0.7590
2025-08-07 07:57:49 - enhanced_ensemble_selector - INFO -   评估模型: RandomForest
2025-08-07 07:57:49 - enhanced_ensemble_selector - INFO -     RandomForest - 性能得分: 0.9088
2025-08-07 07:57:49 - enhanced_ensemble_selector - INFO -   评估模型: KNN
2025-08-07 07:57:50 - enhanced_ensemble_selector - INFO -     KNN - 性能得分: 0.6615
2025-08-07 07:57:50 - enhanced_ensemble_selector - INFO - 使用改进的多样性分析完成
2025-08-07 07:57:50 - enhanced_ensemble_selector - INFO - 数据复杂度: 0.174
2025-08-07 07:57:50 - enhanced_ensemble_selector - INFO - 调整后相关性阈值: 0.600
2025-08-07 07:57:50 - enhanced_ensemble_selector - INFO - 综合多样性得分: 0.333
2025-08-07 07:57:50 - enhanced_ensemble_selector - INFO -   Logistic vs RandomForest: 相关性=0.836, 多样性=0.164
2025-08-07 07:57:50 - enhanced_ensemble_selector - INFO -   Logistic vs KNN: 相关性=0.792, 多样性=0.208
2025-08-07 07:57:50 - enhanced_ensemble_selector - INFO -   RandomForest vs KNN: 相关性=0.640, 多样性=0.360
2025-08-07 07:57:50 - enhanced_ensemble_selector - INFO - 特征重要性多样性: 0.038
2025-08-07 07:57:50 - enhanced_ensemble_selector - INFO - 算法类型多样性: 1.000
2025-08-07 07:57:50 - enhanced_ensemble_selector - INFO - 算法类型分布: {'linear': 1, 'tree': 1, 'instance': 1}
2025-08-07 07:57:50 - enhanced_ensemble_selector - INFO - 开始量化多样性评估...
2025-08-07 07:57:51 - enhanced_ensemble_selector - INFO - 量化多样性评估完成:
2025-08-07 07:57:51 - enhanced_ensemble_selector - INFO -   Q统计量平均多样性: 0.400
2025-08-07 07:57:51 - enhanced_ensemble_selector - INFO -   多样性变异系数: 0.173
2025-08-07 07:57:51 - enhanced_ensemble_selector - INFO -   熵多样性: 0.336
2025-08-07 07:57:51 - enhanced_ensemble_selector - INFO - 从 3 个合格模型中选择 2 个进行集成
2025-08-07 07:57:51 - enhanced_ensemble_selector - INFO - 最优组合: ['RandomForest', 'KNN']
2025-08-07 07:57:51 - enhanced_ensemble_selector - INFO - 综合得分: 0.7005
2025-08-07 08:26:40 - enhanced_ensemble_selector - INFO - 开始评估 10 个基模型...
2025-08-07 08:26:40 - enhanced_ensemble_selector - INFO -   评估模型: DecisionTree
2025-08-07 08:26:40 - enhanced_ensemble_selector - INFO -     DecisionTree - 性能得分: 0.8233
2025-08-07 08:26:40 - enhanced_ensemble_selector - INFO -   评估模型: RandomForest
2025-08-07 08:26:40 - enhanced_ensemble_selector - INFO -     RandomForest - 性能得分: 0.9097
2025-08-07 08:26:40 - enhanced_ensemble_selector - INFO -   评估模型: XGBoost
2025-08-07 08:26:40 - enhanced_ensemble_selector - INFO -     XGBoost - 性能得分: 0.8978
2025-08-07 08:26:40 - enhanced_ensemble_selector - INFO -   评估模型: LightGBM
2025-08-07 08:26:41 - enhanced_ensemble_selector - INFO -     LightGBM - 性能得分: 0.8927
2025-08-07 08:26:41 - enhanced_ensemble_selector - INFO -   评估模型: CatBoost
2025-08-07 08:26:43 - enhanced_ensemble_selector - INFO -     CatBoost - 性能得分: 0.9148
2025-08-07 08:26:43 - enhanced_ensemble_selector - INFO -   评估模型: Logistic
2025-08-07 08:26:43 - enhanced_ensemble_selector - INFO -     Logistic - 性能得分: 0.8694
2025-08-07 08:26:43 - enhanced_ensemble_selector - INFO -   评估模型: SVM
2025-08-07 08:26:43 - enhanced_ensemble_selector - INFO -     SVM - 性能得分: 0.8297
2025-08-07 08:26:43 - enhanced_ensemble_selector - INFO -   评估模型: KNN
2025-08-07 08:26:43 - enhanced_ensemble_selector - INFO -     KNN - 性能得分: 0.8892
2025-08-07 08:26:43 - enhanced_ensemble_selector - INFO -   评估模型: NaiveBayes
2025-08-07 08:26:43 - enhanced_ensemble_selector - INFO -     NaiveBayes - 性能得分: 0.8809
2025-08-07 08:26:43 - enhanced_ensemble_selector - INFO -   评估模型: NeuralNet
2025-08-07 08:26:43 - enhanced_ensemble_selector - INFO -     NeuralNet - 性能得分: 0.8959
2025-08-07 08:26:43 - enhanced_ensemble_selector - INFO - 使用改进的多样性分析完成
2025-08-07 08:26:43 - enhanced_ensemble_selector - INFO - 数据复杂度: 0.246
2025-08-07 08:26:43 - enhanced_ensemble_selector - INFO - 调整后相关性阈值: 0.600
2025-08-07 08:26:43 - enhanced_ensemble_selector - INFO - 综合多样性得分: 0.374
2025-08-07 08:26:43 - enhanced_ensemble_selector - INFO -   DecisionTree vs RandomForest: 相关性=0.806, 多样性=0.194
2025-08-07 08:26:43 - enhanced_ensemble_selector - INFO -   DecisionTree vs XGBoost: 相关性=0.843, 多样性=0.157
2025-08-07 08:26:43 - enhanced_ensemble_selector - INFO -   DecisionTree vs LightGBM: 相关性=0.784, 多样性=0.216
2025-08-07 08:26:43 - enhanced_ensemble_selector - INFO -   DecisionTree vs CatBoost: 相关性=0.803, 多样性=0.197
2025-08-07 08:26:43 - enhanced_ensemble_selector - INFO -   DecisionTree vs Logistic: 相关性=0.673, 多样性=0.327
2025-08-07 08:26:43 - enhanced_ensemble_selector - INFO -   DecisionTree vs SVM: 相关性=0.699, 多样性=0.301
2025-08-07 08:26:43 - enhanced_ensemble_selector - INFO -   DecisionTree vs KNN: 相关性=0.696, 多样性=0.304
2025-08-07 08:26:43 - enhanced_ensemble_selector - INFO -   DecisionTree vs NaiveBayes: 相关性=0.659, 多样性=0.341
2025-08-07 08:26:43 - enhanced_ensemble_selector - INFO -   DecisionTree vs NeuralNet: 相关性=0.643, 多样性=0.357
2025-08-07 08:26:43 - enhanced_ensemble_selector - INFO -   RandomForest vs XGBoost: 相关性=0.955, 多样性=0.045
2025-08-07 08:26:43 - enhanced_ensemble_selector - INFO -   RandomForest vs LightGBM: 相关性=0.950, 多样性=0.050
2025-08-07 08:26:43 - enhanced_ensemble_selector - INFO -   RandomForest vs CatBoost: 相关性=0.986, 多样性=0.014
2025-08-07 08:26:43 - enhanced_ensemble_selector - INFO -   RandomForest vs Logistic: 相关性=0.887, 多样性=0.113
2025-08-07 08:26:43 - enhanced_ensemble_selector - INFO -   RandomForest vs SVM: 相关性=0.936, 多样性=0.064
2025-08-07 08:26:43 - enhanced_ensemble_selector - INFO -   RandomForest vs KNN: 相关性=0.912, 多样性=0.088
2025-08-07 08:26:43 - enhanced_ensemble_selector - INFO -   RandomForest vs NaiveBayes: 相关性=0.861, 多样性=0.139
2025-08-07 08:26:43 - enhanced_ensemble_selector - INFO -   RandomForest vs NeuralNet: 相关性=0.886, 多样性=0.114
2025-08-07 08:26:43 - enhanced_ensemble_selector - INFO -   XGBoost vs LightGBM: 相关性=0.971, 多样性=0.029
2025-08-07 08:26:43 - enhanced_ensemble_selector - INFO -   XGBoost vs CatBoost: 相关性=0.981, 多样性=0.019
2025-08-07 08:26:43 - enhanced_ensemble_selector - INFO -   XGBoost vs Logistic: 相关性=0.814, 多样性=0.186
2025-08-07 08:26:43 - enhanced_ensemble_selector - INFO -   XGBoost vs SVM: 相关性=0.888, 多样性=0.112
2025-08-07 08:26:43 - enhanced_ensemble_selector - INFO -   XGBoost vs KNN: 相关性=0.857, 多样性=0.143
2025-08-07 08:26:43 - enhanced_ensemble_selector - INFO -   XGBoost vs NaiveBayes: 相关性=0.822, 多样性=0.178
2025-08-07 08:26:43 - enhanced_ensemble_selector - INFO -   XGBoost vs NeuralNet: 相关性=0.838, 多样性=0.162
2025-08-07 08:26:43 - enhanced_ensemble_selector - INFO -   LightGBM vs CatBoost: 相关性=0.981, 多样性=0.019
2025-08-07 08:26:43 - enhanced_ensemble_selector - INFO -   LightGBM vs Logistic: 相关性=0.819, 多样性=0.181
2025-08-07 08:26:43 - enhanced_ensemble_selector - INFO -   LightGBM vs SVM: 相关性=0.895, 多样性=0.105
2025-08-07 08:26:43 - enhanced_ensemble_selector - INFO -   LightGBM vs KNN: 相关性=0.876, 多样性=0.124
2025-08-07 08:26:43 - enhanced_ensemble_selector - INFO -   LightGBM vs NaiveBayes: 相关性=0.797, 多样性=0.203
2025-08-07 08:26:43 - enhanced_ensemble_selector - INFO -   LightGBM vs NeuralNet: 相关性=0.882, 多样性=0.118
2025-08-07 08:26:43 - enhanced_ensemble_selector - INFO -   CatBoost vs Logistic: 相关性=0.865, 多样性=0.135
2025-08-07 08:26:43 - enhanced_ensemble_selector - INFO -   CatBoost vs SVM: 相关性=0.924, 多样性=0.076
2025-08-07 08:26:43 - enhanced_ensemble_selector - INFO -   CatBoost vs KNN: 相关性=0.907, 多样性=0.093
2025-08-07 08:26:43 - enhanced_ensemble_selector - INFO -   CatBoost vs NaiveBayes: 相关性=0.843, 多样性=0.157
2025-08-07 08:26:43 - enhanced_ensemble_selector - INFO -   CatBoost vs NeuralNet: 相关性=0.892, 多样性=0.108
2025-08-07 08:26:43 - enhanced_ensemble_selector - INFO -   Logistic vs SVM: 相关性=0.899, 多样性=0.101
2025-08-07 08:26:43 - enhanced_ensemble_selector - INFO -   Logistic vs KNN: 相关性=0.963, 多样性=0.037
2025-08-07 08:26:43 - enhanced_ensemble_selector - INFO -   Logistic vs NaiveBayes: 相关性=0.954, 多样性=0.046
2025-08-07 08:26:43 - enhanced_ensemble_selector - INFO -   Logistic vs NeuralNet: 相关性=0.936, 多样性=0.064
2025-08-07 08:26:43 - enhanced_ensemble_selector - INFO -   SVM vs KNN: 相关性=0.924, 多样性=0.076
2025-08-07 08:26:43 - enhanced_ensemble_selector - INFO -   SVM vs NaiveBayes: 相关性=0.874, 多样性=0.126
2025-08-07 08:26:43 - enhanced_ensemble_selector - INFO -   SVM vs NeuralNet: 相关性=0.894, 多样性=0.106
2025-08-07 08:26:43 - enhanced_ensemble_selector - INFO -   KNN vs NaiveBayes: 相关性=0.910, 多样性=0.090
2025-08-07 08:26:43 - enhanced_ensemble_selector - INFO -   KNN vs NeuralNet: 相关性=0.939, 多样性=0.061
2025-08-07 08:26:43 - enhanced_ensemble_selector - INFO -   NaiveBayes vs NeuralNet: 相关性=0.897, 多样性=0.103
2025-08-07 08:26:43 - enhanced_ensemble_selector - INFO - 特征重要性多样性: 0.359
2025-08-07 08:26:43 - enhanced_ensemble_selector - INFO - 算法类型多样性: 1.000
2025-08-07 08:26:43 - enhanced_ensemble_selector - INFO - 算法类型分布: {'tree': 5, 'linear': 2, 'instance': 1, 'probabilistic': 1, 'neural': 1}
2025-08-07 08:26:43 - enhanced_ensemble_selector - INFO - 开始量化多样性评估...
2025-08-07 08:26:43 - enhanced_ensemble_selector - INFO - 量化多样性评估完成:
2025-08-07 08:26:43 - enhanced_ensemble_selector - INFO -   Q统计量平均多样性: 0.297
2025-08-07 08:26:43 - enhanced_ensemble_selector - INFO -   多样性变异系数: 0.301
2025-08-07 08:26:43 - enhanced_ensemble_selector - INFO -   熵多样性: 0.215
2025-08-07 08:26:43 - enhanced_ensemble_selector - INFO - 从 10 个合格模型中选择 3 个进行集成
2025-08-07 08:26:43 - enhanced_ensemble_selector - INFO - 最优组合: ['DecisionTree', 'NaiveBayes', 'NeuralNet']
2025-08-07 08:26:43 - enhanced_ensemble_selector - INFO - 综合得分: 0.7135
