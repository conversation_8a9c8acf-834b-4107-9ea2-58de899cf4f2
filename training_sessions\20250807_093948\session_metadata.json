{"session_id": "20250807_093948", "session_name": "训练_nodule2_20250807_093948", "description": "自动创建的训练会话，基于数据文件: nodule2", "created_time": "2025-08-07T09:39:48.602663", "last_modified": "2025-08-07T09:39:50.411393", "trained_models": [{"model_name": "DecisionTree", "model_type": "single", "filename": "DecisionTree_single_093948.joblib", "filepath": "c:\\Users\\<USER>\\PycharmProjects\\multi_model_01_updated\\training_sessions\\20250807_093948\\models\\DecisionTree_single_093948.joblib", "save_time": "2025-08-07T09:39:48.648368"}, {"model_name": "RandomForest", "model_type": "single", "filename": "RandomForest_single_093948.joblib", "filepath": "c:\\Users\\<USER>\\PycharmProjects\\multi_model_01_updated\\training_sessions\\20250807_093948\\models\\RandomForest_single_093948.joblib", "save_time": "2025-08-07T09:39:48.750089"}, {"model_name": "XGBoost", "model_type": "single", "filename": "XGBoost_single_093948.joblib", "filepath": "c:\\Users\\<USER>\\PycharmProjects\\multi_model_01_updated\\training_sessions\\20250807_093948\\models\\XGBoost_single_093948.joblib", "save_time": "2025-08-07T09:39:48.853015"}, {"model_name": "LightGBM", "model_type": "single", "filename": "LightGBM_single_093948.joblib", "filepath": "c:\\Users\\<USER>\\PycharmProjects\\multi_model_01_updated\\training_sessions\\20250807_093948\\models\\LightGBM_single_093948.joblib", "save_time": "2025-08-07T09:39:48.933153"}, {"model_name": "CatBoost", "model_type": "single", "filename": "CatBoost_single_093950.joblib", "filepath": "c:\\Users\\<USER>\\PycharmProjects\\multi_model_01_updated\\training_sessions\\20250807_093948\\models\\CatBoost_single_093950.joblib", "save_time": "2025-08-07T09:39:50.001157"}, {"model_name": "Logistic", "model_type": "single", "filename": "Logistic_single_093950.joblib", "filepath": "c:\\Users\\<USER>\\PycharmProjects\\multi_model_01_updated\\training_sessions\\20250807_093948\\models\\Logistic_single_093950.joblib", "save_time": "2025-08-07T09:39:50.039238"}, {"model_name": "SVM", "model_type": "single", "filename": "SVM_single_093950.joblib", "filepath": "c:\\Users\\<USER>\\PycharmProjects\\multi_model_01_updated\\training_sessions\\20250807_093948\\models\\SVM_single_093950.joblib", "save_time": "2025-08-07T09:39:50.074573"}, {"model_name": "KNN", "model_type": "single", "filename": "KNN_single_093950.joblib", "filepath": "c:\\Users\\<USER>\\PycharmProjects\\multi_model_01_updated\\training_sessions\\20250807_093948\\models\\KNN_single_093950.joblib", "save_time": "2025-08-07T09:39:50.109725"}, {"model_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "model_type": "single", "filename": "NaiveBayes_single_093950.joblib", "filepath": "c:\\Users\\<USER>\\PycharmProjects\\multi_model_01_updated\\training_sessions\\20250807_093948\\models\\NaiveBayes_single_093950.joblib", "save_time": "2025-08-07T09:39:50.140915"}, {"model_name": "NeuralNet", "model_type": "single", "filename": "NeuralNet_single_093950.joblib", "filepath": "c:\\Users\\<USER>\\PycharmProjects\\multi_model_01_updated\\training_sessions\\20250807_093948\\models\\NeuralNet_single_093950.joblib", "save_time": "2025-08-07T09:39:50.403396"}], "ensemble_results": [], "data_files": [], "plots": [], "logs": [], "status": "created"}