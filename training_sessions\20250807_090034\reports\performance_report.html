
    <!DOCTYPE html>
    <html lang="zh-CN">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>模型性能比较报告</title>
        <style>
            body {
                font-family: 'Arial', sans-serif;
                margin: 40px;
                background-color: #f5f5f5;
            }
            .container {
                background-color: white;
                padding: 30px;
                border-radius: 10px;
                box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            }
            h1 {
                color: #2c3e50;
                text-align: center;
                border-bottom: 3px solid #3498db;
                padding-bottom: 10px;
            }
            h2 {
                color: #34495e;
                margin-top: 30px;
            }
            table {
                width: 100%;
                border-collapse: collapse;
                margin: 20px 0;
            }
            th, td {
                border: 1px solid #ddd;
                padding: 12px;
                text-align: center;
            }
            th {
                background-color: #3498db;
                color: white;
                font-weight: bold;
            }
            tr:nth-child(even) {
                background-color: #f2f2f2;
            }
            .best-score {
                background-color: #2ecc71 !important;
                color: white;
                font-weight: bold;
            }
            .summary {
                background-color: #ecf0f1;
                padding: 20px;
                border-radius: 5px;
                margin: 20px 0;
            }
            .metric-description {
                font-size: 0.9em;
                color: #7f8c8d;
                margin-top: 10px;
            }
        </style>
    </head>
    <body>
        <div class="container">
            <h1>模型性能比较报告</h1>
            <p style="text-align: center; color: #7f8c8d;">生成时间: 2025-08-07 09:18:24</p>
            
            <div class="summary">
                <h2>📊 执行摘要</h2>
                <p><strong>最佳模型:</strong> CatBoost (综合得分: 0.885)</p>
                <p><strong>比较模型数量:</strong> 10</p>
                <p><strong>评估指标数量:</strong> 13</p>
            </div>
            
            <h2>🏆 模型排名</h2>
            <table>
                <tr>
                    <th>排名</th>
                    <th>模型名称</th>
                    <th>综合得分</th>
                    <th>准确率</th>
                    <th>精确率</th>
                    <th>召回率</th>
                    <th>F1分数</th>
                    <th>AUC-ROC</th>
                </tr>
    
                <tr>
                    <td>1</td>
                    <td><strong>CatBoost</strong></td>
                    <td class="best-score">0.885</td>
                    <td>0.900</td>
                    <td>0.933</td>
                    <td>0.824</td>
                    <td>0.875</td>
                    <td>0.959</td>
                </tr>
        
                <tr>
                    <td>2</td>
                    <td><strong>RandomForest</strong></td>
                    <td class="">0.881</td>
                    <td>0.900</td>
                    <td>0.933</td>
                    <td>0.824</td>
                    <td>0.875</td>
                    <td>0.939</td>
                </tr>
        
                <tr>
                    <td>3</td>
                    <td><strong>XGBoost</strong></td>
                    <td class="">0.867</td>
                    <td>0.878</td>
                    <td>0.882</td>
                    <td>0.833</td>
                    <td>0.857</td>
                    <td>0.969</td>
                </tr>
        
                <tr>
                    <td>4</td>
                    <td><strong>NeuralNet</strong></td>
                    <td class="">0.866</td>
                    <td>0.878</td>
                    <td>0.882</td>
                    <td>0.833</td>
                    <td>0.857</td>
                    <td>0.961</td>
                </tr>
        
                <tr>
                    <td>5</td>
                    <td><strong>LightGBM</strong></td>
                    <td class="">0.863</td>
                    <td>0.878</td>
                    <td>0.882</td>
                    <td>0.833</td>
                    <td>0.857</td>
                    <td>0.949</td>
                </tr>
        
                <tr>
                    <td>6</td>
                    <td><strong>KNN</strong></td>
                    <td class="">0.854</td>
                    <td>0.875</td>
                    <td>0.875</td>
                    <td>0.824</td>
                    <td>0.848</td>
                    <td>0.932</td>
                </tr>
        
                <tr>
                    <td>7</td>
                    <td><strong>NaiveBayes</strong></td>
                    <td class="">0.853</td>
                    <td>0.878</td>
                    <td>0.933</td>
                    <td>0.778</td>
                    <td>0.848</td>
                    <td>0.906</td>
                </tr>
        
                <tr>
                    <td>8</td>
                    <td><strong>Logistic</strong></td>
                    <td class="">0.833</td>
                    <td>0.854</td>
                    <td>0.875</td>
                    <td>0.778</td>
                    <td>0.824</td>
                    <td>0.932</td>
                </tr>
        
                <tr>
                    <td>9</td>
                    <td><strong>DecisionTree</strong></td>
                    <td class="">0.774</td>
                    <td>0.805</td>
                    <td>0.812</td>
                    <td>0.722</td>
                    <td>0.765</td>
                    <td>0.900</td>
                </tr>
        
                <tr>
                    <td>10</td>
                    <td><strong>SVM</strong></td>
                    <td class="">0.763</td>
                    <td>0.800</td>
                    <td>0.846</td>
                    <td>0.647</td>
                    <td>0.733</td>
                    <td>0.921</td>
                </tr>
        
            </table>
            
            <h2>📈 详细性能指标</h2>
            <table>
                <tr>
                    <th>模型</th>
    <th>准确率</th><th>精确率</th><th>召回率</th><th>F1分数</th><th>特异性</th><th>敏感性</th><th>阴性预测值</th><th>阳性预测值</th><th>AUC-ROC</th><th>AUC-PR</th><th>MCC</th><th>Kappa</th><th>平衡准确率</th></tr><tr><td><strong>DecisionTree</strong></td><td class="">0.805</td><td class="">0.812</td><td class="">0.722</td><td class="">0.765</td><td class="">0.870</td><td class="">0.722</td><td class="">0.800</td><td class="">0.812</td><td class="">0.900</td><td class="">0.814</td><td class="">0.602</td><td class="">0.599</td><td class="">0.796</td></tr><tr><td><strong>RandomForest</strong></td><td class="best-score">0.900</td><td class="best-score">0.933</td><td class="">0.824</td><td class="best-score">0.875</td><td class="best-score">0.957</td><td class="">0.824</td><td class="best-score">0.880</td><td class="best-score">0.933</td><td class="">0.939</td><td class="">0.939</td><td class="best-score">0.797</td><td class="best-score">0.792</td><td class="best-score">0.890</td></tr><tr><td><strong>XGBoost</strong></td><td class="">0.878</td><td class="">0.882</td><td class="best-score">0.833</td><td class="">0.857</td><td class="">0.913</td><td class="best-score">0.833</td><td class="">0.875</td><td class="">0.882</td><td class="best-score">0.969</td><td class="best-score">0.960</td><td class="">0.752</td><td class="">0.751</td><td class="">0.873</td></tr><tr><td><strong>LightGBM</strong></td><td class="">0.878</td><td class="">0.882</td><td class="best-score">0.833</td><td class="">0.857</td><td class="">0.913</td><td class="best-score">0.833</td><td class="">0.875</td><td class="">0.882</td><td class="">0.949</td><td class="">0.948</td><td class="">0.752</td><td class="">0.751</td><td class="">0.873</td></tr><tr><td><strong>CatBoost</strong></td><td class="best-score">0.900</td><td class="best-score">0.933</td><td class="">0.824</td><td class="best-score">0.875</td><td class="best-score">0.957</td><td class="">0.824</td><td class="best-score">0.880</td><td class="best-score">0.933</td><td class="">0.959</td><td class="">0.957</td><td class="best-score">0.797</td><td class="best-score">0.792</td><td class="best-score">0.890</td></tr><tr><td><strong>Logistic</strong></td><td class="">0.854</td><td class="">0.875</td><td class="">0.778</td><td class="">0.824</td><td class="">0.913</td><td class="">0.778</td><td class="">0.840</td><td class="">0.875</td><td class="">0.932</td><td class="">0.936</td><td class="">0.703</td><td class="">0.699</td><td class="">0.845</td></tr><tr><td><strong>SVM</strong></td><td class="">0.800</td><td class="">0.846</td><td class="">0.647</td><td class="">0.733</td><td class="">0.913</td><td class="">0.647</td><td class="">0.778</td><td class="">0.846</td><td class="">0.921</td><td class="">0.908</td><td class="">0.591</td><td class="">0.578</td><td class="">0.780</td></tr><tr><td><strong>NeuralNet</strong></td><td class="">0.878</td><td class="">0.882</td><td class="best-score">0.833</td><td class="">0.857</td><td class="">0.913</td><td class="best-score">0.833</td><td class="">0.875</td><td class="">0.882</td><td class="">0.961</td><td class="">0.951</td><td class="">0.752</td><td class="">0.751</td><td class="">0.873</td></tr><tr><td><strong>NaiveBayes</strong></td><td class="">0.878</td><td class="best-score">0.933</td><td class="">0.778</td><td class="">0.848</td><td class="best-score">0.957</td><td class="">0.778</td><td class="">0.846</td><td class="best-score">0.933</td><td class="">0.906</td><td class="">0.919</td><td class="">0.757</td><td class="">0.748</td><td class="">0.867</td></tr><tr><td><strong>KNN</strong></td><td class="">0.875</td><td class="">0.875</td><td class="">0.824</td><td class="">0.848</td><td class="">0.913</td><td class="">0.824</td><td class="">0.875</td><td class="">0.875</td><td class="">0.932</td><td class="">0.919</td><td class="">0.743</td><td class="">0.742</td><td class="">0.868</td></tr>
            </table>
            
            <div class="metric-description">
                <h2>📝 指标说明</h2>
                <ul>
                    <li><strong>准确率(Accuracy):</strong> 正确预测实例的比例</li>
                    <li><strong>精确率(Precision):</strong> 预测为正例中实际为正例的比例</li>
                    <li><strong>召回率(Recall):</strong> 实际正例中被正确预测的比例</li>
                    <li><strong>F1分数(F1 Score):</strong> 精确率和召回率的调和平均数</li>
                    <li><strong>特异性(Specificity):</strong> 实际负例中被正确预测的比例</li>
                    <li><strong>AUC-ROC:</strong> ROC曲线下面积，衡量分类器性能</li>
                    <li><strong>AUC-PR:</strong> 精确率-召回率曲线下面积</li>
                    <li><strong>MCC:</strong> 马修斯相关系数，平衡的分类性能度量</li>
                    <li><strong>Kappa:</strong> 科恩卡帕系数，考虑随机一致性</li>
                    <li><strong>平衡准确率(Balanced Accuracy):</strong> 敏感性和特异性的平均值</li>
                </ul>
            </div>
            
            <div class="summary">
                <h2>💡 推荐建议</h2>
                <p>基于综合性能评估，我们推荐使用 <strong>CatBoost</strong> 模型进行后续任务。</p>
                <p>如果您对特定指标有具体要求，请参考详细性能指标表格选择最合适的模型。</p>
            </div>
        </div>
    </body>
    </html>
    