2025-08-07 09:38:13 - training_session_manager - INFO - 初始化训练会话管理器
2025-08-07 09:38:13 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-08-07 09:38:14 - data_exploration - INFO - 数据探索器初始化完成，输出目录: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\data_exploration
2025-08-07 09:38:14 - gui_data_exploration - INFO - 数据探索GUI组件初始化完成
2025-08-07 09:38:14 - GUI - INFO - GUI界面初始化完成
2025-08-07 09:38:32 - data_preprocessing - INFO - 成功加载 数据，特征维度: (197, 8)
2025-08-07 09:38:32 - data_preprocessing - INFO - 应用了 standard 特征缩放
2025-08-07 09:38:32 - hyperparameter_tuning - INFO - 开始对 DecisionTree 进行超参数调优，试验次数: 50
2025-08-07 09:38:32 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 4
2025-08-07 09:38:32 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-07 09:38:32 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-07 09:38:32 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 50, 'n_jobs': 4, 'timeout': 1800, 'callbacks': [<function tune_model.<locals>.early_stopping_callback at 0x000001DEA36265E0>]}
2025-08-07 09:38:32 - hyperparameter_tuning - INFO - Trial 1: 发现更好的得分 0.9262
2025-08-07 09:38:32 - hyperparameter_tuning - INFO - Trial 3: 发现更好的得分 0.9418
2025-08-07 09:38:32 - hyperparameter_tuning - INFO - Trial 5: 发现更好的得分 0.9505
2025-08-07 09:38:33 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-07 09:38:33 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9508
2025-08-07 09:38:33 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-07 09:38:33 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9508
2025-08-07 09:38:33 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-07 09:38:33 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9508
2025-08-07 09:38:33 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-07 09:38:33 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9508
2025-08-07 09:38:33 - hyperparameter_tuning - INFO - 模型 DecisionTree 的最佳参数: {'max_depth': 8, 'min_samples_split': 49, 'min_samples_leaf': 23, 'criterion': 'entropy', 'class_weight': 'balanced', 'max_features': None}
2025-08-07 09:38:33 - hyperparameter_tuning - INFO - 模型 DecisionTree 的最佳得分: 0.9508
2025-08-07 09:38:33 - hyperparameter_tuning - INFO - 实际执行试验次数: 18/50
2025-08-07 09:38:33 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-07 09:38:33 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-07 09:38:33 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-07 09:38:33 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\DecisionTree\optimization_history_20250807_093833.html
2025-08-07 09:38:33 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-07 09:38:33 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-07 09:38:33 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\DecisionTree\param_importances_20250807_093833.html
2025-08-07 09:38:33 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 1.04 秒
2025-08-07 09:38:33 - hyperparameter_tuning - INFO - 开始对 RandomForest 进行超参数调优，试验次数: 50
2025-08-07 09:38:33 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 4
2025-08-07 09:38:33 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-07 09:38:33 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-07 09:38:33 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 50, 'n_jobs': 4, 'timeout': 1800, 'callbacks': [<function tune_model.<locals>.early_stopping_callback at 0x000001DEA67F7E50>]}
2025-08-07 09:38:36 - hyperparameter_tuning - INFO - Trial 1: 发现更好的得分 0.9810
2025-08-07 09:38:37 - hyperparameter_tuning - INFO - Trial 0: 发现更好的得分 0.9825
2025-08-07 09:38:43 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-07 09:38:43 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9827
2025-08-07 09:38:44 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-07 09:38:44 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9827
2025-08-07 09:38:44 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-07 09:38:44 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9827
2025-08-07 09:38:44 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-07 09:38:44 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9827
2025-08-07 09:38:44 - hyperparameter_tuning - INFO - 模型 RandomForest 的最佳参数: {'n_estimators': 202, 'max_depth': 20, 'min_samples_split': 15, 'min_samples_leaf': 14, 'max_features': 'sqrt'}
2025-08-07 09:38:44 - hyperparameter_tuning - INFO - 模型 RandomForest 的最佳得分: 0.9827
2025-08-07 09:38:44 - hyperparameter_tuning - INFO - 实际执行试验次数: 15/50
2025-08-07 09:38:44 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-07 09:38:44 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-07 09:38:44 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-07 09:38:44 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\RandomForest\optimization_history_20250807_093844.html
2025-08-07 09:38:44 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-07 09:38:45 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-07 09:38:45 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\RandomForest\param_importances_20250807_093844.html
2025-08-07 09:38:45 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 11.34 秒
2025-08-07 09:38:45 - hyperparameter_tuning - INFO - 开始对 XGBoost 进行超参数调优，试验次数: 50
2025-08-07 09:38:45 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 4
2025-08-07 09:38:45 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-07 09:38:45 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-07 09:38:45 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 50, 'n_jobs': 4, 'timeout': 1800, 'callbacks': [<function tune_model.<locals>.early_stopping_callback at 0x000001DEA682D040>]}
2025-08-07 09:38:45 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-07 09:38:45 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-07 09:38:45 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-07 09:38:45 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-07 09:38:46 - hyperparameter_tuning - INFO - Trial 0: 发现更好的得分 0.9836
2025-08-07 09:38:46 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-07 09:38:46 - hyperparameter_tuning - INFO - Trial 2: 发现更好的得分 0.9853
2025-08-07 09:38:46 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-07 09:38:46 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-07 09:38:46 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-07 09:38:46 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-07 09:38:46 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-07 09:38:46 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-07 09:38:46 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-07 09:38:46 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-07 09:38:47 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-07 09:38:47 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-07 09:38:47 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-07 09:38:47 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9860
2025-08-07 09:38:47 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-07 09:38:47 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9860
2025-08-07 09:38:47 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-07 09:38:47 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9860
2025-08-07 09:38:47 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-07 09:38:47 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9860
2025-08-07 09:38:47 - hyperparameter_tuning - INFO - 模型 XGBoost 的最佳参数: {'n_estimators': 220, 'max_depth': 4, 'learning_rate': 0.2033575573412162, 'subsample': 0.8316145929182917, 'colsample_bytree': 0.5838589852135501}
2025-08-07 09:38:47 - hyperparameter_tuning - INFO - 模型 XGBoost 的最佳得分: 0.9860
2025-08-07 09:38:47 - hyperparameter_tuning - INFO - 实际执行试验次数: 15/50
2025-08-07 09:38:47 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-07 09:38:47 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-07 09:38:47 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-07 09:38:48 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\XGBoost\optimization_history_20250807_093847.html
2025-08-07 09:38:48 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-07 09:38:48 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-07 09:38:48 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\XGBoost\param_importances_20250807_093848.html
2025-08-07 09:38:48 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 3.09 秒
2025-08-07 09:38:48 - hyperparameter_tuning - INFO - 开始对 LightGBM 进行超参数调优，试验次数: 50
2025-08-07 09:38:48 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 4
2025-08-07 09:38:48 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-07 09:38:48 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-07 09:38:48 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 50, 'n_jobs': 4, 'timeout': 1800, 'callbacks': [<function tune_model.<locals>.early_stopping_callback at 0x000001DEA682DC10>]}
2025-08-07 09:38:49 - hyperparameter_tuning - INFO - Trial 0: 发现更好的得分 0.9744
2025-08-07 09:38:50 - hyperparameter_tuning - INFO - Trial 4: 发现更好的得分 0.9845
2025-08-07 09:38:50 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-07 09:38:50 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9845
2025-08-07 09:38:51 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-07 09:38:51 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9845
2025-08-07 09:38:51 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-07 09:38:51 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9845
2025-08-07 09:38:51 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-07 09:38:51 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9845
2025-08-07 09:38:51 - hyperparameter_tuning - INFO - 模型 LightGBM 的最佳参数: {'n_estimators': 88, 'max_depth': 3, 'learning_rate': 0.12595315176924943, 'feature_fraction': 0.5192324241244926, 'bagging_fraction': 0.9442482307928439}
2025-08-07 09:38:51 - hyperparameter_tuning - INFO - 模型 LightGBM 的最佳得分: 0.9845
2025-08-07 09:38:51 - hyperparameter_tuning - INFO - 实际执行试验次数: 15/50
2025-08-07 09:38:51 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-07 09:38:51 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-07 09:38:51 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-07 09:38:51 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\LightGBM\optimization_history_20250807_093851.html
2025-08-07 09:38:51 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-07 09:38:51 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-07 09:38:51 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\LightGBM\param_importances_20250807_093851.html
2025-08-07 09:38:51 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 3.30 秒
2025-08-07 09:38:51 - hyperparameter_tuning - INFO - 开始对 CatBoost 进行超参数调优，试验次数: 50
2025-08-07 09:38:51 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 4
2025-08-07 09:38:51 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-07 09:38:51 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-07 09:38:51 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 50, 'n_jobs': 4, 'timeout': 1800, 'callbacks': [<function tune_model.<locals>.early_stopping_callback at 0x000001DEA682D040>]}
2025-08-07 09:38:51 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-07 09:38:51 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-07 09:38:51 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-07 09:38:51 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-07 09:38:54 - hyperparameter_tuning - INFO - Trial 0: 发现更好的得分 0.9771
2025-08-07 09:38:54 - hyperparameter_tuning - ERROR - 超参数调优过程中出错: 
All the 5 fits failed.
It is very likely that your model is misconfigured.
You can try to debug the error by setting error_score='raise'.

Below are more details about the failures:
--------------------------------------------------------------------------------
1 <USER> <GROUP> with the following error:
Traceback (most recent call last):
  File "D:\anaconda\envs\multi_model\lib\site-packages\sklearn\model_selection\_validation.py", line 866, in _fit_and_score
    estimator.fit(X_train, y_train, **fit_params)
  File "D:\anaconda\envs\multi_model\lib\site-packages\catboost\core.py", line 5245, in fit
    self._fit(X, y, cat_features, text_features, embedding_features, None, graph, sample_weight, None, None, None, None, baseline, use_best_model,
  File "D:\anaconda\envs\multi_model\lib\site-packages\catboost\core.py", line 2410, in _fit
    self._train(
  File "D:\anaconda\envs\multi_model\lib\site-packages\catboost\core.py", line 1790, in _train
    self._object._train(train_pool, test_pool, params, allow_clear_pool, init_model._object if init_model else None)
  File "_catboost.pyx", line 5017, in _catboost._CatBoost._train
  File "_catboost.pyx", line 5066, in _catboost._CatBoost._train
_catboost.CatBoostError: catboost/cuda/cuda_lib/devices_provider.h:190: Error: device already requested 0

--------------------------------------------------------------------------------
4 <USER> <GROUP> with the following error:
Traceback (most recent call last):
  File "D:\anaconda\envs\multi_model\lib\site-packages\sklearn\model_selection\_validation.py", line 866, in _fit_and_score
    estimator.fit(X_train, y_train, **fit_params)
  File "D:\anaconda\envs\multi_model\lib\site-packages\catboost\core.py", line 5245, in fit
    self._fit(X, y, cat_features, text_features, embedding_features, None, graph, sample_weight, None, None, None, None, baseline, use_best_model,
  File "D:\anaconda\envs\multi_model\lib\site-packages\catboost\core.py", line 2410, in _fit
    self._train(
  File "D:\anaconda\envs\multi_model\lib\site-packages\catboost\core.py", line 1790, in _train
    self._object._train(train_pool, test_pool, params, allow_clear_pool, init_model._object if init_model else None)
  File "_catboost.pyx", line 5017, in _catboost._CatBoost._train
  File "_catboost.pyx", line 5066, in _catboost._CatBoost._train
_catboost.CatBoostError: catboost/cuda/cuda_lib/cuda_manager.cpp:201: Condition violated: `State == nullptr'

2025-08-07 09:38:54 - hyperparameter_tuning - INFO - 开始对 Logistic 进行超参数调优，试验次数: 50
2025-08-07 09:38:54 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 4
2025-08-07 09:38:54 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-07 09:38:54 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-07 09:38:54 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 50, 'n_jobs': 4, 'timeout': 1800, 'callbacks': [<function tune_model.<locals>.early_stopping_callback at 0x000001DEA7CFE820>]}
2025-08-07 09:38:54 - hyperparameter_tuning - INFO - Trial 0: 发现更好的得分 0.9664
2025-08-07 09:38:54 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-07 09:38:54 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-07 09:38:54 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9672
2025-08-07 09:38:54 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9672
2025-08-07 09:38:55 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-07 09:38:55 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9672
2025-08-07 09:38:55 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-07 09:38:55 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9672
2025-08-07 09:38:55 - hyperparameter_tuning - INFO - 模型 Logistic 的最佳参数: {'C': 5.2514855664547895, 'solver': 'liblinear'}
2025-08-07 09:38:55 - hyperparameter_tuning - INFO - 模型 Logistic 的最佳得分: 0.9672
2025-08-07 09:38:55 - hyperparameter_tuning - INFO - 实际执行试验次数: 14/50
2025-08-07 09:38:55 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-07 09:38:55 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-07 09:38:55 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-07 09:38:55 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\Logistic\optimization_history_20250807_093855.html
2025-08-07 09:38:55 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-07 09:38:55 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-07 09:38:55 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\Logistic\param_importances_20250807_093855.html
2025-08-07 09:38:55 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 0.76 秒
2025-08-07 09:38:55 - hyperparameter_tuning - INFO - 开始对 SVM 进行超参数调优，试验次数: 50
2025-08-07 09:38:55 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 4
2025-08-07 09:38:55 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-07 09:38:55 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-07 09:38:55 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 50, 'n_jobs': 4, 'timeout': 1800, 'callbacks': [<function tune_model.<locals>.early_stopping_callback at 0x000001DEA693AD30>]}
2025-08-07 09:38:55 - hyperparameter_tuning - INFO - Trial 0: 发现更好的得分 0.9887
2025-08-07 09:38:55 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-07 09:38:55 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9895
2025-08-07 09:38:55 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-07 09:38:55 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9895
2025-08-07 09:38:55 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-07 09:38:55 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9895
2025-08-07 09:38:55 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-07 09:38:55 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9895
2025-08-07 09:38:55 - hyperparameter_tuning - INFO - 模型 SVM 的最佳参数: {'C': 5.932786617483413, 'kernel': 'rbf'}
2025-08-07 09:38:55 - hyperparameter_tuning - INFO - 模型 SVM 的最佳得分: 0.9895
2025-08-07 09:38:55 - hyperparameter_tuning - INFO - 实际执行试验次数: 14/50
2025-08-07 09:38:55 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-07 09:38:55 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-07 09:38:55 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-07 09:38:55 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\SVM\optimization_history_20250807_093855.html
2025-08-07 09:38:56 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-07 09:38:56 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-07 09:38:56 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\SVM\param_importances_20250807_093856.html
2025-08-07 09:38:56 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 0.73 秒
2025-08-07 09:38:56 - hyperparameter_tuning - INFO - 开始对 KNN 进行超参数调优，试验次数: 50
2025-08-07 09:38:56 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 4
2025-08-07 09:38:56 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-07 09:38:56 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-07 09:38:56 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 50, 'n_jobs': 4, 'timeout': 1800, 'callbacks': [<function tune_model.<locals>.early_stopping_callback at 0x000001DEA693AAF0>]}
2025-08-07 09:38:56 - hyperparameter_tuning - INFO - Trial 3: 发现更好的得分 0.9605
2025-08-07 09:38:56 - hyperparameter_tuning - INFO - Trial 1: 发现更好的得分 0.9744
2025-08-07 09:38:56 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-07 09:38:56 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-07 09:38:56 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9744
2025-08-07 09:38:56 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9744
2025-08-07 09:38:56 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-07 09:38:56 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9744
2025-08-07 09:38:56 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-07 09:38:56 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9744
2025-08-07 09:38:56 - hyperparameter_tuning - INFO - 模型 KNN 的最佳参数: {'n_neighbors': 8}
2025-08-07 09:38:56 - hyperparameter_tuning - INFO - 模型 KNN 的最佳得分: 0.9744
2025-08-07 09:38:56 - hyperparameter_tuning - INFO - 实际执行试验次数: 15/50
2025-08-07 09:38:56 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-07 09:38:56 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-07 09:38:56 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-07 09:38:56 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\KNN\optimization_history_20250807_093856.html
2025-08-07 09:38:56 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-07 09:38:57 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-07 09:38:57 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\KNN\param_importances_20250807_093856.html
2025-08-07 09:38:57 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 0.79 秒
2025-08-07 09:38:57 - hyperparameter_tuning - INFO - 开始对 NaiveBayes 进行超参数调优，试验次数: 50
2025-08-07 09:38:57 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 4
2025-08-07 09:38:57 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-07 09:38:57 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-07 09:38:57 - hyperparameter_tuning - INFO - NaiveBayes模型无需调参
2025-08-07 09:38:57 - hyperparameter_tuning - INFO - 开始对 NeuralNet 进行超参数调优，试验次数: 50
2025-08-07 09:38:57 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 4
2025-08-07 09:38:57 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-07 09:38:57 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-07 09:38:57 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 50, 'n_jobs': 4, 'timeout': 1800, 'callbacks': [<function tune_model.<locals>.early_stopping_callback at 0x000001DEA6710670>]}
2025-08-07 09:38:59 - hyperparameter_tuning - INFO - Trial 2: 发现更好的得分 0.9806
2025-08-07 09:39:08 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-07 09:39:08 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9815
2025-08-07 09:39:08 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-07 09:39:08 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9815
2025-08-07 09:39:09 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-07 09:39:09 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9815
2025-08-07 09:39:09 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-07 09:39:09 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9815
2025-08-07 09:39:09 - hyperparameter_tuning - INFO - 模型 NeuralNet 的最佳参数: {'hidden_layer_sizes': (50, 50), 'alpha': 0.009037215216917902}
2025-08-07 09:39:09 - hyperparameter_tuning - INFO - 模型 NeuralNet 的最佳得分: 0.9815
2025-08-07 09:39:09 - hyperparameter_tuning - INFO - 实际执行试验次数: 14/50
2025-08-07 09:39:09 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-07 09:39:09 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-07 09:39:09 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-07 09:39:09 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\NeuralNet\optimization_history_20250807_093909.html
2025-08-07 09:39:09 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-07 09:39:09 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-07 09:39:09 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\NeuralNet\param_importances_20250807_093909.html
2025-08-07 09:39:09 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 12.73 秒
2025-08-07 09:39:48 - training_session_manager - INFO - 创建会话目录结构: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250807_093948
2025-08-07 09:39:48 - training_session_manager - INFO - 创建训练会话: 训练_nodule2_20250807_093948 (ID: 20250807_093948)
2025-08-07 09:39:48 - training_session_manager - INFO - 创建新会话: 训练_nodule2_20250807_093948
2025-08-07 09:39:48 - session_utils - INFO - 创建新会话: 训练_nodule2_20250807_093948 (ID: 20250807_093948)
2025-08-07 09:39:48 - data_preprocessing - INFO - 成功加载 数据，特征维度: (197, 8)
2025-08-07 09:39:48 - data_preprocessing - INFO - 应用了 standard 特征缩放
2025-08-07 09:39:48 - model_training - INFO - 模型名称: Decision Tree
2025-08-07 09:39:48 - model_training - INFO - 准确率: 0.8000
2025-08-07 09:39:48 - model_training - INFO - AUC: 0.8951
2025-08-07 09:39:48 - model_training - INFO - 混淆矩阵:
2025-08-07 09:39:48 - model_training - INFO - 
[[20  3]
 [ 5 12]]
2025-08-07 09:39:48 - model_training - INFO - 
分类报告:
2025-08-07 09:39:48 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.80      0.87      0.83        23
           1       0.80      0.71      0.75        17

    accuracy                           0.80        40
   macro avg       0.80      0.79      0.79        40
weighted avg       0.80      0.80      0.80        40

2025-08-07 09:39:48 - model_training - INFO - 训练时间: 0.01 秒
2025-08-07 09:39:48 - model_training - INFO - 模型 DecisionTree 性能: 准确率=0.8000
2025-08-07 09:39:48 - training_session_manager - INFO - 保存模型 DecisionTree 到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250807_093948\models\DecisionTree_single_093948.joblib
2025-08-07 09:39:48 - model_training - INFO - 模型 DecisionTree 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250807_093948\models\DecisionTree_single_093948.joblib
2025-08-07 09:39:48 - model_training - INFO - 模型 DecisionTree 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\DecisionTree_results.joblib
2025-08-07 09:39:48 - model_training - INFO - 模型名称: Random Forest
2025-08-07 09:39:48 - model_training - INFO - 准确率: 0.9000
2025-08-07 09:39:48 - model_training - INFO - AUC: 0.9386
2025-08-07 09:39:48 - model_training - INFO - 混淆矩阵:
2025-08-07 09:39:48 - model_training - INFO - 
[[22  1]
 [ 3 14]]
2025-08-07 09:39:48 - model_training - INFO - 
分类报告:
2025-08-07 09:39:48 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.88      0.96      0.92        23
           1       0.93      0.82      0.88        17

    accuracy                           0.90        40
   macro avg       0.91      0.89      0.90        40
weighted avg       0.90      0.90      0.90        40

2025-08-07 09:39:48 - model_training - INFO - 训练时间: 0.08 秒
2025-08-07 09:39:48 - model_training - INFO - 模型 RandomForest 性能: 准确率=0.9000
2025-08-07 09:39:48 - training_session_manager - INFO - 保存模型 RandomForest 到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250807_093948\models\RandomForest_single_093948.joblib
2025-08-07 09:39:48 - model_training - INFO - 模型 RandomForest 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250807_093948\models\RandomForest_single_093948.joblib
2025-08-07 09:39:48 - model_training - INFO - 模型 RandomForest 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\RandomForest_results.joblib
2025-08-07 09:39:48 - model_training - INFO - 模型名称: XGBoost
2025-08-07 09:39:48 - model_training - INFO - 准确率: 0.8750
2025-08-07 09:39:48 - model_training - INFO - AUC: 0.9668
2025-08-07 09:39:48 - model_training - INFO - 混淆矩阵:
2025-08-07 09:39:48 - model_training - INFO - 
[[21  2]
 [ 3 14]]
2025-08-07 09:39:48 - model_training - INFO - 
分类报告:
2025-08-07 09:39:48 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.88      0.91      0.89        23
           1       0.88      0.82      0.85        17

    accuracy                           0.88        40
   macro avg       0.88      0.87      0.87        40
weighted avg       0.88      0.88      0.87        40

2025-08-07 09:39:48 - model_training - INFO - 训练时间: 0.04 秒
2025-08-07 09:39:48 - model_training - INFO - 模型 XGBoost 性能: 准确率=0.8750
2025-08-07 09:39:48 - training_session_manager - INFO - 保存模型 XGBoost 到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250807_093948\models\XGBoost_single_093948.joblib
2025-08-07 09:39:48 - model_training - INFO - 模型 XGBoost 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250807_093948\models\XGBoost_single_093948.joblib
2025-08-07 09:39:48 - model_training - INFO - 模型 XGBoost 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\XGBoost_results.joblib
2025-08-07 09:39:48 - model_training - INFO - 模型名称: LightGBM
2025-08-07 09:39:48 - model_training - INFO - 准确率: 0.8750
2025-08-07 09:39:48 - model_training - INFO - AUC: 0.9463
2025-08-07 09:39:48 - model_training - INFO - 混淆矩阵:
2025-08-07 09:39:48 - model_training - INFO - 
[[21  2]
 [ 3 14]]
2025-08-07 09:39:48 - model_training - INFO - 
分类报告:
2025-08-07 09:39:48 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.88      0.91      0.89        23
           1       0.88      0.82      0.85        17

    accuracy                           0.88        40
   macro avg       0.88      0.87      0.87        40
weighted avg       0.88      0.88      0.87        40

2025-08-07 09:39:48 - model_training - INFO - 训练时间: 0.04 秒
2025-08-07 09:39:48 - model_training - INFO - 模型 LightGBM 性能: 准确率=0.8750
2025-08-07 09:39:48 - training_session_manager - INFO - 保存模型 LightGBM 到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250807_093948\models\LightGBM_single_093948.joblib
2025-08-07 09:39:48 - model_training - INFO - 模型 LightGBM 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250807_093948\models\LightGBM_single_093948.joblib
2025-08-07 09:39:48 - model_training - INFO - 模型 LightGBM 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\LightGBM_results.joblib
2025-08-07 09:39:49 - model_training - INFO - 模型名称: CatBoost
2025-08-07 09:39:49 - model_training - INFO - 准确率: 0.9000
2025-08-07 09:39:49 - model_training - INFO - AUC: 0.9591
2025-08-07 09:39:49 - model_training - INFO - 混淆矩阵:
2025-08-07 09:39:49 - model_training - INFO - 
[[22  1]
 [ 3 14]]
2025-08-07 09:39:49 - model_training - INFO - 
分类报告:
2025-08-07 09:39:49 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.88      0.96      0.92        23
           1       0.93      0.82      0.88        17

    accuracy                           0.90        40
   macro avg       0.91      0.89      0.90        40
weighted avg       0.90      0.90      0.90        40

2025-08-07 09:39:49 - model_training - INFO - 训练时间: 1.03 秒
2025-08-07 09:39:50 - model_training - INFO - 模型 CatBoost 性能: 准确率=0.9000
2025-08-07 09:39:50 - training_session_manager - INFO - 保存模型 CatBoost 到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250807_093948\models\CatBoost_single_093950.joblib
2025-08-07 09:39:50 - model_training - INFO - 模型 CatBoost 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250807_093948\models\CatBoost_single_093950.joblib
2025-08-07 09:39:50 - model_training - INFO - 模型 CatBoost 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\CatBoost_results.joblib
2025-08-07 09:39:50 - model_training - INFO - 模型名称: Logistic Regression
2025-08-07 09:39:50 - model_training - INFO - 准确率: 0.8500
2025-08-07 09:39:50 - model_training - INFO - AUC: 0.9284
2025-08-07 09:39:50 - model_training - INFO - 混淆矩阵:
2025-08-07 09:39:50 - model_training - INFO - 
[[21  2]
 [ 4 13]]
2025-08-07 09:39:50 - model_training - INFO - 
分类报告:
2025-08-07 09:39:50 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.84      0.91      0.88        23
           1       0.87      0.76      0.81        17

    accuracy                           0.85        40
   macro avg       0.85      0.84      0.84        40
weighted avg       0.85      0.85      0.85        40

2025-08-07 09:39:50 - model_training - INFO - 训练时间: 0.01 秒
2025-08-07 09:39:50 - model_training - INFO - 模型 Logistic 性能: 准确率=0.8500
2025-08-07 09:39:50 - training_session_manager - INFO - 保存模型 Logistic 到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250807_093948\models\Logistic_single_093950.joblib
2025-08-07 09:39:50 - model_training - INFO - 模型 Logistic 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250807_093948\models\Logistic_single_093950.joblib
2025-08-07 09:39:50 - model_training - INFO - 模型 Logistic 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\Logistic_results.joblib
2025-08-07 09:39:50 - model_training - INFO - 模型名称: SVM
2025-08-07 09:39:50 - model_training - INFO - 准确率: 0.8000
2025-08-07 09:39:50 - model_training - INFO - AUC: 0.9207
2025-08-07 09:39:50 - model_training - INFO - 混淆矩阵:
2025-08-07 09:39:50 - model_training - INFO - 
[[21  2]
 [ 6 11]]
2025-08-07 09:39:50 - model_training - INFO - 
分类报告:
2025-08-07 09:39:50 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.78      0.91      0.84        23
           1       0.85      0.65      0.73        17

    accuracy                           0.80        40
   macro avg       0.81      0.78      0.79        40
weighted avg       0.81      0.80      0.79        40

2025-08-07 09:39:50 - model_training - INFO - 训练时间: 0.01 秒
2025-08-07 09:39:50 - model_training - INFO - 模型 SVM 性能: 准确率=0.8000
2025-08-07 09:39:50 - training_session_manager - INFO - 保存模型 SVM 到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250807_093948\models\SVM_single_093950.joblib
2025-08-07 09:39:50 - model_training - INFO - 模型 SVM 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250807_093948\models\SVM_single_093950.joblib
2025-08-07 09:39:50 - model_training - INFO - 模型 SVM 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\SVM_results.joblib
2025-08-07 09:39:50 - model_training - INFO - 模型名称: KNN
2025-08-07 09:39:50 - model_training - INFO - 准确率: 0.8750
2025-08-07 09:39:50 - model_training - INFO - AUC: 0.9322
2025-08-07 09:39:50 - model_training - INFO - 混淆矩阵:
2025-08-07 09:39:50 - model_training - INFO - 
[[21  2]
 [ 3 14]]
2025-08-07 09:39:50 - model_training - INFO - 
分类报告:
2025-08-07 09:39:50 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.88      0.91      0.89        23
           1       0.88      0.82      0.85        17

    accuracy                           0.88        40
   macro avg       0.88      0.87      0.87        40
weighted avg       0.88      0.88      0.87        40

2025-08-07 09:39:50 - model_training - INFO - 训练时间: 0.01 秒
2025-08-07 09:39:50 - model_training - INFO - 模型 KNN 性能: 准确率=0.8750
2025-08-07 09:39:50 - training_session_manager - INFO - 保存模型 KNN 到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250807_093948\models\KNN_single_093950.joblib
2025-08-07 09:39:50 - model_training - INFO - 模型 KNN 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250807_093948\models\KNN_single_093950.joblib
2025-08-07 09:39:50 - model_training - INFO - 模型 KNN 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\KNN_results.joblib
2025-08-07 09:39:50 - model_training - INFO - 模型名称: Naive Bayes
2025-08-07 09:39:50 - model_training - INFO - 准确率: 0.8750
2025-08-07 09:39:50 - model_training - INFO - AUC: 0.8977
2025-08-07 09:39:50 - model_training - INFO - 混淆矩阵:
2025-08-07 09:39:50 - model_training - INFO - 
[[22  1]
 [ 4 13]]
2025-08-07 09:39:50 - model_training - INFO - 
分类报告:
2025-08-07 09:39:50 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.85      0.96      0.90        23
           1       0.93      0.76      0.84        17

    accuracy                           0.88        40
   macro avg       0.89      0.86      0.87        40
weighted avg       0.88      0.88      0.87        40

2025-08-07 09:39:50 - model_training - INFO - 训练时间: 0.01 秒
2025-08-07 09:39:50 - model_training - INFO - 模型 NaiveBayes 性能: 准确率=0.8750
2025-08-07 09:39:50 - training_session_manager - INFO - 保存模型 NaiveBayes 到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250807_093948\models\NaiveBayes_single_093950.joblib
2025-08-07 09:39:50 - model_training - INFO - 模型 NaiveBayes 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250807_093948\models\NaiveBayes_single_093950.joblib
2025-08-07 09:39:50 - model_training - INFO - 模型 NaiveBayes 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\NaiveBayes_results.joblib
2025-08-07 09:39:50 - model_training - INFO - 模型名称: Neural Network
2025-08-07 09:39:50 - model_training - INFO - 准确率: 0.8750
2025-08-07 09:39:50 - model_training - INFO - AUC: 0.9591
2025-08-07 09:39:50 - model_training - INFO - 混淆矩阵:
2025-08-07 09:39:50 - model_training - INFO - 
[[21  2]
 [ 3 14]]
2025-08-07 09:39:50 - model_training - INFO - 
分类报告:
2025-08-07 09:39:50 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.88      0.91      0.89        23
           1       0.88      0.82      0.85        17

    accuracy                           0.88        40
   macro avg       0.88      0.87      0.87        40
weighted avg       0.88      0.88      0.87        40

2025-08-07 09:39:50 - model_training - INFO - 训练时间: 0.24 秒
2025-08-07 09:39:50 - model_training - INFO - 模型 NeuralNet 性能: 准确率=0.8750
2025-08-07 09:39:50 - training_session_manager - INFO - 保存模型 NeuralNet 到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250807_093948\models\NeuralNet_single_093950.joblib
2025-08-07 09:39:50 - model_training - INFO - 模型 NeuralNet 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250807_093948\models\NeuralNet_single_093950.joblib
2025-08-07 09:39:50 - model_training - INFO - 模型 NeuralNet 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\NeuralNet_results.joblib
